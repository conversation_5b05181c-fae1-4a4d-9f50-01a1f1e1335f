{"indexes": [{"collectionGroup": "employees", "queryScope": "COLLECTION", "fields": [{"fieldPath": "companyId", "order": "ASCENDING"}, {"fieldPath": "isDeleted", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}]}, {"collectionGroup": "employees", "queryScope": "COLLECTION", "fields": [{"fieldPath": "companyId", "order": "ASCENDING"}, {"fieldPath": "isDeleted", "order": "ASCENDING"}, {"fieldPath": "department", "order": "ASCENDING"}]}, {"collectionGroup": "employees", "queryScope": "COLLECTION", "fields": [{"fieldPath": "companyId", "order": "ASCENDING"}, {"fieldPath": "isDeleted", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "absences", "queryScope": "COLLECTION", "fields": [{"fieldPath": "companyId", "order": "ASCENDING"}, {"fieldPath": "employeeId", "order": "ASCENDING"}, {"fieldPath": "startDate", "order": "DESCENDING"}]}, {"collectionGroup": "absences", "queryScope": "COLLECTION", "fields": [{"fieldPath": "companyId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "overtime", "queryScope": "COLLECTION", "fields": [{"fieldPath": "companyId", "order": "ASCENDING"}, {"fieldPath": "employeeId", "order": "ASCENDING"}, {"fieldPath": "date", "order": "DESCENDING"}]}, {"collectionGroup": "overtime", "queryScope": "COLLECTION", "fields": [{"fieldPath": "companyId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "users", "queryScope": "COLLECTION", "fields": [{"fieldPath": "companyId", "order": "ASCENDING"}, {"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "role", "order": "ASCENDING"}]}, {"collectionGroup": "departments", "queryScope": "COLLECTION", "fields": [{"fieldPath": "companyId", "order": "ASCENDING"}, {"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "order", "order": "ASCENDING"}]}, {"collectionGroup": "departments", "queryScope": "COLLECTION", "fields": [{"fieldPath": "companyId", "order": "ASCENDING"}, {"fieldPath": "order", "order": "DESCENDING"}]}, {"collectionGroup": "services", "queryScope": "COLLECTION", "fields": [{"fieldPath": "companyId", "order": "ASCENDING"}, {"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "order", "order": "ASCENDING"}]}, {"collectionGroup": "services", "queryScope": "COLLECTION", "fields": [{"fieldPath": "companyId", "order": "ASCENDING"}, {"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "departmentId", "order": "ASCENDING"}, {"fieldPath": "order", "order": "ASCENDING"}]}, {"collectionGroup": "positions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "companyId", "order": "ASCENDING"}, {"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "order", "order": "ASCENDING"}]}, {"collectionGroup": "positions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "companyId", "order": "ASCENDING"}, {"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "departmentId", "order": "ASCENDING"}, {"fieldPath": "order", "order": "ASCENDING"}]}, {"collectionGroup": "positions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "companyId", "order": "ASCENDING"}, {"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "serviceId", "order": "ASCENDING"}, {"fieldPath": "order", "order": "ASCENDING"}]}], "fieldOverrides": []}