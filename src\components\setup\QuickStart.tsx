"use client";

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, XCircle, Loader2, Rocket, Database, Users, Building2 } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { initializeCompany, createAdminUser } from '@/lib/firestore';
import { useFirestore } from '@/context/FirestoreContext';

export function QuickStart() {
  const [step, setStep] = useState<'start' | 'creating' | 'complete'>('start');
  const [isLoading, setIsLoading] = useState(false);
  const [companyId, setCompanyId] = useState<string | null>(null);
  const [adminId, setAdminId] = useState<string | null>(null);
  const { toast } = useToast();
  const { setCompany, setUser } = useFirestore();

  const createDemoSetup = async () => {
    setIsLoading(true);
    setStep('creating');

    try {
      // Step 1: Create demo company
      toast({
        title: "Creating Demo Company",
        description: "Setting up WePaie Demo Company..."
      });

      const company = await initializeCompany({
        name: 'WePaie Demo Company',
        email: '<EMAIL>',
        phone: '+1 (555) 123-DEMO',
        address: '123 Demo Street, Demo City, DC 12345'
      });

      console.log('Demo company created:', company);

      setCompanyId(company.id);
      setCompany(company);

      // Step 2: Create demo admin user
      toast({
        title: "Creating Admin User",
        description: "Setting up demo admin account..."
      });

      const admin = await createAdminUser(company.id, {
        firstName: 'Demo',
        lastName: 'Admin',
        email: '<EMAIL>',
        name: 'Demo Admin'
      });

      setAdminId(admin.id);
      setUser(admin);

      toast({
        title: "Setup Complete!",
        description: "Your demo WePaie environment is ready to use."
      });

      setStep('complete');
    } catch (error: any) {
      console.error('Demo setup error:', error);
      toast({
        title: "Setup Failed",
        description: error.message || "Failed to create demo setup. Please try again.",
        variant: "destructive"
      });
      setStep('start');
    } finally {
      setIsLoading(false);
    }
  };

  if (step === 'complete') {
    return (
      <Card className="w-full max-w-2xl mx-auto">
        <CardHeader className="text-center">
          <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
          <CardTitle className="font-headline text-2xl">Demo Setup Complete!</CardTitle>
          <CardDescription>
            Your WePaie demo environment is ready. You can now explore all features with sample data.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="p-4 border rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <Building2 className="h-5 w-5 text-blue-500" />
                <h4 className="font-medium">Demo Company</h4>
              </div>
              <p className="text-sm text-muted-foreground">WePaie Demo Company</p>
              {companyId && (
                <p className="text-xs font-mono text-muted-foreground mt-1">ID: {companyId}</p>
              )}
            </div>
            
            <div className="p-4 border rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <Users className="h-5 w-5 text-green-500" />
                <h4 className="font-medium">Admin User</h4>
              </div>
              <p className="text-sm text-muted-foreground">Demo Admin</p>
              {adminId && (
                <p className="text-xs font-mono text-muted-foreground mt-1">ID: {adminId}</p>
              )}
            </div>
          </div>

          <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <h4 className="font-medium text-blue-800 mb-2">What's Next?</h4>
            <ul className="text-sm text-blue-700 space-y-1">
              <li>• Navigate to the main application to see the employee management interface</li>
              <li>• Add sample employees using the employee form</li>
              <li>• Explore time management features (absences and overtime)</li>
              <li>• Test the multi-language support</li>
              <li>• Check the Firebase Console to see your data</li>
            </ul>
          </div>

          <div className="flex gap-2">
            <Button 
              onClick={() => window.location.href = '/'} 
              className="flex-1"
            >
              <Rocket className="mr-2 h-4 w-4" />
              Go to Application
            </Button>
            <Button 
              variant="outline" 
              onClick={() => window.open('https://console.firebase.google.com/project/wepaie/firestore', '_blank')}
            >
              <Database className="mr-2 h-4 w-4" />
              View in Firebase
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (step === 'creating') {
    return (
      <Card className="w-full max-w-md mx-auto">
        <CardHeader className="text-center">
          <Loader2 className="h-12 w-12 animate-spin text-blue-500 mx-auto mb-4" />
          <CardTitle className="font-headline text-2xl">Setting Up Demo</CardTitle>
          <CardDescription>
            Creating your demo company and admin user...
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex items-center gap-3">
              <div className="h-2 w-2 bg-blue-500 rounded-full animate-pulse"></div>
              <span className="text-sm">Creating demo company...</span>
            </div>
            <div className="flex items-center gap-3">
              <div className="h-2 w-2 bg-gray-300 rounded-full"></div>
              <span className="text-sm text-muted-foreground">Setting up admin user...</span>
            </div>
            <div className="flex items-center gap-3">
              <div className="h-2 w-2 bg-gray-300 rounded-full"></div>
              <span className="text-sm text-muted-foreground">Finalizing setup...</span>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader className="text-center">
        <Rocket className="h-12 w-12 text-blue-500 mx-auto mb-4" />
        <CardTitle className="font-headline text-2xl">Quick Start</CardTitle>
        <CardDescription>
          Get started with WePaie in seconds by creating a demo company with sample data.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-3">
          <div className="flex items-center gap-3 p-3 border rounded-lg">
            <Building2 className="h-5 w-5 text-blue-500" />
            <div>
              <h4 className="font-medium">Demo Company</h4>
              <p className="text-sm text-muted-foreground">Creates a sample company with settings</p>
            </div>
          </div>
          
          <div className="flex items-center gap-3 p-3 border rounded-lg">
            <Users className="h-5 w-5 text-green-500" />
            <div>
              <h4 className="font-medium">Admin User</h4>
              <p className="text-sm text-muted-foreground">Sets up an admin account with full permissions</p>
            </div>
          </div>
          
          <div className="flex items-center gap-3 p-3 border rounded-lg">
            <Database className="h-5 w-5 text-purple-500" />
            <div>
              <h4 className="font-medium">Firebase Integration</h4>
              <p className="text-sm text-muted-foreground">Tests and verifies your Firebase connection</p>
            </div>
          </div>
        </div>

        <Button 
          onClick={createDemoSetup} 
          disabled={isLoading}
          className="w-full"
          size="lg"
        >
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Creating Demo...
            </>
          ) : (
            <>
              <Rocket className="mr-2 h-4 w-4" />
              Create Demo Setup
            </>
          )}
        </Button>

        <p className="text-xs text-muted-foreground text-center">
          This will create test data in your Firebase project. You can delete it later if needed.
        </p>
      </CardContent>
    </Card>
  );
}
