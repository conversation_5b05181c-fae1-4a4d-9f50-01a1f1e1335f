"use client";

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, XCircle, Loader2, Shield, Database, AlertTriangle } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { db } from '@/lib/firebase';
import { collection, doc, setDoc, getDoc, deleteDoc } from 'firebase/firestore';
import { initializeCompany, companyService } from '@/lib/firestore';

interface TestResult {
  name: string;
  status: 'pending' | 'success' | 'error' | 'warning';
  message: string;
  details?: string;
}

export function ComprehensiveTest() {
  const [tests, setTests] = useState<TestResult[]>([
    { name: 'Security Rules - Unauthorized Read', status: 'pending', message: 'Testing unauthorized read access...' },
    { name: 'Security Rules - Unauthorized Write', status: 'pending', message: 'Testing unauthorized write access...' },
    { name: 'Company Service - Document Reference', status: 'pending', message: 'Testing company creation...' },
    { name: 'Company Service - CRUD Operations', status: 'pending', message: 'Testing company operations...' },
  ]);
  
  const [isRunning, setIsRunning] = useState(false);
  const [testCompanyId, setTestCompanyId] = useState<string | null>(null);
  const { toast } = useToast();

  const updateTest = (index: number, updates: Partial<TestResult>) => {
    setTests(prev => prev.map((test, i) => i === index ? { ...test, ...updates } : test));
  };

  const runComprehensiveTests = async () => {
    setIsRunning(true);
    
    try {
      // Test 1: Security Rules - Unauthorized Read
      updateTest(0, { status: 'pending', message: 'Testing unauthorized read access...' });
      
      try {
        const unauthorizedRef = doc(collection(db, 'companies'), 'test-unauthorized-read');
        await getDoc(unauthorizedRef);
        
        updateTest(0, { 
          status: 'error', 
          message: 'Security vulnerability detected',
          details: 'Unauthorized read access was allowed - security rules may not be active'
        });
      } catch (error: any) {
        if (error.code === 'permission-denied') {
          updateTest(0, { 
            status: 'success', 
            message: 'Unauthorized read properly blocked',
            details: 'Security rules are working correctly'
          });
        } else {
          updateTest(0, { 
            status: 'warning', 
            message: 'Unexpected error during read test',
            details: error.message
          });
        }
      }

      // Test 2: Security Rules - Unauthorized Write
      updateTest(1, { status: 'pending', message: 'Testing unauthorized write access...' });

      try {
        const unauthorizedRef = doc(collection(db, 'companies'), 'test-unauthorized-write');
        await setDoc(unauthorizedRef, {
          name: 'Unauthorized Company (Should Be Blocked)',
          email: '<EMAIL>',
          createdAt: new Date()
        });

        updateTest(1, {
          status: 'error',
          message: 'CRITICAL SECURITY VULNERABILITY',
          details: 'Unauthorized write access was allowed - security rules are not working properly'
        });
      } catch (error: any) {
        if (error.code === 'permission-denied') {
          updateTest(1, {
            status: 'success',
            message: 'Unauthorized write properly blocked',
            details: 'Security rules are working correctly - permission denied as expected'
          });
        } else {
          updateTest(1, {
            status: 'warning',
            message: 'Unexpected error during write test',
            details: `Error code: ${error.code}, Message: ${error.message}`
          });
        }
      }

      // Test 3: Company Service - Document Reference Fix
      updateTest(2, { status: 'pending', message: 'Testing company creation with proper document references...' });

      try {
        console.log('Starting company creation test...');

        const testCompany = await initializeCompany({
          name: 'Test Company (Document Reference Fix)',
          email: '<EMAIL>',
          phone: '******-DOCREF',
          address: 'Test Address for Document Reference Fix'
        });

        console.log('Company creation result:', testCompany);

        if (testCompany && testCompany.id) {
          setTestCompanyId(testCompany.id);
          updateTest(2, {
            status: 'success',
            message: 'Company creation successful',
            details: `Company created with valid document reference. ID: ${testCompany.id}, Name: ${testCompany.name}`
          });
        } else {
          throw new Error('Company creation returned invalid result - missing ID or company object');
        }
      } catch (error: any) {
        console.error('Company creation error:', error);

        if (error.message.includes('even number of segments')) {
          updateTest(2, {
            status: 'error',
            message: 'Document reference error still exists',
            details: 'The Firestore document reference construction is still incorrect'
          });
        } else if (error.code === 'permission-denied') {
          updateTest(2, {
            status: 'error',
            message: 'Company creation blocked by security rules',
            details: 'The security rules are blocking legitimate company creation. Check if the company name matches allowed patterns.'
          });
        } else {
          updateTest(2, {
            status: 'error',
            message: 'Company creation failed',
            details: `Error code: ${error.code || 'unknown'}, Message: ${error.message}`
          });
        }
      }

      // Test 4: Company Service - CRUD Operations
      updateTest(3, { status: 'pending', message: 'Testing company CRUD operations...' });
      
      if (testCompanyId) {
        try {
          // Test Read
          const retrievedCompany = await companyService.getById(testCompanyId);
          if (!retrievedCompany) {
            throw new Error('Failed to retrieve created company');
          }

          // Test Update
          const updatedCompany = await companyService.update(testCompanyId, {
            name: 'Updated Test Company (CRUD Test)'
          });
          
          if (updatedCompany.name !== 'Updated Test Company (CRUD Test)') {
            throw new Error('Company update failed');
          }

          updateTest(3, { 
            status: 'success', 
            message: 'All CRUD operations successful',
            details: 'Create, Read, and Update operations working correctly'
          });
        } catch (error: any) {
          updateTest(3, { 
            status: 'error', 
            message: 'CRUD operations failed',
            details: error.message
          });
        }
      } else {
        updateTest(3, { 
          status: 'error', 
          message: 'Cannot test CRUD operations',
          details: 'No test company was created in previous step'
        });
      }

    } catch (error: any) {
      console.error('Comprehensive test suite error:', error);
      toast({
        title: "Test Suite Error",
        description: error.message,
        variant: "destructive"
      });
    } finally {
      setIsRunning(false);
    }
  };

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'pending':
        return <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />;
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
    }
  };

  const getStatusBadge = (status: TestResult['status']) => {
    switch (status) {
      case 'pending':
        return <Badge variant="secondary">Pending</Badge>;
      case 'success':
        return <Badge variant="default" className="bg-green-500">Success</Badge>;
      case 'error':
        return <Badge variant="destructive">Error</Badge>;
      case 'warning':
        return <Badge variant="outline" className="border-yellow-500 text-yellow-600">Warning</Badge>;
    }
  };

  const allTestsPassed = tests.every(test => test.status === 'success');
  const hasErrors = tests.some(test => test.status === 'error');
  const hasWarnings = tests.some(test => test.status === 'warning');

  return (
    <Card className="w-full max-w-3xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Shield className="h-5 w-5" />
          Comprehensive Security & Functionality Test
        </CardTitle>
        <CardDescription>
          Verify that security rules are properly blocking unauthorized access and that company operations work correctly.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-3">
          {tests.map((test, index) => (
            <div key={index} className="flex items-start gap-3 p-4 border rounded-lg">
              <div className="mt-0.5">
                {getStatusIcon(test.status)}
              </div>
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between gap-2">
                  <h4 className="font-medium">{test.name}</h4>
                  {getStatusBadge(test.status)}
                </div>
                <p className="text-sm text-muted-foreground mt-1">{test.message}</p>
                {test.details && (
                  <p className="text-xs text-muted-foreground mt-2 p-2 bg-muted rounded font-mono">
                    {test.details}
                  </p>
                )}
              </div>
            </div>
          ))}
        </div>

        <Button 
          onClick={runComprehensiveTests} 
          disabled={isRunning}
          className="w-full"
          size="lg"
        >
          {isRunning ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Running Comprehensive Tests...
            </>
          ) : (
            <>
              <Shield className="mr-2 h-4 w-4" />
              Run Security & Functionality Tests
            </>
          )}
        </Button>

        {allTestsPassed && !isRunning && (
          <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
            <div className="flex items-center gap-2 text-green-800">
              <CheckCircle className="h-5 w-5" />
              <h4 className="font-medium">All Tests Passed!</h4>
            </div>
            <p className="text-sm text-green-700 mt-1">
              ✅ Security rules are properly blocking unauthorized access<br/>
              ✅ Company service is working with correct document references<br/>
              ✅ All CRUD operations are functioning correctly
            </p>
            {testCompanyId && (
              <p className="text-xs text-green-600 mt-2 font-mono">
                Test company ID: {testCompanyId}
              </p>
            )}
          </div>
        )}

        {hasErrors && !isRunning && (
          <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-center gap-2 text-red-800">
              <XCircle className="h-5 w-5" />
              <h4 className="font-medium">Critical Issues Detected</h4>
            </div>
            <p className="text-sm text-red-700 mt-1">
              Please review the error details above. These issues need to be resolved before using the application in production.
            </p>
          </div>
        )}

        {hasWarnings && !hasErrors && !isRunning && (
          <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <div className="flex items-center gap-2 text-yellow-800">
              <AlertTriangle className="h-5 w-5" />
              <h4 className="font-medium">Warnings Detected</h4>
            </div>
            <p className="text-sm text-yellow-700 mt-1">
              Some tests completed with warnings. Review the details above for more information.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
