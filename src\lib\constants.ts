import type { LucideIcon } from 'lucide-react';
import { LayoutDashboard, Users, Clock3, Settings, Shield } from 'lucide-react';

export const APP_NAME_DEFAULT = 'WePaie'; // For server-side metadata and default

export type NavItem = {
  href: string;
  labelKey: string; // Changed from label to labelKey for translation
  icon: LucideIcon;
  matchExact?: boolean;
  requireAdmin?: boolean;
};

export const NAV_LINKS: NavItem[] = [
  { href: '/dashboard', labelKey: 'nav.dashboard', icon: LayoutDashboard, matchExact: true },
  { href: '/employees', labelKey: 'nav.employees', icon: Users },
  { href: '/time-management', labelKey: 'nav.timeManagement', icon: Clock3 },
  { href: '/admin', labelKey: 'nav.admin', icon: Shield, requireAdmin: true },
  { href: '/settings', labelKey: 'nav.settings', icon: Settings },
];

// Employee Management Constants
// Note: Department management has been simplified to Service → Position hierarchy

export const SERVICES = [
  'Frontend',
  'Backend',
  'DevOps',
  'QA/Testing',
  'Mobile',
  'Data Science',
  'Security',
  'Infrastructure',
  'Management',
  'Business Development',
  'Content Creation',
  'Social Media',
  'SEO/SEM',
  'Analytics',
  'Recruitment',
  'Training',
  'Payroll',
  'Benefits',
  'Accounting',
  'Budgeting',
  'Tax',
  'Audit',
  'Technical Support',
  'Customer Success',
  'Help Desk',
  'Product Management',
  'Research',
  'Strategy',
  'UI/UX Design',
  'Graphic Design',
  'Brand Design',
  'Compliance',
  'Contracts',
  'Intellectual Property',
  'General Administration',
  'Office Management',
  'Facilities',
] as const;

export const POSITIONS = [
  'Software Developer',
  'Senior Developer',
  'Lead Developer',
  'Principal Engineer',
  'Engineering Manager',
  'CTO',
  'DevOps Engineer',
  'QA Engineer',
  'Data Scientist',
  'Security Engineer',
  'Mobile Developer',
  'Frontend Developer',
  'Backend Developer',
  'Full Stack Developer',
  'Operations Manager',
  'Operations Specialist',
  'Business Analyst',
  'Project Manager',
  'Product Manager',
  'Scrum Master',
  'Marketing Manager',
  'Marketing Specialist',
  'Content Manager',
  'Social Media Manager',
  'SEO Specialist',
  'Digital Marketing Specialist',
  'Sales Manager',
  'Sales Representative',
  'Account Manager',
  'Business Development Manager',
  'HR Manager',
  'HR Specialist',
  'Recruiter',
  'Training Coordinator',
  'Finance Manager',
  'Accountant',
  'Financial Analyst',
  'Payroll Specialist',
  'Customer Support Manager',
  'Support Specialist',
  'Technical Support Engineer',
  'Customer Success Manager',
  'Designer',
  'UI/UX Designer',
  'Graphic Designer',
  'Product Designer',
  'Legal Counsel',
  'Compliance Officer',
  'Administrative Assistant',
  'Office Manager',
  'Executive Assistant',
  'CEO',
  'COO',
  'CFO',
  'VP',
  'Director',
  'Manager',
  'Specialist',
  'Coordinator',
  'Assistant',
  'Intern',
] as const;

export type Department = typeof DEPARTMENTS[number];
export type Service = typeof SERVICES[number];
export type Position = typeof POSITIONS[number];

export const DEFAULT_REDIRECT = '/dashboard';
export const AUTH_REDIRECT = '/login';
