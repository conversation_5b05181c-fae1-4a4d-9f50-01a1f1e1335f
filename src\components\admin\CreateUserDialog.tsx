/**
 * Create User Dialog Component
 * 
 * Provides a form for creating new users with role assignment
 * and proper validation for the WePaie RBAC system.
 */

'use client';

import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Loader2, Mail, User, Phone, Shield, Lock } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';

import { Checkbox } from '@/components/ui/checkbox';
import { useRBAC } from '@/context/RBACContext';
import { useToast } from '@/hooks/use-toast';
import { createUser } from '@/lib/rbac/userService';

import { getAvailableRoles, ROLE_NAMES_FR, ROLE_DESCRIPTIONS_FR } from '@/lib/rbac/roles';
import type { UserRole, CreateUserRequest } from '@/lib/rbac/types';

// Form validation schema
const createUserSchema = z.object({
  email: z.string().email('Adresse email invalide'),
  firstName: z.string().min(2, 'Le prénom doit contenir au moins 2 caractères'),
  lastName: z.string().min(2, 'Le nom doit contenir au moins 2 caractères'),
  role: z.enum(['super_admin', 'company_admin', 'editor', 'viewer'] as const, {
    required_error: 'Veuillez sélectionner un rôle'
  }),
  phoneNumber: z.string().optional(),
  password: z.string().min(6, 'Le mot de passe doit contenir au moins 6 caractères'),
  sendEmailNotification: z.boolean().default(false)
});

type CreateUserFormData = z.infer<typeof createUserSchema>;

interface CreateUserDialogProps {
  companyId: string;
  onSuccess: () => void;
  onCancel: () => void;
}

export function CreateUserDialog({ companyId, onSuccess, onCancel }: CreateUserDialogProps) {
  const { user, canCreateUserWithRole } = useRBAC();
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);


  const form = useForm<CreateUserFormData>({
    resolver: zodResolver(createUserSchema),
    defaultValues: {
      email: '',
      firstName: '',
      lastName: '',
      phoneNumber: '',
      password: '',
      sendEmailNotification: false
    }
  });

  // Get available roles for current user
  const availableRoles = getAvailableRoles(user?.role || 'viewer');

  const onSubmit = async (data: CreateUserFormData) => {
    if (!canCreateUserWithRole(data.role, companyId)) {
      toast({
        title: 'Erreur',
        description: 'Vous n\'avez pas l\'autorisation de créer un utilisateur avec ce rôle',
        variant: 'destructive'
      });
      return;
    }

    setIsSubmitting(true);
    try {
      const request: CreateUserRequest = {
        ...data,
        companyId
      };

      await createUser(request);

      toast({
        title: 'Succès',
        description: 'Utilisateur créé avec succès. Un email de bienvenue a été envoyé.'
      });

      // Call onSuccess to close the dialog and refresh the user list
      onSuccess();

    } catch (error) {
      console.error('Error creating user:', error);
      toast({
        title: 'Erreur',
        description: error instanceof Error ? error.message : 'Impossible de créer l\'utilisateur',
        variant: 'destructive'
      });
    } finally {
      setIsSubmitting(false);
    }
  };



  return (
    <div>
      <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        {/* Email */}
        <FormField
          control={form.control}
          name="email"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Adresse email</FormLabel>
              <FormControl>
                <div className="relative">
                  <Mail className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    type="email"
                    placeholder="<EMAIL>"
                    className="pl-9"
                    {...field}
                  />
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* First Name */}
        <FormField
          control={form.control}
          name="firstName"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Prénom</FormLabel>
              <FormControl>
                <div className="relative">
                  <User className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Prénom"
                    className="pl-9"
                    {...field}
                  />
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Last Name */}
        <FormField
          control={form.control}
          name="lastName"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Nom</FormLabel>
              <FormControl>
                <div className="relative">
                  <User className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Nom"
                    className="pl-9"
                    {...field}
                  />
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Role */}
        <FormField
          control={form.control}
          name="role"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Rôle</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <div className="flex items-center gap-2">
                      <Shield className="h-4 w-4 text-muted-foreground" />
                      <SelectValue placeholder="Sélectionner un rôle" />
                    </div>
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {availableRoles.map((role) => (
                    <SelectItem key={role} value={role}>
                      <span className="font-medium">{ROLE_NAMES_FR[role]}</span>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormDescription>
                Sélectionnez le rôle qui détermine les permissions de l'utilisateur
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Phone Number */}
        <FormField
          control={form.control}
          name="phoneNumber"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Numéro de téléphone (optionnel)</FormLabel>
              <FormControl>
                <div className="relative">
                  <Phone className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    type="tel"
                    placeholder="+212 6XX XX XX XX"
                    className="pl-9"
                    {...field}
                  />
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Password */}
        <FormField
          control={form.control}
          name="password"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Mot de passe</FormLabel>
              <FormControl>
                <div className="relative">
                  <Lock className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    type="password"
                    placeholder="Mot de passe (min. 6 caractères)"
                    className="pl-9"
                    {...field}
                  />
                </div>
              </FormControl>
              <FormDescription>
                L'utilisateur pourra se connecter immédiatement avec ce mot de passe
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        {/* Send Email Notification */}
        <FormField
          control={form.control}
          name="sendEmailNotification"
          render={({ field }) => (
            <FormItem className="flex flex-row items-start space-x-3 space-y-0">
              <FormControl>
                <Checkbox
                  checked={field.value}
                  onCheckedChange={field.onChange}
                />
              </FormControl>
              <div className="space-y-1 leading-none">
                <FormLabel>
                  Envoyer par email
                </FormLabel>
                <FormDescription>
                  Envoyer un email à l'utilisateur avec ses identifiants de connexion
                </FormDescription>
              </div>
            </FormItem>
          )}
        />

        {/* Actions */}
        <div className="flex justify-end gap-2 pt-4">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={isSubmitting}
          >
            Annuler
          </Button>
          <Button
            type="submit"
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Création...
              </>
            ) : (
              'Créer l\'utilisateur'
            )}
          </Button>
        </div>
      </form>
    </Form>
    </div>
  );
}
