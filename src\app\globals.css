@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 200 20% 98%; /* Light cool gray */
    --foreground: 210 10% 23%; /* Dark gray for text */

    --card: 0 0% 100%; /* White card */
    --card-foreground: 210 10% 23%; /* Dark text on card */

    --popover: 0 0% 100%;
    --popover-foreground: 210 10% 23%;

    --primary: 207 68% 53%; /* Vibrant Blue #3498db */
    --primary-foreground: 210 40% 98%; /* Light text on primary */

    --secondary: 210 17% 95%; /* Clean White/Light Gray #ecf0f1 */
    --secondary-foreground: 210 10% 23%; /* Dark text on secondary */

    --muted: 210 17% 95%; /* Same as secondary for muted backgrounds */
    --muted-foreground: 210 10% 45%; /* Slightly lighter dark gray for muted text */

    --accent: 186 80% 56%; /* Bright Sky Blue #34d9eb */
    --accent-foreground: 210 10% 23%; /* Dark text on accent */

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 210 15% 88%;
    --input: 210 15% 88%;
    --ring: 186 80% 56%; /* Accent color for rings */

    --radius: 0.375rem; /* Reduced from 0.5rem for compactness */

    --chart-1: 207 68% 53%;
    --chart-2: 186 80% 56%;
    --chart-3: 210 30% 60%;
    --chart-4: 200 50% 70%;
    --chart-5: 190 60% 65%;

    /* Sidebar specific colors - Light Theme */
    --sidebar-background: 210 20% 96%;
    --sidebar-foreground: 210 10% 30%;
    --sidebar-primary: 207 68% 53%; /* Active item background */
    --sidebar-primary-foreground: 210 40% 98%; /* Active item text */
    --sidebar-accent: 200 20% 92%; /* Hover item background */
    --sidebar-accent-foreground: 207 68% 45%; /* Hover item text */
    --sidebar-border: 210 15% 88%;
    --sidebar-ring: 186 80% 56%;
  }

  .dark {
    --background: 215 28% 17%;
    --foreground: 210 40% 98%;

    --card: 215 28% 17%;
    --card-foreground: 210 40% 98%;

    --popover: 215 28% 17%;
    --popover-foreground: 210 40% 98%;

    --primary: 207 70% 60%;
    --primary-foreground: 215 25% 27%;

    --secondary: 215 20% 25%;
    --secondary-foreground: 210 40% 98%;

    --muted: 215 20% 25%;
    --muted-foreground: 210 20% 65%;

    --accent: 186 70% 65%;
    --accent-foreground: 215 25% 27%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;

    --border: 215 20% 30%;
    --input: 215 20% 30%;
    --ring: 186 70% 65%;

    --chart-1: 207 70% 60%;
    --chart-2: 186 70% 65%;
    --chart-3: 210 30% 50%;
    --chart-4: 200 50% 60%;
    --chart-5: 190 60% 55%;
    
    /* Sidebar specific colors - Dark Theme */
    --sidebar-background: 215 28% 12%;
    --sidebar-foreground: 210 30% 80%;
    --sidebar-primary: 207 70% 60%;
    --sidebar-primary-foreground: 215 25% 20%;
    --sidebar-accent: 215 20% 20%;
    --sidebar-accent-foreground: 186 70% 75%;
    --sidebar-border: 215 20% 25%;
    --sidebar-ring: 186 70% 65%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground text-sm;
    transition: background-color 0.3s ease, color 0.3s ease;
    font-size: 14px; /* Explicit font-size for global text reduction */
    font-family: var(--font-family, 'Roboto, sans-serif');
    font-weight: var(--font-weight, 400);
  }

  /* Text size classes - apply to all elements */
  html.text-size-small,
  html.text-size-small * {
    font-size: 12px !important;
  }

  html.text-size-medium,
  html.text-size-medium * {
    font-size: 14px !important;
  }

  html.text-size-large,
  html.text-size-large * {
    font-size: 16px !important;
  }

  /* Override specific elements that need to maintain relative sizing */
  html.text-size-small h1 { font-size: 1.5rem !important; }
  html.text-size-small h2 { font-size: 1.25rem !important; }
  html.text-size-small h3 { font-size: 1.125rem !important; }

  html.text-size-medium h1 { font-size: 1.75rem !important; }
  html.text-size-medium h2 { font-size: 1.5rem !important; }
  html.text-size-medium h3 { font-size: 1.25rem !important; }

  html.text-size-large h1 { font-size: 2rem !important; }
  html.text-size-large h2 { font-size: 1.75rem !important; }
  html.text-size-large h3 { font-size: 1.5rem !important; }

  /* Font style classes - apply to all elements */
  .font-style-normal,
  .font-style-normal * {
    font-weight: 400 !important;
  }

  .font-style-medium,
  .font-style-medium * {
    font-weight: 500 !important;
  }

  .font-style-bold,
  .font-style-bold * {
    font-weight: 700 !important;
  }

  /* Apply font family based on language */
  [lang="ar"],
  [lang="ar"] * {
    font-family: var(--font-arabic, 'Noto Sans Arabic, sans-serif') !important;
  }

  [lang="en"],
  [lang="en"] *,
  [lang="fr"],
  [lang="fr"] * {
    font-family: var(--font-latin, 'Roboto, sans-serif') !important;
  }

  /* Font family override for current language */
  html.font-family-override,
  html.font-family-override * {
    font-family: var(--font-family, 'Roboto, sans-serif') !important;
  }

  /* Smooth transitions for theme changes */
  :root {
    transition: all 0.3s ease;
  }

  /* Apply transitions to common elements */
  .card, .border, .bg-card, .bg-background, .bg-primary, .bg-secondary, .bg-muted, .bg-accent {
    transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
  }

  /* Global text size reduction for compact display */
  h1 { @apply text-xl; } /* Reduced from text-2xl */
  h2 { @apply text-lg; } /* Reduced from text-xl */
  h3 { @apply text-base; } /* Reduced from text-lg */
  h4 { @apply text-sm; } /* Reduced from text-base */
  h5 { @apply text-xs; } /* Reduced from text-sm */
  h6 { @apply text-xs; } /* Reduced from text-sm */

  /* Card components */
  .card-title { @apply text-base; } /* Reduced from text-lg */
  .card-description { @apply text-xs; } /* Reduced from text-sm */

  /* Form elements */
  label { @apply text-xs; } /* Reduced from text-sm */
  .form-label { @apply text-xs; } /* Reduced from text-sm */
  .form-description { @apply text-xs; } /* Keep text-xs */

  /* Table elements */
  th { @apply text-xs; } /* Reduced from text-sm */
  td { @apply text-xs; } /* Reduced from text-sm */

  /* Navigation elements */
  .nav-link { @apply text-xs; } /* Reduced from text-sm */
  .tab-trigger { @apply text-xs; } /* Reduced from text-sm */

  /* Button text - maintain readability */
  .btn { @apply text-xs; } /* Reduced from text-sm */
  .btn-lg { @apply text-sm; } /* Reduced from text-base */
  .btn-sm { @apply text-xs; } /* Keep text-xs */

  /* RTL Layout Support */
  [dir="rtl"] {
    direction: rtl;
  }

  /* RTL Text alignment for specific elements */
  [dir="rtl"] .text-left {
    text-align: right;
  }

  [dir="rtl"] .text-right {
    text-align: left;
  }

  /* RTL Form elements */
  [dir="rtl"] input,
  [dir="rtl"] textarea,
  [dir="rtl"] select {
    text-align: right;
  }

  /* RTL Dropdown positioning */
  [dir="rtl"] [data-radix-popper-content-wrapper] {
    transform-origin: top right !important;
  }

  /* RTL specific margin/padding adjustments for icons */
  [dir="rtl"] .mr-2 {
    margin-right: 0;
    margin-left: 0.5rem;
  }

  [dir="rtl"] .ml-2 {
    margin-left: 0;
    margin-right: 0.5rem;
  }

  [dir="rtl"] .mr-4 {
    margin-right: 0;
    margin-left: 1rem;
  }

  [dir="rtl"] .ml-4 {
    margin-left: 0;
    margin-right: 1rem;
  }

  /* RTL Sidebar positioning */
  [dir="rtl"] [data-sidebar="sidebar"] {
    order: 2;
  }

  [dir="rtl"] [data-sidebar="content"] {
    order: 1;
  }

  /* Standardized Container System */
  .page-container {
    width: 100%;
    margin-left: auto;
    margin-right: auto;
    padding-left: 1rem; /* 16px */
    padding-right: 1rem; /* 16px */
  }

  @media (min-width: 640px) {
    .page-container {
      padding-left: 1.5rem; /* 24px */
      padding-right: 1.5rem; /* 24px */
    }
  }

  @media (min-width: 1024px) {
    .page-container {
      padding-left: 2rem; /* 32px */
      padding-right: 2rem; /* 32px */
    }
  }

  /* Content width constraints for readability */
  .content-container {
    width: 100%;
    max-width: 1400px; /* Generous max-width for wide screens */
    margin-left: auto;
    margin-right: auto;
  }

  /* Form containers for better readability */
  .form-container {
    width: 100%;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
  }

  /* Ensure maximum width utilization */
  .max-width-layout {
    width: 100%;
    max-width: none;
  }

  /* Full-width header border */
  .full-width-header {
    width: 100%;
    border-bottom: 1px solid hsl(var(--border));
  }

  /* RTL border adjustments */
  [dir="rtl"] .border-r {
    border-right: none;
    border-left: 1px solid hsl(var(--border));
  }

  [dir="rtl"] .border-l {
    border-left: none;
    border-right: 1px solid hsl(var(--border));
  }
}

@layer components {
  /* Remove number input spinners */
  .no-spinner::-webkit-outer-spin-button,
  .no-spinner::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  .no-spinner[type="number"] {
    -moz-appearance: textfield;
  }

  /* Apply to all number inputs by default */
  input[type="number"]::-webkit-outer-spin-button,
  input[type="number"]::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }

  input[type="number"] {
    -moz-appearance: textfield;
  }
}
