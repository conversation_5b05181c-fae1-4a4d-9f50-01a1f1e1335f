
"use client";

import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { employeeSchema, type EmployeeFormData } from '@/lib/schemas';
import type { Employee } from '@/types';
import type { EmployeeDocument } from '@/lib/firestore/types';

import { useCreateEmployee, useUpdateEmployee } from '@/hooks/useEmployeeData';
import { useToast } from "@/hooks/use-toast";

import { Button } from "@/components/ui/button";

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";

import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { SearchableCombobox } from "@/components/ui/searchable-combobox";
import { FormSkeleton } from "@/components/ui/skeleton-loaders";
import { useImmediateFeedback } from "@/hooks/useImmediateFeedback";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { useServices, usePositions } from "@/hooks/useOrganizationalData";
import { useCompanyId, useUserId, usePermissions } from "@/context/FirestoreContext";
import {
  Loader2,
  Save,
  X,
  Calendar as CalendarIcon,
  Phone,
  MapPin,
  Calculator
} from "lucide-react";

/**
 * Employee Form Props
 */
interface EmployeeFormProps {
  employee?: EmployeeDocument | null;
  onSuccess?: (employee: EmployeeDocument) => void;
  onCancel?: () => void;
  mode?: 'create' | 'edit';
  className?: string;
}

/**
 * Employee Form Component
 *
 * Comprehensive form for creating and editing employees.
 * Integrates with Firestore services and provides real-time validation.
 *
 * Features:
 * - Create/edit modes with proper validation
 * - Salary validation (hourly or weekly required)
 * - Hire date validation
 * - Loading states and error handling
 */
export function EmployeeForm({
  employee,
  onSuccess,
  onCancel,
  mode = 'create',
  className
}: EmployeeFormProps) {
  const { toast } = useToast();

  const { createEmployee, isLoading: isCreating, error: createError } = useCreateEmployee();
  const { updateEmployee, isLoading: isUpdating, error: updateError } = useUpdateEmployee();

  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [calculatedHourlyRate, setCalculatedHourlyRate] = useState<number | null>(null);
  const [showCancelDialog, setShowCancelDialog] = useState(false);

  // Immediate feedback for form operations
  const { isProcessing, withImmediateFeedback } = useImmediateFeedback();

  // Context hooks - must be called at component level
  const companyId = useCompanyId();
  const userId = useUserId();
  const { isAdmin, isPayrollManager } = usePermissions();

  const isLoading = isCreating || isUpdating;
  const isEditMode = mode === 'edit';

  // Initialize form first
  const form = useForm<EmployeeFormData>({
    resolver: zodResolver(employeeSchema),
    defaultValues: {
      firstName: employee?.firstName || "",
      lastName: employee?.lastName || "",
      phoneNumber: employee?.phoneNumber || "",
      address: employee?.address || "",
      service: employee?.service || "",
      position: employee?.position || "",
      hoursPerWeek: employee?.hoursPerWeek || 40,
      weeklySalary: employee?.weeklySalary || 0,
      hourlyRate: employee?.hourlyRate || 0, // Changed from undefined to 0
      overtimeRate: employee?.overtimeRate || 0, // Changed from undefined to 0
      hireDate: employee?.hireDate ? employee.hireDate : new Date().toISOString().split('T')[0],
      status: employee?.status || "Active",
    },
  });

  // Watch for form changes to detect unsaved changes and calculate hourly rate
  const watchedValues = form.watch();
  const weeklySalary = form.watch('weeklySalary');
  const hoursPerWeek = form.watch('hoursPerWeek');

  // Organizational data hooks with simplified hierarchy (after form initialization)
  const { services, createService } = useServices();

  // Get selected service ID for filtering
  const selectedServiceName = form.watch('service');
  const selectedService = services.find(s => s.name === selectedServiceName);
  // If no service is selected or "Aucun service spécifique" is selected, don't filter positions
  const selectedServiceId = selectedService?.id;

  // Get positions - filtered by service if selected, otherwise all positions
  const { positions, createPosition } = usePositions(selectedServiceId);

  useEffect(() => {
    setHasUnsavedChanges(form.formState.isDirty);
  }, [watchedValues, form.formState.isDirty]);

  // Calculate hourly rate automatically
  useEffect(() => {
    if (weeklySalary && hoursPerWeek && weeklySalary > 0 && hoursPerWeek > 0) {
      const hourlyRate = weeklySalary / hoursPerWeek;
      setCalculatedHourlyRate(Math.round(hourlyRate * 100) / 100);
    } else {
      setCalculatedHourlyRate(null);
    }
  }, [weeklySalary, hoursPerWeek]);

  // Reset form when employee changes
  useEffect(() => {
    if (employee) {
      form.reset({
        firstName: employee.firstName,
        lastName: employee.lastName,
        phoneNumber: employee.phoneNumber || "",
        address: employee.address || "",
        service: employee.service || "",
        position: employee.position,
        hoursPerWeek: employee.hoursPerWeek || 40,
        weeklySalary: employee.weeklySalary || 0,
        hourlyRate: employee.hourlyRate || 0, // Changed from undefined to 0
        overtimeRate: employee.overtimeRate || 0, // Changed from undefined to 0
        hireDate: employee.hireDate,
        status: employee.status,
      });
      setHasUnsavedChanges(false);
    }
  }, [employee, form]);

  // Smart auto-selection logic
  useEffect(() => {
    const currentPosition = form.getValues('position');

    // When "Aucun service spécifique" is selected (empty service), reset position field
    if (!selectedServiceName || selectedServiceName === '') {
      if (currentPosition) {
        form.setValue('position', '');
        console.log('🔄 Reset position field because no service is selected');
      }
      return; // Exit early when no service is selected
    }

    // When a specific service is selected, reset position if it's no longer valid for the selected service
    if (selectedServiceName && currentPosition) {
      const isPositionValid = positions.some(p => p.name === currentPosition);
      if (!isPositionValid) {
        form.setValue('position', '');
        console.log('🔄 Reset position field because it\'s not valid for the selected service');
      }
    }
  }, [selectedServiceName, positions, form]);

  useEffect(() => {
    // When position is selected, auto-select the corresponding service if the position belongs to a specific service
    const selectedPositionName = form.watch('position');
    if (selectedPositionName && !selectedServiceName) {
      // Find all positions to get the one with service info
      const allPositions = positions; // This will be all positions when no service is selected
      const selectedPosition = allPositions.find(p => p.name === selectedPositionName);
      if (selectedPosition && selectedPosition.serviceId) {
        // Find the service that this position belongs to
        const correspondingService = services.find(s => s.id === selectedPosition.serviceId);
        if (correspondingService) {
          form.setValue('service', correspondingService.name);
        }
      }
    }
  }, [form.watch('position'), selectedServiceName, positions, services, form]);

  /**
   * Handle form submission
   */
  async function onSubmit(data: EmployeeFormData) {
    const message = isEditMode ? 'Mise à jour de l\'employé...' : 'Création de l\'employé...';

    try {

      const result = await withImmediateFeedback(async () => {
        let employeeResult: EmployeeDocument;

        // Clean the form data before sending
        const cleanedData = {
          ...data,
          // Convert empty strings to undefined for optional fields
          phoneNumber: data.phoneNumber || undefined,
          address: data.address || undefined,
          service: data.service || undefined,
          // Ensure numeric fields are properly typed
          hourlyRate: typeof data.hourlyRate === 'number' ? data.hourlyRate : undefined,
          hoursPerWeek: typeof data.hoursPerWeek === 'number' ? data.hoursPerWeek : undefined,
          weeklySalary: typeof data.weeklySalary === 'number' ? data.weeklySalary : undefined,
          overtimeRate: typeof data.overtimeRate === 'number' ? data.overtimeRate : undefined,
        };

        console.log('🔍 Cleaned form data for submission:', cleanedData);

        if (isEditMode && employee) {
          // Update existing employee
          employeeResult = await updateEmployee(employee.id, cleanedData as Partial<Employee>);
        } else {
          // Create new employee
          employeeResult = await createEmployee(cleanedData as Omit<Employee, 'id'>);

          // Reset form for new entries
          form.reset();
        }

        return employeeResult;
      }, message, 500);

      setHasUnsavedChanges(false);
      onSuccess?.(result);

    } catch (error: any) {
      console.error("Failed to save employee:", error);

      // Enhanced error logging for permission issues
      if (error.message?.includes('permission') || error.code === 'permission-denied') {
        console.error('🚫 Permission error details:', {
          error: error.message,
          code: error.code,
          companyId,
          userId,
          isAdmin,
          isPayrollManager
        });
      }

      toast({
        title: isEditMode ? "Échec de la mise à jour" : "Échec de la création",
        description: error.message?.includes('permission')
          ? "Permissions insuffisantes. Veuillez vérifier vos droits d'accès."
          : error.message || `Impossible de ${isEditMode ? 'mettre à jour' : 'créer'} l'employé. Veuillez réessayer.`,
        variant: "destructive",
      });
    }
  }

  /**
   * Handle cancel action
   */
  const handleCancel = () => {
    if (hasUnsavedChanges) {
      setShowCancelDialog(true);
      return;
    }

    // No unsaved changes, cancel immediately
    form.reset();
    setHasUnsavedChanges(false);
    onCancel?.();
  };

  /**
   * Confirm cancel action
   */
  const confirmCancel = () => {
    form.reset();
    setHasUnsavedChanges(false);
    setShowCancelDialog(false);
    onCancel?.();
  };

  // Show skeleton loading when processing
  if (isProcessing) {
    return (
      <div className={className}>
        <FormSkeleton />
      </div>
    );
  }

  return (
    <div className={className}>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-3">

          {/* Status Badge for Edit Mode */}
          {isEditMode && employee && (
            <div className="flex justify-end">
              <Badge variant={employee.status === 'Active' ? 'default' : 'secondary'}>
                {employee.status === 'Active' ? 'Actif' : 'Inactif'}
              </Badge>
            </div>
          )}

          {/* Personal Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
            <FormField
              control={form.control}
              name="firstName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm">Prénom *</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Ahmed"
                      {...field}
                      disabled={isLoading}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="lastName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm">Nom *</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Benali"
                      {...field}
                      disabled={isLoading}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="phoneNumber"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm">N° Téléphone</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="+212 6XX XX XX XX"
                      {...field}
                      disabled={isLoading}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="address"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm">Adresse</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="123 Avenue Mohammed V, Casablanca"
                      {...field}
                      disabled={isLoading}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="hireDate"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="flex items-center gap-2 text-sm">
                    <CalendarIcon className="h-3 w-3" />
                    Date d'embauche *
                  </FormLabel>
                  <FormControl>
                    <Input
                      type="date"
                      {...field}
                      disabled={isLoading}
                      max={new Date().toISOString().split('T')[0]}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
          {/* Employment Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
            <FormField
              control={form.control}
              name="service"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm">Service</FormLabel>
                  <FormControl>
                    <SearchableCombobox
                      options={[
                        { value: "__none__", label: "Aucun service spécifique" },
                        ...services.map(service => ({ value: service.name, label: service.name }))
                      ]}
                      value={field.value === "" ? "__none__" : field.value}
                      onValueChange={(value) => field.onChange(value === "__none__" ? "" : value)}
                      onCreateNew={async (name) => {
                        await createService(name);
                      }}
                      placeholder="Sélectionner un service"
                      searchPlaceholder="Rechercher un service..."
                      emptyText="Aucun service trouvé."
                      createText="Créer le service"
                      disabled={isLoading}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="position"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm">Fonction *</FormLabel>
                  <FormControl>
                    <SearchableCombobox
                      options={positions.map(pos => ({ value: pos.name, label: pos.name }))}
                      value={field.value}
                      onValueChange={field.onChange}
                      onCreateNew={async (name) => {
                        await createPosition(name, selectedServiceId);
                      }}
                      placeholder="Sélectionner une fonction"
                      searchPlaceholder="Rechercher une fonction..."
                      emptyText="Aucune fonction trouvée."
                      createText="Créer la fonction"
                      disabled={isLoading}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="status"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm">Statut d'emploi</FormLabel>
                  <div className="flex items-center space-x-2 pt-1">
                    <Switch
                      checked={field.value === 'Active'}
                      onCheckedChange={(checked) =>
                        field.onChange(checked ? 'Active' : 'Inactive')
                      }
                      disabled={isLoading}
                    />
                    <span className="text-sm">
                      {field.value === 'Active' ? 'Actif' : 'Inactif'}
                    </span>
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
          {/* Compensation Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
            <FormField
              control={form.control}
              name="weeklySalary"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm">Salaire Hébdomadaire *</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      step="0.01"
                      placeholder="3000.00"
                      {...field}
                      disabled={isLoading}
                      onChange={e => field.onChange(e.target.value === '' ? 0 : parseFloat(e.target.value))}
                      className="no-spinner"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="hoursPerWeek"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm">Nombre d'heure travaillé par semaine *</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      step="0.1"
                      placeholder="44"
                      {...field}
                      disabled={isLoading}
                      onChange={e => field.onChange(e.target.value === '' ? 0 : parseFloat(e.target.value))}
                      className="no-spinner"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="overtimeRate"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm">Prix Heure Supplémentaire</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      step="0.01"
                      placeholder="85.00"
                      {...field}
                      disabled={isLoading}
                      onChange={e => field.onChange(e.target.value === '' ? 0 : parseFloat(e.target.value))}
                      className="no-spinner"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          {/* Calculated Hourly Rate Display */}
          {calculatedHourlyRate !== null && (
            <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="flex items-center gap-2 text-blue-800">
                <Calculator className="h-3 w-3" />
                <span className="text-xs font-medium">
                  Prix Heure (calculé automatiquement): {calculatedHourlyRate.toFixed(2)} MAD
                </span>
              </div>
            </div>
          )}
          {/* Form Actions */}
          <div className="flex flex-col sm:flex-row gap-2 pt-3 sm:justify-end">
            {onCancel && (
              <Button
                type="button"
                variant="outline"
                onClick={handleCancel}
                disabled={isLoading}
                className="flex-1 sm:flex-none"
              >
                <X className="mr-2 h-4 w-4" />
                Annuler
              </Button>
            )}

            <Button
              type="submit"
              disabled={isLoading || !hasUnsavedChanges || isProcessing}
              className="flex-1 sm:flex-none"
            >
              {(isLoading || isProcessing) ? (
                <>
                  <div className="mr-2 h-4 w-4 animate-pulse bg-current rounded" />
                  {isEditMode ? 'Mise à jour...' : 'Création...'}
                </>
              ) : (
                <>
                  <Save className="mr-2 h-4 w-4" />
                  {isEditMode ? 'Mettre à jour l\'employé' : 'Créer l\'employé'}
                </>
              )}
            </Button>
          </div>

          {/* Cancel Confirmation Dialog */}
          <AlertDialog open={showCancelDialog} onOpenChange={setShowCancelDialog}>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Confirmer l'annulation</AlertDialogTitle>
                <AlertDialogDescription>
                  Vous avez des modifications non sauvegardées. Êtes-vous sûr de vouloir annuler ?
                  Toutes les modifications seront perdues.
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel onClick={() => setShowCancelDialog(false)}>
                  Continuer l'édition
                </AlertDialogCancel>
                <AlertDialogAction onClick={confirmCancel} className="bg-destructive text-destructive-foreground hover:bg-destructive/90">
                  Confirmer l'annulation
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>

          {/* Unsaved Changes Warning */}
          {hasUnsavedChanges && (
            <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
              <div className="flex items-center gap-2 text-yellow-800">
                <CalendarIcon className="h-3 w-3" />
                <span className="text-xs font-medium">
                  Vous avez des modifications non sauvegardées.
                </span>
              </div>
            </div>
          )}

          {/* Error Display */}
          {(createError || updateError) && (
            <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
              <div className="flex items-center gap-2 text-red-800">
                <CalendarIcon className="h-3 w-3" />
                <span className="text-xs font-medium">
                  Erreur: {createError?.message || updateError?.message}
                </span>
              </div>
            </div>
          )}
        </form>
      </Form>
    </div>
  );
}

    