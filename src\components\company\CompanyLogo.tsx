"use client";

import React from 'react';
import { Building2 } from 'lucide-react';
import { useCompany } from '@/context/CompanyContext';
import { useTheme } from '@/context/ThemeContext';
import { cn } from '@/lib/utils';

interface CompanyLogoProps {
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  fallbackToDefault?: boolean;
}

export function CompanyLogo({ 
  className, 
  size = 'md', 
  fallbackToDefault = true 
}: CompanyLogoProps) {
  const { selectedCompany } = useCompany();
  const { currentTheme } = useTheme();

  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-6 w-6',
    lg: 'h-8 w-8'
  };

  // If no company or no logo, show default WePaie logo
  if (!selectedCompany?.logo) {
    if (!fallbackToDefault) return null;
    
    return (
      <Building2 
        className={cn(
          sizeClasses[size], 
          'text-primary',
          className
        )} 
      />
    );
  }

  // Apply theme colors to SVG
  const applyThemeToSVG = (svgContent: string): string => {
    // Get current theme colors
    const primaryColor = getComputedStyle(document.documentElement)
      .getPropertyValue('--primary')
      .trim();
    
    const foregroundColor = getComputedStyle(document.documentElement)
      .getPropertyValue('--foreground')
      .trim();

    // Replace common SVG color attributes with theme colors
    let themedSVG = svgContent
      .replace(/fill="[^"]*"/g, `fill="hsl(${primaryColor})"`)
      .replace(/stroke="[^"]*"/g, `stroke="hsl(${foregroundColor})"`)
      .replace(/fill:#[0-9a-fA-F]{3,6}/g, `fill:hsl(${primaryColor})`)
      .replace(/stroke:#[0-9a-fA-F]{3,6}/g, `stroke:hsl(${foregroundColor})`);

    // Ensure SVG has proper dimensions
    if (!themedSVG.includes('width=') && !themedSVG.includes('height=')) {
      themedSVG = themedSVG.replace(
        '<svg',
        `<svg width="100%" height="100%" viewBox="0 0 24 24"`
      );
    }

    return themedSVG;
  };

  const themedLogo = applyThemeToSVG(selectedCompany.logo);

  return (
    <div 
      className={cn(
        sizeClasses[size],
        'flex items-center justify-center',
        className
      )}
      dangerouslySetInnerHTML={{ __html: themedLogo }}
    />
  );
}

// Hook to get company logo URL for use in other contexts
export function useCompanyLogo() {
  const { selectedCompany } = useCompany();
  
  return {
    hasLogo: !!selectedCompany?.logo,
    logoContent: selectedCompany?.logo || null,
    companyName: selectedCompany?.name || 'WePaie'
  };
}
