import type { Metadata } from 'next';
import AppShell from '@/components/layout/AppShell';
import { EmployeeTable } from '@/components/employees/EmployeeTable';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';

export const metadata: Metadata = {
  title: 'Gestion des Employés - WePaie',
};

export default function EmployeesPage() {
  return (
    <ProtectedRoute requireAuth={true}>
      <AppShell pageTitle="Gestion des Employés">
        <EmployeeTable />
      </AppShell>
    </ProtectedRoute>
  );
}
