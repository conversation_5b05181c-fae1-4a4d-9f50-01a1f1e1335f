"use client";

import type React from 'react';
import { createContext, useContext, useState, useEffect, useCallback } from 'react';

export type Locale = 'en' | 'fr' | 'ar';
export type Translations = Record<string, string>;

interface I18nContextType {
  language: Locale;
  setLanguage: (language: Locale) => void;
  translations: Translations;
  t: (key: string, fallback?: string) => string;
}

const I18nContext = createContext<I18nContextType | undefined>(undefined);

const defaultTranslations: Record<Locale, Translations> = {
  en: {},
  fr: {},
  ar: {},
};

export function I18nProvider({ children }: { children: React.ReactNode }) {
  const [language, setLanguageState] = useState<Locale>('en');
  const [translations, setTranslations] = useState<Translations>(defaultTranslations[language]);
  const [isLoaded, setIsLoaded] = useState(false);

  const loadTranslations = useCallback(async (locale: Locale) => {
    setIsLoaded(false);
    try {
      // Dynamically import the JSON file for the selected locale
      const module = await import(`@/locales/${locale}.json`);
      setTranslations(module.default);
    } catch (error) {
      console.error(`Could not load translations for ${locale}:`, error);
      // Fallback to English or empty translations if loading fails
      const fallbackModule = await import(`@/locales/en.json`);
      setTranslations(fallbackModule.default);
    } finally {
      setIsLoaded(true);
    }
  }, []);

  useEffect(() => {
    const storedLang = localStorage.getItem('wepaie_lang') as Locale | null;
    let initialLang: Locale = 'en'; // Default language

    if (storedLang && ['en', 'fr', 'ar'].includes(storedLang)) {
      initialLang = storedLang;
    } else {
      const browserLang = navigator.language.split('-')[0] as Locale;
      if (['en', 'fr', 'ar'].includes(browserLang)) {
        initialLang = browserLang;
      }
    }
    
    setLanguageState(initialLang);
    loadTranslations(initialLang);
  }, [loadTranslations]);

  const setLanguage = (newLocale: Locale) => {
    localStorage.setItem('wepaie_lang', newLocale);
    setLanguageState(newLocale);
    loadTranslations(newLocale);
  };

  const t = useCallback((key: string, fallback?: string): string => {
    return translations[key] || fallback || key;
  }, [translations]);

  useEffect(() => {
    if (language === 'ar') {
      document.documentElement.setAttribute('dir', 'rtl');
    } else {
      document.documentElement.setAttribute('dir', 'ltr');
    }
    document.documentElement.lang = language;
  }, [language]);
  
  // Render children directly; loading state could be enhanced with a global loader
  // if (!isLoaded) {
  //   return null; // Or a loading spinner
  // }

  return (
    <I18nContext.Provider value={{ language, setLanguage, translations, t }}>
      {children}
    </I18nContext.Provider>
  );
}

export function useTranslation() {
  const context = useContext(I18nContext);
  if (context === undefined) {
    throw new Error('useTranslation must be used within an I18nProvider');
  }
  return context;
}
