"use client";

import React, { useState, useEffect } from 'react';
import { useFirestore } from '@/context/FirestoreContext';
import { initializeDemoSetup, shouldUseDemoData } from '@/lib/demo-data';
import { companyService } from '@/lib/firestore';
import type { Company } from '@/lib/firestore/types';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Loader2, 
  Building2, 
  Plus, 
  AlertCircle, 
  CheckCircle,
  Rocket
} from 'lucide-react';

/**
 * Company Selector Component
 * 
 * Displays when no company is selected and provides options to:
 * - Initialize demo data for development
 * - Select from existing companies
 * - Create a new company
 */

interface CompanySelectorProps {
  onCompanySelected?: (company: Company) => void;
}

export function CompanySelector({ onCompanySelected }: CompanySelectorProps) {
  const { setCompany, setUser, isLoading: contextLoading } = useFirestore();
  const [isInitializing, setIsInitializing] = useState(false);
  const [availableCompanies, setAvailableCompanies] = useState<Company[]>([]);
  const [isLoadingCompanies, setIsLoadingCompanies] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Load available companies on mount
  useEffect(() => {
    loadAvailableCompanies();
  }, []);

  const loadAvailableCompanies = async () => {
    setIsLoadingCompanies(true);
    setError(null);
    
    try {
      // For now, we'll just check if demo company exists
      // In a real app, you might want to list companies the user has access to
      const demoCompany = await companyService.getById('demo-company-wepaie');
      if (demoCompany) {
        setAvailableCompanies([demoCompany]);
      }
    } catch (error) {
      console.log('No existing companies found');
      setAvailableCompanies([]);
    } finally {
      setIsLoadingCompanies(false);
    }
  };

  const handleInitializeDemoData = async () => {
    setIsInitializing(true);
    setError(null);
    setSuccess(null);

    try {
      console.log('Initializing demo data...');
      const { company, admin } = await initializeDemoSetup();
      
      // Set the company and user in context
      setCompany(company);
      setUser(admin);
      
      setSuccess(`Demo company "${company.name}" has been initialized successfully!`);
      onCompanySelected?.(company);
      
      // Refresh available companies
      await loadAvailableCompanies();
      
    } catch (error: any) {
      console.error('Failed to initialize demo data:', error);
      setError(`Failed to initialize demo data: ${error.message}`);
    } finally {
      setIsInitializing(false);
    }
  };

  const handleSelectCompany = async (company: Company) => {
    setError(null);
    setSuccess(null);

    try {
      // For demo purposes, we'll use the demo admin
      // In a real app, you'd need proper user authentication
      const { admin } = await initializeDemoSetup();
      
      setCompany(company);
      setUser(admin);
      
      setSuccess(`Selected company: ${company.name}`);
      onCompanySelected?.(company);
      
    } catch (error: any) {
      console.error('Failed to select company:', error);
      setError(`Failed to select company: ${error.message}`);
    }
  };

  if (contextLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-8 w-8 animate-spin mr-2" />
        <span>Loading application...</span>
      </div>
    );
  }

  return (
    <div className="max-w-2xl mx-auto p-6 space-y-6">
      <div className="text-center space-y-2">
        <Building2 className="h-12 w-12 mx-auto text-muted-foreground" />
        <h1 className="text-2xl font-bold">Welcome to WePaie</h1>
        <p className="text-muted-foreground">
          To get started, please select a company or initialize demo data.
        </p>
      </div>

      {/* Error Alert */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Success Alert */}
      {success && (
        <Alert>
          <CheckCircle className="h-4 w-4" />
          <AlertDescription>{success}</AlertDescription>
        </Alert>
      )}



      {/* Available Companies */}
      {availableCompanies.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Building2 className="h-5 w-5" />
              Available Companies
            </CardTitle>
            <CardDescription>
              Select a company to continue.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            {isLoadingCompanies ? (
              <div className="flex items-center justify-center p-4">
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                <span>Loading companies...</span>
              </div>
            ) : (
              availableCompanies.map((company) => (
                <div 
                  key={company.id}
                  className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors"
                >
                  <div>
                    <h3 className="font-medium">{company.name}</h3>
                    <p className="text-sm text-muted-foreground">{company.email}</p>
                  </div>
                  <Button 
                    onClick={() => handleSelectCompany(company)}
                    variant="outline"
                    size="sm"
                  >
                    Select
                  </Button>
                </div>
              ))
            )}
          </CardContent>
        </Card>
      )}

      {/* Create New Company */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Plus className="h-5 w-5" />
            Créer une Nouvelle Entreprise
          </CardTitle>
          <CardDescription>
            Configurez une nouvelle entreprise à partir de zéro.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Button
            variant="outline"
            className="w-full"
            onClick={() => {
              // Navigate to admin page for company creation
              window.location.href = '/admin';
            }}
          >
            <Plus className="mr-2 h-4 w-4" />
            Créer une Nouvelle Entreprise
          </Button>
        </CardContent>
      </Card>
    </div>
  );
}
