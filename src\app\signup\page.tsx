"use client";

import type { Metadata } from 'next';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Building2 } from 'lucide-react';
import { useTranslation } from '@/context/i18nContext';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';

// export const metadata: Metadata = {
//   title: 'Sign Up', // Static title
// };

export default function SignupPage() {
  const { t } = useTranslation();
  return (
    <ProtectedRoute requireAuth={false} redirectTo="/dashboard">
      <div className="flex min-h-screen flex-col items-center justify-center bg-muted/40 p-4">
      <Card className="w-full max-w-md shadow-xl">
        <CardHeader className="text-center">
          <div className="mb-4 flex justify-center">
             <Building2 className="h-12 w-12 text-primary" />
          </div>
          <CardTitle className="font-headline text-3xl">{t('appName')}</CardTitle>
          <CardDescription>{t('signup.description')}</CardDescription>
        </CardHeader>
        <CardContent>
          <form className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="companyName">{t('companyName')}</Label>
              <Input id="companyName" type="text" placeholder="Your Company LLC" required />
            </div>
            <div className="space-y-2">
              <Label htmlFor="email">{t('email')}</Label>
              <Input id="email" type="email" placeholder="<EMAIL>" required />
            </div>
            <div className="space-y-2">
              <Label htmlFor="password">{t('password')}</Label>
              <Input id="password" type="password" required />
            </div>
             <div className="space-y-2">
              <Label htmlFor="confirmPassword">{t('confirmPassword')}</Label>
              <Input id="confirmPassword" type="password" required />
            </div>
            <Button type="submit" className="w-full">
              {t('signup')}
            </Button>
          </form>
          <div className="mt-6 text-center text-sm">
            {t('alreadyHaveAccount')}{' '}
            <Link href="/login" className="font-medium text-primary hover:underline">
              {t('login')}
            </Link>
          </div>
        </CardContent>
      </Card>
      </div>
    </ProtectedRoute>
  );
}
