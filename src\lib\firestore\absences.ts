import { BaseFirestoreService } from './base';
import type { AbsenceDocument, QueryOptions } from './types';
import type { Absence, AbsenceCategory } from '@/types';

/**
 * Absence service for managing absence data
 */
export class AbsenceService extends BaseFirestoreService<AbsenceDocument> {
  constructor() {
    super('absences');
  }

  /**
   * Create new absence
   */
  async createAbsence(
    absenceData: Omit<Absence, 'id'>, 
    companyId: string
  ): Promise<AbsenceDocument> {
    const data = {
      ...absenceData,
      status: 'pending' as const,
    };

    return this.create(data as any, companyId);
  }

  /**
   * Create multiple absences (bulk operation)
   */
  async createBulkAbsences(
    employeeIds: string[],
    absenceData: Omit<Absence, 'id' | 'employeeId'>,
    companyId: string
  ): Promise<AbsenceDocument[]> {
    const promises = employeeIds.map(employeeId => 
      this.createAbsence({ ...absenceData, employeeId }, companyId)
    );

    return Promise.all(promises);
  }

  /**
   * Update absence
   */
  async updateAbsence(
    id: string, 
    updates: Partial<Absence>, 
    companyId: string
  ): Promise<AbsenceDocument> {
    return this.update(id, updates as any, companyId);
  }

  /**
   * Approve absence
   */
  async approveAbsence(
    id: string, 
    approvedBy: string, 
    companyId: string
  ): Promise<AbsenceDocument> {
    return this.update(id, {
      status: 'approved',
      approvedBy,
      approvedAt: new Date(),
    } as any, companyId);
  }

  /**
   * Reject absence
   */
  async rejectAbsence(
    id: string, 
    rejectedBy: string, 
    rejectionReason: string, 
    companyId: string
  ): Promise<AbsenceDocument> {
    return this.update(id, {
      status: 'rejected',
      approvedBy: rejectedBy,
      approvedAt: new Date(),
      rejectionReason,
    } as any, companyId);
  }

  /**
   * Get absences for specific employee
   */
  async getAbsencesByEmployee(
    employeeId: string, 
    companyId: string, 
    options: QueryOptions = {}
  ): Promise<AbsenceDocument[]> {
    const whereClause = [
      { field: 'employeeId', operator: '==' as const, value: employeeId },
      ...(options.where || [])
    ];

    return this.getAll(companyId, { ...options, where: whereClause });
  }

  /**
   * Get absences by status
   */
  async getAbsencesByStatus(
    status: 'pending' | 'approved' | 'rejected', 
    companyId: string, 
    options: QueryOptions = {}
  ): Promise<AbsenceDocument[]> {
    const whereClause = [
      { field: 'status', operator: '==' as const, value: status },
      ...(options.where || [])
    ];

    return this.getAll(companyId, { ...options, where: whereClause });
  }

  /**
   * Get absences by category
   */
  async getAbsencesByCategory(
    category: AbsenceCategory, 
    companyId: string, 
    options: QueryOptions = {}
  ): Promise<AbsenceDocument[]> {
    const whereClause = [
      { field: 'category', operator: '==' as const, value: category },
      ...(options.where || [])
    ];

    return this.getAll(companyId, { ...options, where: whereClause });
  }

  /**
   * Get absences in date range
   */
  async getAbsencesInDateRange(
    startDate: Date, 
    endDate: Date, 
    companyId: string, 
    options: QueryOptions = {}
  ): Promise<AbsenceDocument[]> {
    const whereClause = [
      { field: 'startDate', operator: '>=' as const, value: startDate.toISOString() },
      { field: 'startDate', operator: '<=' as const, value: endDate.toISOString() },
      ...(options.where || [])
    ];

    return this.getAll(companyId, { ...options, where: whereClause });
  }

  /**
   * Get pending absences (for approval workflow)
   */
  async getPendingAbsences(companyId: string, options: QueryOptions = {}): Promise<AbsenceDocument[]> {
    return this.getAbsencesByStatus('pending', companyId, options);
  }

  /**
   * Get absence statistics
   */
  async getAbsenceStats(companyId: string, year?: number): Promise<{
    total: number;
    byStatus: Record<string, number>;
    byCategory: Record<AbsenceCategory, number>;
    byMonth: Record<string, number>;
    totalDays: number;
  }> {
    let absences = await this.getAll(companyId);

    // Filter by year if specified
    if (year) {
      absences = absences.filter(absence => {
        const absenceYear = new Date(absence.startDate).getFullYear();
        return absenceYear === year;
      });
    }

    const stats = {
      total: absences.length,
      byStatus: {} as Record<string, number>,
      byCategory: {} as Record<AbsenceCategory, number>,
      byMonth: {} as Record<string, number>,
      totalDays: 0,
    };

    absences.forEach(absence => {
      // Count by status
      stats.byStatus[absence.status] = (stats.byStatus[absence.status] || 0) + 1;

      // Count by category
      stats.byCategory[absence.category] = (stats.byCategory[absence.category] || 0) + 1;

      // Count by month
      const month = new Date(absence.startDate).toLocaleString('default', { month: 'long' });
      stats.byMonth[month] = (stats.byMonth[month] || 0) + 1;

      // Calculate total days
      const start = new Date(absence.startDate);
      const end = new Date(absence.endDate);
      const diffTime = Math.abs(end.getTime() - start.getTime());
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1; // +1 to include both start and end dates
      stats.totalDays += diffDays;
    });

    return stats;
  }

  /**
   * Check for overlapping absences for an employee
   */
  async checkOverlappingAbsences(
    employeeId: string,
    startDate: Date,
    endDate: Date,
    companyId: string,
    excludeId?: string
  ): Promise<AbsenceDocument[]> {
    const employeeAbsences = await this.getAbsencesByEmployee(employeeId, companyId);

    return employeeAbsences.filter(absence => {
      if (excludeId && absence.id === excludeId) {
        return false;
      }

      const absenceStart = new Date(absence.startDate);
      const absenceEnd = new Date(absence.endDate);

      // Check for overlap
      return (startDate <= absenceEnd && endDate >= absenceStart);
    });
  }

  /**
   * Convert AbsenceDocument to Absence (for compatibility with existing code)
   */
  toAbsence(absenceDoc: AbsenceDocument): Absence {
    const { 
      approvedBy, 
      approvedAt, 
      status, 
      rejectionReason, 
      createdAt, 
      updatedAt, 
      companyId, 
      ...absence 
    } = absenceDoc;
    
    return absence as Absence;
  }

  /**
   * Convert multiple AbsenceDocuments to Absences
   */
  toAbsences(absenceDocs: AbsenceDocument[]): Absence[] {
    return absenceDocs.map(doc => this.toAbsence(doc));
  }
}

// Export singleton instance
export const absenceService = new AbsenceService();
