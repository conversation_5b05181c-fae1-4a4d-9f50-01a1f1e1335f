{"appName": "WePaie", "login": "Connexion", "login.description": "Connectez-vous pour accéder à votre compte", "signup": "S'inscrire", "signup.description": "<PERSON><PERSON><PERSON> votre compte pour commencer", "dashboard": "Tableau de bord", "employees": "Employés", "timeManagement": "Gestion du temps", "settings": "Paramètres", "language": "<PERSON><PERSON>", "english": "<PERSON><PERSON><PERSON>", "french": "Français", "arabic": "<PERSON><PERSON>", "profileSettings": "Paramètres du profil", "companySettings": "Paramètres de l'entreprise", "userManagement": "Gestion des utilisateurs", "systemPreferences": "Préférences système", "email": "E-mail", "password": "Mot de passe", "forgotPassword": "Mot de passe oublié ?", "dontHaveAccount": "Vous n'avez pas de compte ?", "alreadyHaveAccount": "Vous avez déjà un compte ?", "companyName": "Nom de l'entreprise", "confirmPassword": "Confirmer le mot de passe", "saveChanges": "Enregistrer les modifications", "updatePassword": "Mettre à jour le mot de passe", "currentPassword": "Mot de passe actuel", "newPassword": "Nouveau mot de passe", "confirmNewPassword": "Confirmer le nouveau mot de passe", "logout": "Déconnexion", "profile": "Profil", "profile.yourProfile": "Votre Profil", "profile.description": "<PERSON><PERSON><PERSON> vos informations personnelles.", "fullName": "Nom complet", "emailAddress": "Adresse e-mail", "changePassword": "Changer le mot de passe", "nav.dashboard": "Tableau de bord", "nav.employees": "Employés", "nav.timeManagement": "Gestion du temps", "nav.admin": "Administration", "nav.settings": "Paramètres", "languageSettings.title": "Paramètres de langue", "languageSettings.description": "Choisissez votre langue préférée pour l'application.", "dashboard.totalEmployees": "Total Employés", "dashboard.totalEmployees.caption": "+2 depuis le mois dernier", "dashboard.pendingPayroll": "Paie en attente", "dashboard.pendingPayroll.caption": "Prochaine execution : 30 juillet", "dashboard.openPositions": "<PERSON><PERSON> ouverts", "dashboard.openPositions.caption": "2 nouveaux cette semaine", "dashboard.recentActivity": "Activité Récente", "dashboard.recentActivity.description": "Aperçu des actions récentes dans le système.", "dashboard.recentActivity.empty": "Aucune activité récente à afficher.", "form.label.firstName": "Prénom", "form.label.lastName": "Nom", "form.label.phoneNumber": "N° Téléphone", "form.label.address": "<PERSON><PERSON><PERSON>", "form.label.department": "Département", "form.label.service": "Service", "form.label.position": "Poste", "form.label.weeklySalary": "Salaire He<PERSON>do.", "form.label.hoursPerWeek": "Heures/Sem.", "form.label.hourlyRate": "<PERSON><PERSON>", "form.label.overtimeRate": "Taux Supp.", "form.label.hireDate": "Date d'embauche", "form.label.status": "Statut", "form.label.email": "Email", "form.placeholder.firstName": "<PERSON>", "form.placeholder.lastName": "<PERSON><PERSON>", "form.placeholder.address": "123 Rue de la Paix, 75000 Paris", "form.placeholder.department": "Ingénierie", "form.placeholder.service": "Développement Web", "form.placeholder.position": "Développeur Senior", "form.placeholder.pickDate": "Choisir une date", "form.placeholder.selectStatus": "Sélectionner statut", "status.active": "Actif", "status.inactive": "Inactif", "employeeForm.toast.addedTitle": "<PERSON><PERSON><PERSON><PERSON>", "employeeForm.toast.updatedTitle": "Employé Mis à Jour", "employeeForm.toast.successDescription_add": "{{name}} a été ajouté avec succès. (Simulé)", "employeeForm.toast.successDescription_edit": "{{name}} a été mis à jour avec succès. (Simulé)", "employeeForm.toast.errorTitle": "<PERSON><PERSON><PERSON>", "employeeForm.toast.errorDescription_add": "Échec de l'ajout de l'employé. Veuillez réessayer.", "employeeForm.toast.errorDescription_edit": "Échec de la mise à jour de l'employé. Veuillez réessayer.", "employeeForm.button.add": "<PERSON><PERSON>ter Employé", "employeeForm.button.save": "<PERSON><PERSON><PERSON><PERSON>", "employeeTable.searchPlaceholder": "Rechercher employés...", "employeeTable.filter": "<PERSON><PERSON><PERSON>", "employeeTable.addEmployee": "<PERSON><PERSON>ter Employé", "employeeTable.modal.addTitle": "Ajouter un nouvel employé", "employeeTable.modal.editTitle": "Modifier Employé", "employeeTable.toast.deletedTitle": "Employé Supprimé", "employeeTable.toast.deletedDescription": "L'employé {{name}} a été supprimé. (Si<PERSON><PERSON>)", "employeeTable.actionsLabel": "Actions", "employeeTable.edit": "Modifier", "employeeTable.delete": "<PERSON><PERSON><PERSON><PERSON>", "employeeTable.deleteDialog.title": "Êtes-vous sûr ?", "employeeTable.deleteDialog.description": "Cette action est irréversible. Cela supprimera définitivement l'employé {{name}}.", "employeeTable.deleteDialog.cancel": "Annuler", "employeeTable.deleteDialog.confirm": "<PERSON><PERSON><PERSON><PERSON>", "employeeTable.noEmployeesFound": "<PERSON><PERSON>n employé trouvé.", "table.column.name": "Nom", "table.column.email": "Email", "table.column.phone": "Téléphone", "table.column.department": "Département", "table.column.position": "Poste", "table.column.status": "Statut", "table.column.actions": "Actions", "absenceForm.toast.loggedTitle": "Absence Enregistrée", "absenceForm.toast.loggedDescription": "L'absence pour le(s) employé(s) sélectionné(s) a été enregistrée. (Simulé)", "absenceForm.label.employees": "Employé(s)", "absenceForm.placeholder.selectEmployee": "Sélectionner employé(s)", "absenceForm.description.selectEmployee": "Sélectionner un ou plusieurs employés.", "absenceForm.label.category": "<PERSON><PERSON><PERSON><PERSON> d'absence", "absenceForm.placeholder.selectCategory": "Sélectionner catégorie", "absenceForm.category.sickLeave": "<PERSON><PERSON><PERSON><PERSON>", "absenceForm.category.annualLeave": "<PERSON><PERSON>", "absenceForm.category.unpaidLeave": "Congé sans Solde", "absenceForm.category.other": "<PERSON><PERSON>", "absenceForm.label.startDate": "Date de Début", "absenceForm.label.endDate": "Date de Fin", "absenceForm.label.notes": "Notes (Optionnel)", "absenceForm.placeholder.notes": "<PERSON>son de l'absence, référence certificat médical, etc.", "absenceForm.button.logAbsence": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "overtimeForm.toast.loggedTitle": "Heures Supp. Enregistrées", "overtimeForm.toast.loggedDescription": "Les heures supplémentaires pour le(s) employé(s) sélectionné(s) ont été enregistrées. (Simulé)", "overtimeForm.label.employees": "Employé(s)", "overtimeForm.placeholder.selectEmployee": "Sélectionner employé(s)", "overtimeForm.description.selectEmployee": "Sélectionner un ou plusieurs employés.", "overtimeForm.label.date": "Date", "overtimeForm.label.hours": "<PERSON><PERSON>", "overtimeForm.label.category": "Catégorie Heures Supp.", "overtimeForm.placeholder.selectCategory": "Sélectionner catégorie", "overtimeForm.category.regular": "Normales", "overtimeForm.category.holiday": "<PERSON><PERSON>", "overtimeForm.category.specialProject": "Projet Spécial", "overtimeForm.label.reason": "Raison (Optionnel)", "overtimeForm.placeholder.reason": "Raison des heures supp., code projet, etc.", "overtimeForm.button.logOvertime": "Enregistrer Heures Supp.", "settings.language": "<PERSON><PERSON>", "settings.fontFamily": "Police de caractères", "settings.textSize": "<PERSON>lle du texte", "settings.fontStyle": "Style de police", "settings.theme": "Thème", "settings.displayMode": "Mode d'affichage", "settings.passwordManagement": "Gestion du mot de passe", "settings.applicationPreferences": "Préférences de l'application", "size.small": "<PERSON>", "size.medium": "<PERSON><PERSON><PERSON>", "size.large": "Grand", "style.normal": "Normal", "style.medium": "Medium", "style.bold": "Gras", "theme.default": "Défaut", "theme.nature": "Nature", "theme.corporate": "Entreprise", "theme.warm": "<PERSON><PERSON><PERSON><PERSON>", "mode.light": "<PERSON>", "mode.dark": "Sombre", "common.select": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}