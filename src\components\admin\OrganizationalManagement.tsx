"use client";

import { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { ServiceManagement } from './ServiceManagement';
import { PositionManagement } from './PositionManagement';
import { UserManagement } from './UserManagement';
import { CompanyManagement } from './CompanyManagement';
import { SystemSettings } from './SystemSettings';

import { Building2, Users, UserCog, Settings, RefreshCw, Filter, Calendar } from 'lucide-react';
import { useRBAC } from '@/context/RBACContext';
import { useAuth } from '@/context/AuthContext';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';

export function OrganizationalManagement() {
  const { hasPermission, isSuperAdmin, rbacUser } = useRBAC();
  const { user } = useAuth();
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Debug logging
  console.log('🔍 OrganizationalManagement: RBAC state:', {
    hasUserPermission: hasPermission('users.read'),
    isSuperAdmin: isSuperAdmin(),
    rbacUser: rbacUser ? { id: rbacUser.id, email: rbacUser.email, role: rbacUser.role } : null,
    authUser: user ? { uid: user.uid, email: user.email } : null
  });

  const refreshToken = async () => {
    if (!user) return;

    setIsRefreshing(true);
    try {
      console.log('🔄 Refreshing Firebase Auth token...');
      await user.getIdToken(true);
      console.log('✅ Token refreshed, reloading page...');
      window.location.reload();
    } catch (error) {
      console.error('❌ Token refresh error:', error);
    } finally {
      setIsRefreshing(false);
    }
  };

  // Determine which tabs to show based on permissions
  const showUserManagement = hasPermission('users.read');
  const showCompanyManagement = isSuperAdmin();
  const showSystemSettings = isSuperAdmin();

  // Calculate tab count based on permissions
  let tabCount = 1; // Base: application data
  if (showUserManagement) tabCount++;
  if (showCompanyManagement) tabCount++;
  if (showSystemSettings) tabCount++;

  return (
    <div className="w-full space-y-4">
      {/* Full-width header to establish consistent page width */}
      <div className="w-full border-b border-border pb-4">
        <h1 className="text-2xl font-bold">Administration</h1>
        <p className="text-muted-foreground">Gestion des entreprises, utilisateurs et données d'application</p>
      </div>

      {!isSuperAdmin() && (
        <div className="flex justify-end">
          <Button
            onClick={refreshToken}
            disabled={isRefreshing}
            variant="outline"
            size="sm"
            className="flex items-center gap-2"
          >
            <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
            {isRefreshing ? 'Actualisation...' : 'Actualiser les permissions'}
          </Button>
        </div>
      )}

      <Tabs defaultValue={showCompanyManagement ? "companies" : showUserManagement ? "users" : "appdata"} className="w-full">
        <TabsList className={`grid w-full grid-cols-${tabCount} md:w-auto`}>
          {showCompanyManagement && (
            <TabsTrigger value="companies" className="flex items-center gap-2">
              <Building2 className="h-4 w-4" />
              Entreprises
            </TabsTrigger>
          )}
          {showUserManagement && (
            <TabsTrigger value="users" className="flex items-center gap-2">
              <UserCog className="h-4 w-4" />
              Gestion des Utilisateurs
            </TabsTrigger>
          )}
          <TabsTrigger value="appdata" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            Données d'Application
          </TabsTrigger>
          {showSystemSettings && (
            <TabsTrigger value="system" className="flex items-center gap-2">
              <Settings className="h-4 w-4" />
              Système
            </TabsTrigger>
          )}
        </TabsList>

        {showCompanyManagement && (
          <TabsContent value="companies" className="space-y-4">
            <Card>
              <CardContent className="p-6">
                <CompanyManagement />
              </CardContent>
            </Card>
          </TabsContent>
        )}

        {showUserManagement && (
          <TabsContent value="users" className="space-y-4">
            <Card>
              <CardContent className="p-6">
                <UserManagement />
              </CardContent>
            </Card>
          </TabsContent>
        )}

        <TabsContent value="appdata" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <Card>
              <CardContent className="p-6">
                <ServiceManagement />
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <PositionManagement />
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {showSystemSettings && (
          <TabsContent value="system" className="space-y-4">
            <Card>
              <CardContent className="p-6">
                <SystemSettings />
              </CardContent>
            </Card>
          </TabsContent>
        )}
      </Tabs>
    </div>
  );
}
