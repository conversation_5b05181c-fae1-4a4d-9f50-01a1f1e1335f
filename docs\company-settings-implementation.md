# Company Settings Management System

This document describes the comprehensive company settings management system implemented for WePaie.

## Overview

The company settings system provides a complete interface for managing company information, work schedules, localization preferences, and system configuration. It follows all established patterns from the WePaie codebase and integrates seamlessly with the existing Firebase/Firestore infrastructure.

## Architecture

### Component Structure

```
src/
├── components/settings/
│   ├── CompanySettings.tsx          # Main company settings component (client)
│   ├── ProfileSettingsClient.tsx   # Profile settings client component
│   └── SettingsLayout.tsx           # Settings navigation layout (client)
├── app/settings/
│   ├── layout.tsx                   # Settings layout wrapper (server)
│   ├── page.tsx                     # Settings index (redirects to profile)
│   ├── company/page.tsx             # Company settings page (server)
│   ├── profile/page.tsx             # Profile settings page (server)
│   ├── users/page.tsx               # User management page (server)
│   └── system/page.tsx              # System preferences page (server)
```

### Server/Client Component Architecture

The settings system follows Next.js 13+ App Router best practices by separating server and client components:

- **Server Components**: Handle metadata exports, static rendering, and SEO
- **Client Components**: Handle interactive functionality, hooks, and state management

**Example Pattern:**
```typescript
// Server Component (page.tsx)
import { Metadata } from 'next';
import { ClientComponent } from '@/components/ClientComponent';

export const metadata: Metadata = {
  title: 'Page Title',
  description: 'Page description',
};

export default function Page() {
  return <ClientComponent />;
}

// Client Component
"use client";
import { useTranslation } from '@/context/i18nContext';

export function ClientComponent() {
  const { t } = useTranslation();
  // Interactive functionality here
}
```

### Key Features

#### 1. **Permission-Based Access Control**
- Only administrators can access company settings
- Uses `usePermissions()` hook for role checking
- Displays appropriate error messages for unauthorized access
- Admin-only navigation items are clearly marked

#### 2. **Comprehensive Form Management**
- React Hook Form with Zod validation
- Real-time form validation and error display
- Unsaved changes detection and warnings
- Cancel/reset functionality

#### 3. **Firestore Integration**
- Uses established `useFirestoreDocument` and `useFirestoreMutation` hooks
- Respects multi-tenant security model
- Proper error handling and loading states
- Optimistic updates with rollback on failure

#### 4. **Settings Categories**

**Basic Information:**
- Company name (required)
- Email address (required, validated)
- Phone number (optional)
- Physical address (optional)

**Work Schedule:**
- Working days selection (checkboxes for each day)
- Working hours (start/end time inputs)
- Minimum one working day required

**Localization:**
- Timezone selection (dropdown with common timezones)
- Currency selection (dropdown with major currencies)
- Date format selection (multiple format options)

**System Information (Read-only):**
- Subscription plan and status
- Company ID
- Creation and last update timestamps

## Implementation Details

### Security & Permissions

```typescript
// Permission check in CompanySettings component
if (!isAdmin()) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-destructive">
          <Shield className="h-5 w-5" />
          Access Denied
        </CardTitle>
      </CardHeader>
    </Card>
  );
}
```

### Form Validation Schema

```typescript
const companySettingsSchema = z.object({
  name: z.string().min(1, 'Company name is required').max(100, 'Company name too long'),
  email: z.string().email('Invalid email address'),
  phone: z.string().optional(),
  address: z.string().optional(),
  workingDays: z.array(z.number().min(0).max(6)).min(1, 'At least one working day required'),
  workingHours: z.object({
    start: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Invalid time format'),
    end: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Invalid time format'),
  }),
  timezone: z.string().min(1, 'Timezone is required'),
  currency: z.string().min(1, 'Currency is required'),
  dateFormat: z.string().min(1, 'Date format is required'),
});
```

### Data Flow

1. **Load Company Data**: Uses `useFirestoreDocument` to fetch current company settings
2. **Form Initialization**: Populates form with existing data when loaded
3. **Real-time Validation**: Validates input as user types
4. **Change Detection**: Tracks unsaved changes and shows warnings
5. **Save Operation**: Updates Firestore and refreshes company context
6. **Error Handling**: Shows appropriate error messages and allows retry

### Navigation Structure

The settings system includes a comprehensive navigation layout:

- **Profile Settings**: Personal user information and preferences
- **Company Settings**: Company-wide configuration (admin only)
- **User Management**: User account management (admin only, placeholder)
- **System Preferences**: System-wide settings (admin only, placeholder)

### Responsive Design

- Mobile-first responsive layout
- Collapsible navigation on smaller screens
- Proper form layout for different screen sizes
- Accessible keyboard navigation

## Usage Examples

### Accessing Company Settings

```typescript
// Navigate to company settings
router.push('/settings/company');

// Or use direct link
<Link href="/settings/company">Company Settings</Link>
```

### Checking Permissions

```typescript
import { usePermissions } from '@/context/FirestoreContext';

function MyComponent() {
  const { isAdmin } = usePermissions();
  
  if (isAdmin()) {
    // Show admin-only content
  }
}
```

### Using Settings Layout

```typescript
// In any settings page
export default function MySettingsPage() {
  return (
    <div className="space-y-6">
      <h1>My Settings</h1>
      {/* Settings content */}
    </div>
  );
}
```

## Testing

### Manual Testing Checklist

- [ ] **Permission Enforcement**: Non-admin users see access denied message
- [ ] **Form Validation**: All validation rules work correctly
- [ ] **Data Persistence**: Changes save correctly to Firestore
- [ ] **Error Handling**: Network errors and validation errors display properly
- [ ] **Responsive Design**: Works on mobile, tablet, and desktop
- [ ] **Navigation**: All settings navigation links work correctly
- [ ] **Unsaved Changes**: Warning appears when leaving with unsaved changes

### Test Scenarios

1. **Admin User Flow**:
   - Login as admin
   - Navigate to company settings
   - Modify various fields
   - Save changes
   - Verify changes persist after page refresh

2. **Non-Admin User Flow**:
   - Login as non-admin user
   - Navigate to settings
   - Verify company settings shows access denied
   - Verify admin-only navigation items are hidden

3. **Validation Testing**:
   - Try to save with empty required fields
   - Enter invalid email format
   - Enter invalid time format
   - Uncheck all working days

## Future Enhancements

### Planned Features

1. **User Management**: Complete user account management interface
2. **System Preferences**: Advanced system configuration options
3. **Audit Logging**: Track all settings changes with timestamps
4. **Backup/Restore**: Export and import company settings
5. **Advanced Permissions**: Granular permission system beyond admin/user

### Integration Opportunities

1. **Email Integration**: Configure SMTP settings for notifications
2. **Calendar Integration**: Sync working hours with calendar systems
3. **Payroll Integration**: Connect with external payroll systems
4. **Reporting**: Generate settings reports and change logs

## Troubleshooting

### Common Issues

1. **Access Denied**: Ensure user has admin role in Firestore
2. **Form Not Saving**: Check Firebase security rules and network connectivity
3. **Validation Errors**: Verify all required fields are filled correctly
4. **Navigation Issues**: Ensure all route files exist and are properly configured
5. **Metadata Export Error**: "You are attempting to export 'metadata' from a component marked with 'use client'"
   - **Cause**: Trying to export static metadata from a client component
   - **Solution**: Split into server component (handles metadata) and client component (handles interactivity)
   - **Example**: See ProfileSettingsClient.tsx pattern

### Debug Steps

1. Check browser console for JavaScript errors
2. Verify Firebase connection in Network tab
3. Check Firestore security rules in Firebase Console
4. Verify user permissions in FirestoreContext

## Security Considerations

- All company settings operations require admin authentication
- Multi-tenant data isolation ensures companies can't access each other's settings
- Form validation prevents malicious input
- Firestore security rules provide server-side validation
- Sensitive information (like company ID) is displayed but not editable

## Performance Optimizations

- Lazy loading of settings components
- Optimistic updates for better user experience
- Efficient form state management with React Hook Form
- Minimal re-renders with proper dependency arrays
- Cached company data in FirestoreContext
