"use client";

import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { themes, type ThemeName, type ThemeMode, getTheme } from '../themes';

interface ThemeContextType {
  currentThemeName: ThemeName;
  currentMode: ThemeMode;
  isDarkMode: boolean;
  setTheme: (themeName: ThemeName) => void;
  setMode: (mode: ThemeMode) => void;
  toggleMode: () => void;
  themeStyles: Record<string, string>;
  previewTheme: (themeName: ThemeName, mode?: ThemeMode) => void;
  resetPreview: () => void;
  // Compatibilité avec l'ancien code
  baseTheme: ThemeName;
  mode: ThemeMode;
  setBaseTheme: (theme: ThemeName) => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export function ThemeProvider({ children }: { children: React.ReactNode }) {
  // Récupérer les préférences de l'utilisateur ou valeurs par défaut
  const [currentThemeName, setCurrentThemeName] = useState<ThemeName>(() => {
    if (typeof window !== 'undefined') {
      const stored = localStorage.getItem('wepaie_theme_name') as ThemeName | null;
      return stored && stored in themes ? stored : 'default';
    }
    return 'default';
  });

  const [currentMode, setCurrentMode] = useState<ThemeMode>(() => {
    if (typeof window !== 'undefined') {
      const stored = localStorage.getItem('wepaie_theme_mode') as ThemeMode | null;
      if (stored && ['light', 'dark'].includes(stored)) {
        return stored;
      }
      // Check system preference for dark mode
      if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
        return 'dark';
      }
    }
    return 'light';
  });

  const [themeStyles, setThemeStyles] = useState<Record<string, string>>({});

  // Mettre à jour les styles du thème quand le thème ou le mode change
  useEffect(() => {
    const selectedTheme = getTheme(currentThemeName, currentMode);
    setThemeStyles(selectedTheme);
  }, [currentThemeName, currentMode]);

  // Appliquer les variables CSS au document.documentElement avec transitions fluides
  useEffect(() => {
    if (typeof window !== 'undefined') {
      // Ensure smooth transitions are enabled
      document.documentElement.style.setProperty('transition', 'all 0.3s ease');

      // Apply all theme variables
      for (const [property, value] of Object.entries(themeStyles)) {
        document.documentElement.style.setProperty(property, value);
      }

      // Log theme application for debugging
      console.log('🎨 ThemeContext: Applied theme variables', {
        theme: currentThemeName,
        mode: currentMode,
        variableCount: Object.keys(themeStyles).length
      });
    }
  }, [themeStyles, currentThemeName, currentMode]);

  // Sauvegarder les préférences dans localStorage
  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('wepaie_theme_name', currentThemeName);
      localStorage.setItem('wepaie_theme_mode', currentMode);
    }
  }, [currentThemeName, currentMode]);

  const setTheme = useCallback((themeName: ThemeName) => {
    setCurrentThemeName(themeName);
  }, []);

  const setMode = useCallback((mode: ThemeMode) => {
    setCurrentMode(mode);
  }, []);

  const toggleMode = useCallback(() => {
    setCurrentMode(prevMode => (prevMode === 'light' ? 'dark' : 'light'));
  }, []);

  // Preview functionality for real-time theme testing
  const previewTheme = useCallback((themeName: ThemeName, mode?: ThemeMode) => {
    const previewMode = mode || currentMode;
    const previewStyles = getTheme(themeName, previewMode);

    if (typeof window !== 'undefined') {
      for (const [property, value] of Object.entries(previewStyles)) {
        document.documentElement.style.setProperty(property, value);
      }
    }
  }, [currentMode]);

  const resetPreview = useCallback(() => {
    if (typeof window !== 'undefined') {
      for (const [property, value] of Object.entries(themeStyles)) {
        document.documentElement.style.setProperty(property, value);
      }
    }
  }, [themeStyles]);

  const isDarkMode = currentMode === 'dark';

  const value = {
    currentThemeName,
    currentMode,
    isDarkMode,
    setTheme,
    setMode,
    toggleMode,
    themeStyles,
    previewTheme,
    resetPreview,
    // Compatibilité avec l'ancien code
    baseTheme: currentThemeName,
    mode: currentMode,
    setBaseTheme: setTheme,
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
}

export function useTheme() {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
}

// Exports pour la compatibilité avec l'ancien code
export type BaseTheme = ThemeName;
export { type ThemeMode, type ThemeName };
