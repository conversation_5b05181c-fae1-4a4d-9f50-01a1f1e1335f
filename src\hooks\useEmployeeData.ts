"use client";

import { useState, useEffect, useCallback } from 'react';
import { useCompanyId, useUserId } from '@/context/FirestoreContext';
import { useCompany } from '@/context/CompanyContext';
import { employeeService } from '@/lib/firestore';
import type { EmployeeDocument, QueryOptions } from '@/lib/firestore/types';
import type { Employee } from '@/types';

/**
 * Employee Data Hooks
 * 
 * Custom hooks for managing employee data following established patterns.
 * Provides CRUD operations with proper loading states, error handling, and caching.
 */

interface UseEmployeesOptions extends QueryOptions {
  enabled?: boolean;
  refetchInterval?: number;
}

interface UseEmployeesResult {
  employees: EmployeeDocument[];
  isLoading: boolean;
  error: Error | null;
  refetch: () => Promise<void>;
  isEmpty: boolean;
  total: number;
}

/**
 * Hook for fetching all employees
 */
export function useEmployees(options: UseEmployeesOptions = {}): UseEmployeesResult {
  const { selectedCompany } = useCompany();
  const [employees, setEmployees] = useState<EmployeeDocument[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  const { enabled = true, refetchInterval, ...queryOptions } = options;
  const companyId = selectedCompany?.id;

  const fetchEmployees = useCallback(async () => {
    if (!companyId || !enabled) {
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);
      setError(null);
      const data = await employeeService.getActiveEmployees(companyId, queryOptions);
      setEmployees(data);
    } catch (err) {
      console.error('Error fetching employees:', err);
      setError(err instanceof Error ? err : new Error('Failed to fetch employees'));
    } finally {
      setIsLoading(false);
    }
  }, [companyId, enabled, queryOptions?.orderBy?.field, queryOptions?.orderBy?.direction, queryOptions?.limit, queryOptions?.startAfter]);

  useEffect(() => {
    fetchEmployees();
  }, [fetchEmployees]);

  // Auto-refetch interval
  useEffect(() => {
    if (refetchInterval && enabled) {
      const interval = setInterval(fetchEmployees, refetchInterval);
      return () => clearInterval(interval);
    }
  }, [fetchEmployees, refetchInterval, enabled]);

  return {
    employees,
    isLoading,
    error,
    refetch: fetchEmployees,
    isEmpty: employees.length === 0,
    total: employees.length,
  };
}

interface UseEmployeeResult {
  employee: EmployeeDocument | null;
  isLoading: boolean;
  error: Error | null;
  refetch: () => Promise<void>;
}

/**
 * Hook for fetching a single employee by ID
 */
export function useEmployee(employeeId: string | null, enabled = true): UseEmployeeResult {
  const companyId = useCompanyId();
  const [employee, setEmployee] = useState<EmployeeDocument | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  const fetchEmployee = useCallback(async () => {
    if (!companyId || !employeeId || !enabled) {
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);
      setError(null);
      const data = await employeeService.getById(employeeId, companyId);
      setEmployee(data);
    } catch (err) {
      console.error('Error fetching employee:', err);
      setError(err instanceof Error ? err : new Error('Failed to fetch employee'));
    } finally {
      setIsLoading(false);
    }
  }, [companyId, employeeId, enabled]);

  useEffect(() => {
    fetchEmployee();
  }, [fetchEmployee]);

  return {
    employee,
    isLoading,
    error,
    refetch: fetchEmployee,
  };
}

interface UseCreateEmployeeResult {
  createEmployee: (employeeData: Omit<Employee, 'id'>) => Promise<EmployeeDocument>;
  isLoading: boolean;
  error: Error | null;
}

/**
 * Hook for creating employees
 */
export function useCreateEmployee(): UseCreateEmployeeResult {
  const { selectedCompany } = useCompany();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const createEmployee = useCallback(async (employeeData: Omit<Employee, 'id'>): Promise<EmployeeDocument> => {
    if (!selectedCompany?.id) {
      throw new Error('Company selection is required. Please select a company first.');
    }

    try {
      setIsLoading(true);
      setError(null);

      const newEmployee = await employeeService.createEmployee(employeeData, selectedCompany.id);
      return newEmployee;
    } catch (err) {
      console.error('Error creating employee:', err);
      const error = err instanceof Error ? err : new Error('Failed to create employee');
      setError(error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [selectedCompany?.id]);

  return {
    createEmployee,
    isLoading,
    error,
  };
}

interface UseUpdateEmployeeResult {
  updateEmployee: (employeeId: string, updates: Partial<Employee>) => Promise<EmployeeDocument>;
  isLoading: boolean;
  error: Error | null;
}

/**
 * Hook for updating employees
 */
export function useUpdateEmployee(): UseUpdateEmployeeResult {
  const companyId = useCompanyId();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const updateEmployee = useCallback(async (
    employeeId: string, 
    updates: Partial<Employee>
  ): Promise<EmployeeDocument> => {
    if (!companyId) {
      throw new Error('Company ID is required');
    }

    try {
      setIsLoading(true);
      setError(null);

      const updatedEmployee = await employeeService.updateEmployee(employeeId, updates, companyId);
      return updatedEmployee;
    } catch (err) {
      console.error('Error updating employee:', err);
      const error = err instanceof Error ? err : new Error('Failed to update employee');
      setError(error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [companyId]);

  return {
    updateEmployee,
    isLoading,
    error,
  };
}

interface UseDeleteEmployeeResult {
  deleteEmployee: (employeeId: string) => Promise<void>;
  isLoading: boolean;
  error: Error | null;
}

/**
 * Hook for deleting employees (soft delete)
 */
export function useDeleteEmployee(): UseDeleteEmployeeResult {
  const companyId = useCompanyId();
  const userId = useUserId();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const deleteEmployee = useCallback(async (employeeId: string): Promise<void> => {
    if (!companyId || !userId) {
      throw new Error('Company ID and User ID are required');
    }

    try {
      setIsLoading(true);
      setError(null);

      await employeeService.softDeleteEmployee(employeeId, companyId, userId);
    } catch (err) {
      console.error('Error deleting employee:', err);
      const error = err instanceof Error ? err : new Error('Failed to delete employee');
      setError(error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [companyId, userId]);

  return {
    deleteEmployee,
    isLoading,
    error,
  };
}

interface UseEmployeeSearchResult {
  searchEmployees: (searchTerm: string) => Promise<EmployeeDocument[]>;
  isLoading: boolean;
  error: Error | null;
  results: EmployeeDocument[];
}

/**
 * Hook for searching employees
 */
export function useEmployeeSearch(): UseEmployeeSearchResult {
  const companyId = useCompanyId();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [results, setResults] = useState<EmployeeDocument[]>([]);

  const searchEmployees = useCallback(async (searchTerm: string): Promise<EmployeeDocument[]> => {
    if (!companyId) {
      throw new Error('Company ID is required');
    }

    try {
      setIsLoading(true);
      setError(null);

      const searchResults = await employeeService.searchEmployees(companyId, searchTerm);
      setResults(searchResults);
      return searchResults;
    } catch (err) {
      console.error('Error searching employees:', err);
      const error = err instanceof Error ? err : new Error('Failed to search employees');
      setError(error);
      setResults([]);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [companyId]);

  return {
    searchEmployees,
    isLoading,
    error,
    results,
  };
}

interface UseEmployeeStatsResult {
  stats: {
    total: number;
    active: number;
    inactive: number;
    byDepartment: Record<string, number>;
  } | null;
  isLoading: boolean;
  error: Error | null;
  refetch: () => Promise<void>;
}

/**
 * Hook for fetching employee statistics
 */
export function useEmployeeStats(): UseEmployeeStatsResult {
  const companyId = useCompanyId();
  const [stats, setStats] = useState<UseEmployeeStatsResult['stats']>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  const fetchStats = useCallback(async () => {
    if (!companyId) {
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);
      setError(null);
      const data = await employeeService.getEmployeeStats(companyId);
      setStats(data);
    } catch (err) {
      console.error('Error fetching employee stats:', err);
      setError(err instanceof Error ? err : new Error('Failed to fetch employee statistics'));
    } finally {
      setIsLoading(false);
    }
  }, [companyId]);

  useEffect(() => {
    fetchStats();
  }, [fetchStats]);

  return {
    stats,
    isLoading,
    error,
    refetch: fetchStats,
  };
}
