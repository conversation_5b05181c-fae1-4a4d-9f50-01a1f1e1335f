"use client";

import React, { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/context/AuthContext';
import { usePermissions, useFirestore } from '@/context/FirestoreContext';
import { useRBAC } from '@/context/RBACContext';
import { LoadingScreen } from '@/components/ui/loading-screen';
import type { Permission, UserRole } from '@/lib/rbac/types';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requireAuth?: boolean;
  requireAdmin?: boolean;
  requiredRole?: UserRole;
  requiredPermissions?: Permission[];
  redirectTo?: string;
}

/**
 * Protected Route Component
 * 
 * Handles authentication requirements for routes.
 * Can be used to protect routes that require authentication
 * or redirect authenticated users away from auth pages.
 */
export function ProtectedRoute({
  children,
  requireAuth = true,
  requireAdmin = false,
  requiredRole,
  requiredPermissions,
  redirectTo
}: ProtectedRouteProps) {
  const router = useRouter();
  const { firebaseUser, isLoading, isInitialized } = useAuth();
  const { isAdmin } = usePermissions();
  const firestoreContext = useFirestore();
  const rbac = useRBAC();

  useEffect(() => {
    if (!isInitialized || !rbac.isInitialized) return;

    if (requireAuth && !firebaseUser) {
      // User needs to be authenticated but isn't
      router.push(redirectTo || '/login');
    } else if (!requireAuth && firebaseUser && redirectTo) {
      // User is authenticated but on a page that doesn't require auth (like login)
      // Only redirect if a specific redirectTo is provided
      router.push(redirectTo);
    } else if (requireAdmin && firebaseUser && !rbac.isAdmin()) {
      // User is authenticated but doesn't have admin permissions
      router.push(redirectTo || '/dashboard');
    } else if (requiredRole && firebaseUser && !rbac.hasRoleOrHigher(requiredRole)) {
      // User doesn't have required role
      router.push(redirectTo || '/dashboard');
    } else if (requiredPermissions && firebaseUser && !rbac.hasAllPermissions(requiredPermissions)) {
      // User doesn't have required permissions
      router.push(redirectTo || '/dashboard');
    }
  }, [firebaseUser, isInitialized, rbac.isInitialized, requireAuth, requireAdmin, requiredRole, requiredPermissions, redirectTo, router, rbac]);

  // Determine if we should show loading screen
  const shouldShowLoading = () => {
    // Only show loading if auth is not initialized
    if (!isInitialized) return true;

    // For authenticated users, only wait for RBAC to initialize if we need admin permissions
    if (firebaseUser && requireAdmin && !rbac.isInitialized) return true;

    return false;
  };

  // Show loading while auth is initializing
  if (shouldShowLoading()) {
    return (
      <LoadingScreen
        message="Vérification de l'authentification..."
        submessage="Veuillez patienter pendant que nous vérifions vos identifiants"
      />
    );
  }

  // Show loading while redirecting
  if (requireAuth && !firebaseUser) {
    return (
      <LoadingScreen
        message="Redirection vers la connexion..."
        submessage="Vous devez vous connecter pour accéder à cette page"
      />
    );
  }



  // Show loading while redirecting authenticated users from login page
  if (!requireAuth && firebaseUser && redirectTo) {
    return (
      <LoadingScreen
        message="Redirection..."
        submessage="Vous êtes déjà connecté"
      />
    );
  }

  // Check admin permissions (with fallback for manually created users)
  if (requireAdmin && firebaseUser && rbac.isInitialized) {
    const isAdminUser = rbac.isAdmin() ||
                       firebaseUser.email === '<EMAIL>' ||
                       firebaseUser.email === '<EMAIL>';

    if (!isAdminUser) {
      return (
        <LoadingScreen
          message="Accès refusé"
          submessage="Vous n'avez pas l'autorisation d'accéder à cette page"
        />
      );
    }
  }

  // Check role requirements (with fallback)
  if (requiredRole && firebaseUser && rbac.isInitialized) {
    const hasRequiredRole = rbac.hasRoleOrHigher(requiredRole) ||
                           (requiredRole === 'super_admin' && firebaseUser.email === '<EMAIL>') ||
                           (requiredRole === 'company_admin' && firebaseUser.email === '<EMAIL>');

    if (!hasRequiredRole) {
      return (
        <LoadingScreen
          message="Accès refusé"
          submessage="Votre rôle ne vous permet pas d'accéder à cette page"
        />
      );
    }
  }

  // Check permission requirements (with fallback for superuser)
  if (requiredPermissions && firebaseUser && rbac.isInitialized) {
    const hasPermissions = rbac.hasAllPermissions(requiredPermissions) ||
                          firebaseUser.email === '<EMAIL>';

    if (!hasPermissions) {
      return (
        <LoadingScreen
          message="Accès refusé"
          submessage="Vous n'avez pas les permissions nécessaires pour accéder à cette page"
        />
      );
    }
  }

  return <>{children}</>;
}
