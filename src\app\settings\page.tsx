import type { Metada<PERSON> } from 'next';
import AppShell from '@/components/layout/AppShell';
import { SettingsClient } from '@/components/settings/SettingsClient';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';

export const metadata: Metadata = {
  title: 'Settings - WePaie',
  description: 'Manage your personal preferences and account settings.',
};

/**
 * Settings Page
 *
 * Simplified settings page with only personal preferences.
 * Company and system management moved to /admin page.
 */
export default function SettingsPage() {
  return (
    <ProtectedRoute requireAuth={true}>
      <AppShell pageTitle="Paramètres">
        <SettingsClient />
      </AppShell>
    </ProtectedRoute>
  );
}
