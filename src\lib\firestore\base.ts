import {
  collection,
  doc,
  getDoc,
  getDocs,
  addDoc,
  setDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  limit,
  startAfter,
  Timestamp,
  DocumentReference,
  QueryConstraint,
  DocumentSnapshot,
  QueryDocumentSnapshot,
} from 'firebase/firestore';
import { db } from '@/lib/firebase';
import type { FirestoreDocument, QueryOptions, PaginatedResponse, FirestoreError } from './types';

/**
 * Base Firestore service class with common CRUD operations
 */
export abstract class BaseFirestoreService<T extends FirestoreDocument> {
  protected collectionName: string;

  constructor(collectionName: string) {
    this.collectionName = collectionName;
  }

  /**
   * Get collection reference
   */
  protected getCollection() {
    return collection(db, this.collectionName);
  }

  /**
   * Get document reference
   */
  protected getDocRef(id: string) {
    return doc(db, this.collectionName, id);
  }

  /**
   * Convert Firestore document to typed object
   */
  protected docToObject(doc: DocumentSnapshot | QueryDocumentSnapshot): T {
    const data = doc.data();
    if (!data) {
      throw new Error(`Document ${doc.id} not found`);
    }

    return {
      id: doc.id,
      ...data,
      // Convert Firestore Timestamps to Date objects
      createdAt: data.createdAt?.toDate() || new Date(),
      updatedAt: data.updatedAt?.toDate() || new Date(),
    } as T;
  }

  /**
   * Convert typed object to Firestore document data
   */
  protected objectToDoc(obj: Partial<T>): any {
    const { id, ...data } = obj as any;

    // Convert Date objects to Firestore Timestamps and remove undefined values
    const processedData: any = {};

    for (const [key, value] of Object.entries(data)) {
      if (value !== undefined) {
        if (value instanceof Date) {
          processedData[key] = Timestamp.fromDate(value);
        } else {
          processedData[key] = value;
        }
      }
    }

    return processedData;
  }

  /**
   * Build query constraints from options
   */
  protected buildQueryConstraints(options: QueryOptions = {}): QueryConstraint[] {
    const constraints: QueryConstraint[] = [];

    // Add where clauses
    if (options.where) {
      options.where.forEach(({ field, operator, value }) => {
        constraints.push(where(field, operator, value));
      });
    }

    // Add order by
    if (options.orderBy) {
      constraints.push(orderBy(options.orderBy.field, options.orderBy.direction));
    }

    // Add limit
    if (options.limit) {
      constraints.push(limit(options.limit));
    }

    // Add pagination
    if (options.startAfter) {
      constraints.push(startAfter(options.startAfter));
    }

    return constraints;
  }

  /**
   * Handle Firestore errors
   */
  protected handleError(error: any): FirestoreError {
    console.error(`Firestore error in ${this.collectionName}:`, error);
    
    return {
      code: error.code || 'unknown',
      message: error.message || 'An unknown error occurred',
      details: error,
    };
  }

  /**
   * Get document by ID
   */
  async getById(id: string, companyId: string): Promise<T | null> {
    try {
      const docRef = this.getDocRef(id);
      const docSnap = await getDoc(docRef);

      if (!docSnap.exists()) {
        return null;
      }

      const data = this.docToObject(docSnap);
      
      // Check company access
      if (data.companyId !== companyId) {
        throw new Error('Access denied: Document belongs to different company');
      }

      return data;
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * Get all documents with optional filtering
   */
  async getAll(companyId: string, options: QueryOptions = {}): Promise<T[]> {
    try {
      // Always filter by company
      const companyFilter = { field: 'companyId', operator: '==' as const, value: companyId };
      const whereClause = options.where ? [companyFilter, ...options.where] : [companyFilter];
      
      const constraints = this.buildQueryConstraints({ ...options, where: whereClause });
      const q = query(this.getCollection(), ...constraints);
      const querySnapshot = await getDocs(q);

      return querySnapshot.docs.map(doc => this.docToObject(doc));
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * Get paginated documents
   */
  async getPaginated(companyId: string, options: QueryOptions = {}): Promise<PaginatedResponse<T>> {
    try {
      // Always filter by company
      const companyFilter = { field: 'companyId', operator: '==' as const, value: companyId };
      const whereClause = options.where ? [companyFilter, ...options.where] : [companyFilter];
      
      const constraints = this.buildQueryConstraints({ ...options, where: whereClause });
      const q = query(this.getCollection(), ...constraints);
      const querySnapshot = await getDocs(q);

      const data = querySnapshot.docs.map(doc => this.docToObject(doc));
      const hasMore = data.length === (options.limit || 0);
      const lastDoc = querySnapshot.docs[querySnapshot.docs.length - 1];

      return {
        data,
        hasMore,
        lastDoc,
      };
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * Create new document
   */
  async create(
    data: Omit<T, 'id' | 'createdAt' | 'updatedAt'>,
    companyId: string,
    customId?: string
  ): Promise<T> {
    try {
      console.log(`🔍 Creating document in ${this.collectionName}:`, {
        companyId,
        customId,
        dataKeys: Object.keys(data),
        data: data
      });

      const now = new Date();
      const docData = this.objectToDoc({
        ...data,
        companyId,
        createdAt: now,
        updatedAt: now,
      } as T);

      console.log('🔍 Prepared document data:', docData);

      let docRef: DocumentReference;

      if (customId) {
        // Use custom ID
        docRef = doc(this.getCollection(), customId);
        console.log('🔍 Using custom ID:', customId);
        await setDoc(docRef, docData);
      } else {
        // Auto-generate ID
        console.log('🔍 Auto-generating ID');
        docRef = await addDoc(this.getCollection(), docData);
      }

      console.log('🔍 Document created with ID:', docRef.id);

      const newDoc = await getDoc(docRef);
      if (!newDoc.exists()) {
        throw new Error('Failed to retrieve created document');
      }

      const result = this.docToObject(newDoc);
      console.log('✅ Document creation successful:', result.id);
      return result;
    } catch (error) {
      console.error(`❌ Error creating document in ${this.collectionName}:`, error);
      throw this.handleError(error);
    }
  }

  /**
   * Update existing document
   */
  async update(id: string, data: Partial<T>, companyId: string): Promise<T> {
    try {
      // First verify the document exists and belongs to the company
      const existing = await this.getById(id, companyId);
      if (!existing) {
        throw new Error('Document not found or access denied');
      }

      const docRef = this.getDocRef(id);
      const updateData = this.objectToDoc({
        ...data,
        updatedAt: new Date(),
      });

      await updateDoc(docRef, updateData);
      
      const updatedDoc = await getDoc(docRef);
      return this.docToObject(updatedDoc);
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * Delete document
   */
  async delete(id: string, companyId: string): Promise<void> {
    try {
      // First verify the document exists and belongs to the company
      const existing = await this.getById(id, companyId);
      if (!existing) {
        throw new Error('Document not found or access denied');
      }

      const docRef = this.getDocRef(id);
      await deleteDoc(docRef);
    } catch (error) {
      throw this.handleError(error);
    }
  }

  /**
   * Count documents with optional filtering
   */
  async count(companyId: string, options: QueryOptions = {}): Promise<number> {
    try {
      const data = await this.getAll(companyId, options);
      return data.length;
    } catch (error) {
      throw this.handleError(error);
    }
  }
}
