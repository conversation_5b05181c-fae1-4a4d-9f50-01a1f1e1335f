// src/types/layout.ts

import { ReactElement, ReactNode } from 'react';
import { NextPage } from 'next';

// Interface pour les éléments de navigation dans la sidebar
export interface NavItem {
  name: string;
  href: string;
  icon?: React.ComponentType<{ className?: string }>;
  permission?: string;
  badge?: string | number;
}

// Interface pour les props du Header
export interface HeaderProps {
  pageTitle?: string;
  showCompanySelector?: boolean;
  actions?: ReactNode;
}

// Interface pour les props du Layout principal
export interface MainLayoutProps {
  children: ReactNode;
  pageTitle?: string;
  showCompanySelector?: boolean;
  headerActions?: ReactNode;
  requireAuth?: boolean;
  requireCompany?: boolean;
}

// Extension du type NextPage pour inclure getLayout
export type NextPageWithLayout<P = {}, IP = P> = NextPage<P, IP> & {
  getLayout?: (page: ReactElement) => ReactNode;
  requireAuth?: boolean;
  requireCompany?: boolean;
};

// Interface pour les props de la Sidebar
export interface SidebarProps {
  className?: string;
}

// Interface pour la configuration de page
export interface PageConfig {
  title: string;
  description?: string;
  requireAuth?: boolean;
  requireCompany?: boolean;
  showCompanySelector?: boolean;
}

// Mapping des routes vers les configurations de page
export interface RouteConfig {
  [key: string]: PageConfig;
}
