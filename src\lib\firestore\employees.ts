import { BaseFirestoreService } from './base';
import type { EmployeeDocument, QueryOptions } from './types';
import type { Employee } from '@/types';
import { auth } from '@/lib/firebase';

/**
 * Employee service for managing employee data
 */
export class EmployeeService extends BaseFirestoreService<EmployeeDocument> {
  constructor() {
    super('employees');
  }

  /**
   * Create new employee
   */
  async createEmployee(
    employeeData: Omit<Employee, 'id'>,
    companyId: string
  ): Promise<EmployeeDocument> {
    try {
      // Clean and validate the employee data
      const cleanedData = {
        ...employeeData,
        isDeleted: false,
        // Ensure numeric fields are properly handled
        hourlyRate: typeof employeeData.hourlyRate === 'number' ? employeeData.hourlyRate : undefined,
        hoursPerWeek: typeof employeeData.hoursPerWeek === 'number' ? employeeData.hoursPerWeek : undefined,
        weeklySalary: typeof employeeData.weeklySalary === 'number' ? employeeData.weeklySalary : undefined,
        overtimeRate: typeof employeeData.overtimeRate === 'number' ? employeeData.overtimeRate : undefined,
        // Ensure string fields are properly handled
        phoneNumber: employeeData.phoneNumber || undefined,
        address: employeeData.address || undefined,
        service: employeeData.service || undefined,
      };

      console.log('🔍 Creating employee with cleaned data:', {
        ...cleanedData,
        companyId,
        currentUser: auth.currentUser?.email,
        currentUID: auth.currentUser?.uid
      });

      return this.create(cleanedData as any, companyId);
    } catch (error) {
      console.error('❌ Error in createEmployee:', error);
      console.error('❌ Error details:', {
        message: error.message,
        code: error.code,
        companyId,
        currentUser: auth.currentUser?.email,
        currentUID: auth.currentUser?.uid
      });
      throw error;
    }
  }

  /**
   * Update employee
   */
  async updateEmployee(
    id: string, 
    updates: Partial<Employee>, 
    companyId: string
  ): Promise<EmployeeDocument> {
    return this.update(id, updates as any, companyId);
  }

  /**
   * Soft delete employee (mark as deleted instead of removing)
   */
  async softDeleteEmployee(id: string, companyId: string, deletedBy: string): Promise<void> {
    await this.update(id, {
      isDeleted: true,
      deletedAt: new Date(),
      deletedBy,
    } as any, companyId);
  }

  /**
   * Get active employees only
   */
  async getActiveEmployees(companyId: string, options: QueryOptions = {}): Promise<EmployeeDocument[]> {
    const whereClause = [
      { field: 'isDeleted', operator: '==' as const, value: false },
      ...(options.where || [])
    ];

    return this.getAll(companyId, { ...options, where: whereClause });
  }

  /**
   * Search employees by name or email
   */
  async searchEmployees(
    companyId: string, 
    searchTerm: string, 
    options: QueryOptions = {}
  ): Promise<EmployeeDocument[]> {
    // Note: Firestore doesn't support full-text search natively
    // This is a basic implementation that gets all employees and filters client-side
    // For production, consider using Algolia or similar service for better search
    
    const employees = await this.getActiveEmployees(companyId, options);
    
    const searchLower = searchTerm.toLowerCase();
    return employees.filter(employee => 
      employee.firstName.toLowerCase().includes(searchLower) ||
      employee.lastName.toLowerCase().includes(searchLower) ||
      employee.email.toLowerCase().includes(searchLower) ||
      `${employee.firstName} ${employee.lastName}`.toLowerCase().includes(searchLower)
    );
  }

  /**
   * Get employees by department
   */
  async getEmployeesByDepartment(
    companyId: string, 
    department: string, 
    options: QueryOptions = {}
  ): Promise<EmployeeDocument[]> {
    const whereClause = [
      { field: 'isDeleted', operator: '==' as const, value: false },
      { field: 'department', operator: '==' as const, value: department },
      ...(options.where || [])
    ];

    return this.getAll(companyId, { ...options, where: whereClause });
  }

  /**
   * Get employees by status
   */
  async getEmployeesByStatus(
    companyId: string, 
    status: 'Active' | 'Inactive', 
    options: QueryOptions = {}
  ): Promise<EmployeeDocument[]> {
    const whereClause = [
      { field: 'isDeleted', operator: '==' as const, value: false },
      { field: 'status', operator: '==' as const, value: status },
      ...(options.where || [])
    ];

    return this.getAll(companyId, { ...options, where: whereClause });
  }

  /**
   * Get employee statistics
   */
  async getEmployeeStats(companyId: string): Promise<{
    total: number;
    active: number;
    inactive: number;
    byDepartment: Record<string, number>;
  }> {
    const employees = await this.getActiveEmployees(companyId);
    
    const stats = {
      total: employees.length,
      active: employees.filter(e => e.status === 'Active').length,
      inactive: employees.filter(e => e.status === 'Inactive').length,
      byDepartment: {} as Record<string, number>,
    };

    // Count by department
    employees.forEach(employee => {
      const dept = employee.department;
      stats.byDepartment[dept] = (stats.byDepartment[dept] || 0) + 1;
    });

    return stats;
  }

  /**
   * Check if email is already used by another employee
   */
  async isEmailTaken(companyId: string, email: string, excludeId?: string): Promise<boolean> {
    const whereClause = [
      { field: 'isDeleted', operator: '==' as const, value: false },
      { field: 'email', operator: '==' as const, value: email },
    ];

    const employees = await this.getAll(companyId, { where: whereClause });

    if (excludeId) {
      return employees.some(emp => emp.id !== excludeId);
    }

    return employees.length > 0;
  }

  /**
   * Get employee by email
   */
  async getEmployeeByEmail(companyId: string, email: string): Promise<EmployeeDocument | null> {
    const whereClause = [
      { field: 'isDeleted', operator: '==' as const, value: false },
      { field: 'email', operator: '==' as const, value: email },
    ];

    const employees = await this.getAll(companyId, { where: whereClause, limit: 1 });
    return employees.length > 0 ? employees[0] : null;
  }

  /**
   * Get employees by position
   */
  async getEmployeesByPosition(
    companyId: string,
    position: string,
    options: QueryOptions = {}
  ): Promise<EmployeeDocument[]> {
    const whereClause = [
      { field: 'isDeleted', operator: '==' as const, value: false },
      { field: 'position', operator: '==' as const, value: position },
      ...(options.where || [])
    ];

    return this.getAll(companyId, { ...options, where: whereClause });
  }

  /**
   * Get recently hired employees (within last N days)
   */
  async getRecentlyHiredEmployees(
    companyId: string,
    days: number = 30
  ): Promise<EmployeeDocument[]> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - days);

    const whereClause = [
      { field: 'isDeleted', operator: '==' as const, value: false },
      { field: 'hireDate', operator: '>=' as const, value: cutoffDate.toISOString() },
    ];

    return this.getAll(companyId, {
      where: whereClause,
      orderBy: { field: 'hireDate', direction: 'desc' }
    });
  }

  /**
   * Get employees hired in a date range
   */
  async getEmployeesHiredBetween(
    companyId: string, 
    startDate: Date, 
    endDate: Date
  ): Promise<EmployeeDocument[]> {
    const whereClause = [
      { field: 'isDeleted', operator: '==' as const, value: false },
      { field: 'hireDate', operator: '>=' as const, value: startDate.toISOString() },
      { field: 'hireDate', operator: '<=' as const, value: endDate.toISOString() },
    ];

    return this.getAll(companyId, { where: whereClause });
  }

  /**
   * Convert EmployeeDocument to Employee (for compatibility with existing code)
   */
  toEmployee(employeeDoc: EmployeeDocument): Employee {
    const { isDeleted, deletedAt, deletedBy, createdAt, updatedAt, companyId, ...employee } = employeeDoc;
    return employee as Employee;
  }

  /**
   * Convert multiple EmployeeDocuments to Employees
   */
  toEmployees(employeeDocs: EmployeeDocument[]): Employee[] {
    return employeeDocs.map(doc => this.toEmployee(doc));
  }
}

// Export singleton instance
export const employeeService = new EmployeeService();
