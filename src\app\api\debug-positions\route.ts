import { NextRequest, NextResponse } from 'next/server';
import { getFirestore } from 'firebase-admin/firestore';
import { initializeApp, getApps, cert } from 'firebase-admin/app';

// Initialize Firebase Admin SDK
if (!getApps().length) {
  const serviceAccount = {
    type: "service_account",
    project_id: process.env.FIREBASE_PROJECT_ID,
    private_key_id: process.env.FIREBASE_PRIVATE_KEY_ID,
    private_key: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
    client_email: process.env.FIREBASE_CLIENT_EMAIL,
    client_id: process.env.FIREBASE_CLIENT_ID,
    auth_uri: "https://accounts.google.com/o/oauth2/auth",
    token_uri: "https://oauth2.googleapis.com/token",
    auth_provider_x509_cert_url: "https://www.googleapis.com/oauth2/v1/certs",
    client_x509_cert_url: `https://www.googleapis.com/robot/v1/metadata/x509/${process.env.FIREBASE_CLIENT_EMAIL}`
  };

  initializeApp({
    credential: cert(serviceAccount as any),
    projectId: process.env.FIREBASE_PROJECT_ID,
  });
}

const db = getFirestore();

export async function GET(request: NextRequest) {
  try {
    console.log('🔍 Debug Positions: Checking position data');
    
    // Get all companies
    const companiesSnapshot = await db.collection('companies').get();
    const companies = companiesSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }));
    
    console.log('🔍 Debug Positions: Found companies:', companies.length);
    
    // Get all services
    const servicesSnapshot = await db.collection('services').get();
    const services = servicesSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }));
    
    console.log('🔍 Debug Positions: Found services:', services.length);
    
    // Get all positions
    const positionsSnapshot = await db.collection('positions').get();
    const positions = positionsSnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }));
    
    console.log('🔍 Debug Positions: Found positions:', positions.length);
    
    // Group positions by company
    const positionsByCompany = positions.reduce((acc, position) => {
      const companyId = position.companyId;
      if (!acc[companyId]) {
        acc[companyId] = [];
      }
      acc[companyId].push(position);
      return acc;
    }, {} as Record<string, any[]>);
    
    // Get test company positions specifically
    const testCompany = companies.find(c => c.name === 'WePaie Test Company');
    const testCompanyPositions = testCompany ? positionsByCompany[testCompany.id] || [] : [];
    
    console.log('🔍 Debug Positions: Test company positions:', testCompanyPositions.length);

    return NextResponse.json({
      success: true,
      data: {
        companies: companies.map(c => ({ id: c.id, name: c.name })),
        services: services.map(s => ({ id: s.id, name: s.name, companyId: s.companyId })),
        positions: positions.map(p => ({ id: p.id, name: p.name, companyId: p.companyId, serviceId: p.serviceId })),
        testCompany: testCompany ? { id: testCompany.id, name: testCompany.name } : null,
        testCompanyPositions: testCompanyPositions.map(p => ({ 
          id: p.id, 
          name: p.name, 
          serviceId: p.serviceId,
          isActive: p.isActive 
        })),
        stats: {
          totalCompanies: companies.length,
          totalServices: services.length,
          totalPositions: positions.length,
          testCompanyPositions: testCompanyPositions.length
        }
      }
    });

  } catch (error) {
    console.error('❌ Debug Positions: Error:', error);
    return NextResponse.json({ 
      error: 'Debug failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
