# WePaie

WePaie is a simplified payroll and HR management application built with Next.js and Firebase.

## Features

- **Employee Management**: Streamlined UI for adding and managing employees with essential details
- **Time Management**: Simplified absence and overtime tracking with minimal categories
- **Multi-tenancy**: Support for multiple companies with data isolation
- **User Roles**: Admin and payroll manager roles with appropriate permissions
- **Internationalization**: Support for English, French, and Arabic languages

## Getting Started

1. Install dependencies:
   ```bash
   npm install
   ```

2. Set up your Firebase configuration (see Firebase Setup section below)

3. Run the development server:
   ```bash
   npm run dev
   ```

4. Open [http://localhost:9002](http://localhost:9002) in your browser

## Firebase Setup

### Option 1: Use Firebase Emulators (Recommended for Development)

1. Copy the example environment file:
   ```bash
   cp .env.local.example .env.local
   ```

2. Start the Firebase emulators:
   ```bash
   npm run firebase:emulators
   ```

3. In another terminal, start the development server:
   ```bash
   npm run dev
   ```

   Or use the combined command:
   ```bash
   npm run dev:emulators
   ```

4. Visit http://localhost:4000 to access the Firebase Emulator UI

### Option 2: Use Production Firebase

1. Create a Firebase project at https://console.firebase.google.com
2. Enable Firestore Database
3. Copy your Firebase configuration
4. Create a `.env.local` file with your Firebase configuration:

```env
NEXT_PUBLIC_FIREBASE_API_KEY=your_api_key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_project_id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your_project.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
NEXT_PUBLIC_FIREBASE_APP_ID=your_app_id
```

5. Deploy Firestore security rules:
   ```bash
   firebase deploy --only firestore:rules,firestore:indexes
   ```

## Tech Stack

- **Frontend**: Next.js 15, React 18, TypeScript
- **UI**: Radix UI, Tailwind CSS
- **Database**: Firebase Firestore
- **Forms**: React Hook Form with Zod validation
- **AI**: Google Genkit integration
- **State Management**: React Context + Custom Hooks
- **Authentication**: Firebase Auth (ready for integration)
- **Development**: Firebase Emulators for local development

## Project Structure

```
src/
├── app/                    # Next.js app router pages
├── components/             # React components
│   ├── ui/                # Reusable UI components
│   ├── employees/         # Employee management components
│   ├── time-management/   # Time tracking components
│   └── setup/             # Setup and onboarding components
├── context/               # React context providers
│   ├── i18nContext.tsx    # Internationalization
│   └── FirestoreContext.tsx # Firestore data management
├── hooks/                 # Custom React hooks
├── lib/                   # Utility libraries
│   ├── firestore/         # Firestore services and types
│   ├── firebase.ts        # Firebase configuration
│   ├── schemas.ts         # Zod validation schemas
│   └── utils.ts           # Utility functions
├── locales/               # Translation files
└── types/                 # TypeScript type definitions
```

## Features

### Implemented
- ✅ Multi-language support (English, French, Arabic)
- ✅ Employee management with Firestore integration
- ✅ Time management (absences and overtime) - UI ready
- ✅ Multi-tenancy with company isolation
- ✅ Role-based permissions (admin, payroll_manager)
- ✅ Responsive design with Tailwind CSS
- ✅ Type-safe database operations
- ✅ Firebase emulator support for development
- ✅ Real-time data updates with React hooks

### Ready for Integration
- 🔄 Time management Firestore integration
- 🔄 User authentication with Firebase Auth
- 🔄 Dashboard with real-time statistics
- 🔄 Advanced search and filtering
- 🔄 Data export functionality
- 🔄 Audit logging

## Quick Start

After setting up your Firebase configuration:

1. **Start the application**:
   ```bash
   npm run dev
   ```

2. **Go to the setup page**:
   Visit: http://localhost:9002/test-firebase

3. **Use Quick Start**:
   - Click "Create Demo Setup" to instantly create a demo company
   - This bypasses authentication requirements for immediate testing
   - Creates sample data to explore all features

4. **Explore the application**:
   - Navigate to the main app to see employee management
   - Test time management features
   - Try different language settings

## Troubleshooting

### "Missing or insufficient permissions" Error

This is normal! It means your Firebase security rules are working correctly. Use one of these solutions:

1. **Quick Start (Recommended)**: Visit http://localhost:9002/test-firebase and use the Quick Start feature
2. **Emulator Mode**: Set `NEXT_PUBLIC_USE_FIREBASE_EMULATOR=true` in `.env.local`
3. **Check Configuration**: Verify your Firebase environment variables are correct

See [Troubleshooting Guide](docs/troubleshooting-permissions.md) for detailed solutions.

## Documentation

- [Firestore Integration Guide](docs/firestore-integration.md)
- [Production Deployment Guide](docs/production-deployment.md)
- [Troubleshooting Permissions](docs/troubleshooting-permissions.md)
- [Application Blueprint](docs/blueprint.md)
