"use client";

import { useState, useCallback, useRef } from 'react';

/**
 * Hook for providing immediate visual feedback
 * Shows loading states instantly while operations are in progress
 */
export function useImmediateFeedback() {
  const [isProcessing, setIsProcessing] = useState(false);
  const [processingMessage, setProcessingMessage] = useState<string>('');
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  const startProcessing = useCallback((message: string = 'Processing...') => {
    setIsProcessing(true);
    setProcessingMessage(message);
    
    // Clear any existing timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
  }, []);

  const stopProcessing = useCallback((delay: number = 0) => {
    if (delay > 0) {
      timeoutRef.current = setTimeout(() => {
        setIsProcessing(false);
        setProcessingMessage('');
      }, delay);
    } else {
      setIsProcessing(false);
      setProcessingMessage('');
    }
  }, []);

  const withImmediateFeedback = useCallback(
    async <T>(
      operation: () => Promise<T>,
      message: string = 'Processing...',
      minDuration: number = 300
    ): Promise<T> => {
      startProcessing(message);
      const startTime = Date.now();
      
      try {
        const result = await operation();
        
        // Ensure minimum duration for smooth UX
        const elapsed = Date.now() - startTime;
        const remainingTime = Math.max(0, minDuration - elapsed);
        
        stopProcessing(remainingTime);
        return result;
      } catch (error) {
        stopProcessing();
        throw error;
      }
    },
    [startProcessing, stopProcessing]
  );

  return {
    isProcessing,
    processingMessage,
    startProcessing,
    stopProcessing,
    withImmediateFeedback,
  };
}

/**
 * Hook for navigation feedback
 * Provides immediate feedback when navigating between pages
 */
export function useNavigationFeedback() {
  const [isNavigating, setIsNavigating] = useState(false);
  const [navigationTarget, setNavigationTarget] = useState<string>('');

  const startNavigation = useCallback((target: string) => {
    setIsNavigating(true);
    setNavigationTarget(target);
  }, []);

  const stopNavigation = useCallback(() => {
    setIsNavigating(false);
    setNavigationTarget('');
  }, []);

  return {
    isNavigating,
    navigationTarget,
    startNavigation,
    stopNavigation,
  };
}
