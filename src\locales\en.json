{"appName": "WePaie", "login": "<PERSON><PERSON>", "login.description": "Log in to access your account", "signup": "Sign Up", "signup.description": "Create your account to get started", "dashboard": "Dashboard", "employees": "Employees", "timeManagement": "Time Management", "settings": "Settings", "language": "Language", "english": "English", "french": "French", "arabic": "Arabic", "profileSettings": "Profile Settings", "companySettings": "Company Settings", "userManagement": "User Management", "systemPreferences": "System Preferences", "email": "Email", "password": "Password", "forgotPassword": "Forgot password?", "dontHaveAccount": "Don't have an account?", "alreadyHaveAccount": "Already have an account?", "companyName": "Company Name", "confirmPassword": "Confirm Password", "saveChanges": "Save Changes", "updatePassword": "Update Password", "currentPassword": "Current Password", "newPassword": "New Password", "confirmNewPassword": "Confirm New Password", "logout": "Log out", "profile": "Profile", "profile.yourProfile": "Your Profile", "profile.description": "Manage your personal information.", "fullName": "Full Name", "emailAddress": "Email Address", "changePassword": "Change Password", "nav.dashboard": "Dashboard", "nav.employees": "Employees", "nav.timeManagement": "Time Management", "nav.admin": "Administration", "nav.settings": "Settings", "languageSettings.title": "Language Settings", "languageSettings.description": "Choose your preferred language for the application.", "dashboard.totalEmployees": "Total Employees", "dashboard.totalEmployees.caption": "+2 from last month", "dashboard.pendingPayroll": "Pending Payroll", "dashboard.pendingPayroll.caption": "Next run: July 30th", "dashboard.openPositions": "Open Positions", "dashboard.openPositions.caption": "2 new this week", "dashboard.recentActivity": "Recent Activity", "dashboard.recentActivity.description": "Overview of recent actions in the system.", "dashboard.recentActivity.empty": "No recent activity to display.", "form.label.firstName": "First Name", "form.label.lastName": "Last Name", "form.label.phoneNumber": "Phone Number", "form.label.address": "Address", "form.label.department": "Department", "form.label.service": "Service", "form.label.position": "Position", "form.label.weeklySalary": "Weekly Salary", "form.label.hoursPerWeek": "Hours/Week", "form.label.hourlyRate": "Hourly Rate", "form.label.overtimeRate": "Overtime Rate", "form.label.hireDate": "Hire Date", "form.label.status": "Status", "form.label.email": "Email", "form.placeholder.firstName": "<PERSON>", "form.placeholder.lastName": "<PERSON><PERSON>", "form.placeholder.address": "123 Main St, Anytown, USA", "form.placeholder.department": "Engineering", "form.placeholder.service": "Web Development", "form.placeholder.position": "Senior Developer", "form.placeholder.pickDate": "Pick a date", "form.placeholder.selectStatus": "Select status", "status.active": "Active", "status.inactive": "Inactive", "employeeForm.toast.addedTitle": "Employee Added", "employeeForm.toast.updatedTitle": "Employee Updated", "employeeForm.toast.successDescription_add": "{{name}} has been successfully added. (Simulated)", "employeeForm.toast.successDescription_edit": "{{name}} has been successfully updated. (Simulated)", "employeeForm.toast.errorTitle": "Error", "employeeForm.toast.errorDescription_add": "Failed to add employee. Please try again.", "employeeForm.toast.errorDescription_edit": "Failed to update employee. Please try again.", "employeeForm.button.add": "Add Employee", "employeeForm.button.save": "Save Changes", "employeeTable.searchPlaceholder": "Search employees...", "employeeTable.filter": "Filter", "employeeTable.addEmployee": "Add Employee", "employeeTable.modal.addTitle": "Add New Employee", "employeeTable.modal.editTitle": "Edit Employee", "employeeTable.toast.deletedTitle": "Employee Deleted", "employeeTable.toast.deletedDescription": "Employee {{name}} has been deleted. (Simulated)", "employeeTable.actionsLabel": "Actions", "employeeTable.edit": "Edit", "employeeTable.delete": "Delete", "employeeTable.deleteDialog.title": "Are you sure?", "employeeTable.deleteDialog.description": "This action cannot be undone. This will permanently delete the employee {{name}}.", "employeeTable.deleteDialog.cancel": "Cancel", "employeeTable.deleteDialog.confirm": "Delete", "employeeTable.noEmployeesFound": "No employees found.", "table.column.name": "Name", "table.column.email": "Email", "table.column.phone": "Phone", "table.column.department": "Department", "table.column.position": "Position", "table.column.status": "Status", "table.column.actions": "Actions", "absenceForm.toast.loggedTitle": "Absence Logged", "absenceForm.toast.loggedDescription": "Absence for selected employee(s) has been logged. (Simulated)", "absenceForm.label.employees": "Employee(s)", "absenceForm.placeholder.selectEmployee": "Select employee(s)", "absenceForm.description.selectEmployee": "Select one or more employees.", "absenceForm.label.category": "Absence Category", "absenceForm.placeholder.selectCategory": "Select absence category", "absenceForm.category.sickLeave": "Sick Leave", "absenceForm.category.annualLeave": "Annual Leave", "absenceForm.category.unpaidLeave": "Unpaid Leave", "absenceForm.category.other": "Other", "absenceForm.label.startDate": "Start Date", "absenceForm.label.endDate": "End Date", "absenceForm.label.notes": "Notes (Optional)", "absenceForm.placeholder.notes": "Reason for absence, doctor's note reference, etc.", "absenceForm.button.logAbsence": "Log Absence", "overtimeForm.toast.loggedTitle": "Overtime Logged", "overtimeForm.toast.loggedDescription": "Overtime for selected employee(s) has been logged. (Simulated)", "overtimeForm.label.employees": "Employee(s)", "overtimeForm.placeholder.selectEmployee": "Select employee(s)", "overtimeForm.description.selectEmployee": "Select one or more employees.", "overtimeForm.label.date": "Date", "overtimeForm.label.hours": "Hours", "overtimeForm.label.category": "Overtime Category", "overtimeForm.placeholder.selectCategory": "Select overtime category", "overtimeForm.category.regular": "Regular Overtime", "overtimeForm.category.holiday": "Holiday Overtime", "overtimeForm.category.specialProject": "Special Project", "overtimeForm.label.reason": "Reason (Optional)", "overtimeForm.placeholder.reason": "Reason for overtime, project code, etc.", "overtimeForm.button.logOvertime": "Log Overtime", "settings.language": "Language", "settings.fontFamily": "Font Family", "settings.textSize": "Text Size", "settings.fontStyle": "Font Style", "settings.theme": "Theme", "settings.displayMode": "Display Mode", "settings.passwordManagement": "Password Management", "settings.applicationPreferences": "Application Preferences", "size.small": "Small", "size.medium": "Medium", "size.large": "Large", "style.normal": "Normal", "style.medium": "Medium", "style.bold": "Bold", "theme.default": "<PERSON><PERSON><PERSON>", "theme.nature": "Nature", "theme.corporate": "Corporate", "theme.warm": "Warm", "mode.light": "Light", "mode.dark": "Dark", "common.select": "Select"}