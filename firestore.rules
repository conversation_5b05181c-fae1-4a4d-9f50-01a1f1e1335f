rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }

    function isDevelopmentTest() {
      // Allow specific development test operations only
      // Must have both a specific source marker AND a test name pattern
      return request.resource != null &&
             request.resource.data.keys().hasAny(['source']) &&
             request.resource.data.get('source', '') == 'WePaie_Connection_Test' &&
             request.resource.data.keys().hasAny(['name']) &&
             request.resource.data.get('name', '').matches('.*Test Company.*|.*Demo Company.*');
    }

    function isLegitimateTestCompany() {
      // Allow creation of companies with specific test patterns from our application
      return request.resource != null &&
             request.resource.data.keys().hasAny(['name']) &&
             (request.resource.data.get('name', '') == 'WePaie Demo Company' ||
              request.resource.data.get('name', '') == 'Test Company (WePaie)' ||
              request.resource.data.get('name', '') == 'Test Company (Document Reference Fix)' ||
              request.resource.data.get('name', '').matches('.*WePaie.*Demo.*') ||
              request.resource.data.get('name', '').matches('.*Test Company.*WePaie.*'));
    }

    function isDemoDataCreation() {
      // Allow creation of demo data for development
      return request.resource != null &&
             (
               // Demo company creation
               (request.resource.data.get('name', '') == 'WePaie Demo Company') ||
               // Demo user creation
               (request.resource.data.keys().hasAny(['email']) &&
                request.resource.data.get('email', '') == '<EMAIL>' &&
                request.resource.data.get('role', '') == 'admin')
             );
    }

    function isSuperAdminUser() {
      // Check if the authenticated user is the super admin
      return isAuthenticated() && request.auth.token.email == '<EMAIL>';
    }

    function isDemoAdminUser() {
      // Check if the authenticated user is the demo admin
      // Simplified check for better reliability
      return isAuthenticated() && request.auth.token.email == '<EMAIL>';
    }

    function isDemoAdminUserForCreation() {
      // Special function for creation operations that allows demo admin
      // without requiring user document to exist
      return isAuthenticated() && request.auth.token.email == '<EMAIL>';
    }

    function isAdminUser() {
      // Check if user is any type of admin (super admin or demo admin)
      return isSuperAdminUser() || isDemoAdminUser();
    }
    
    function getUserData() {
      return get(/databases/$(database)/documents/users/$(request.auth.uid)).data;
    }

    function userDocumentExists() {
      return exists(/databases/$(database)/documents/users/$(request.auth.uid));
    }

    function isCompanyMember(companyId) {
      return isAuthenticated() && userDocumentExists() && getUserData().companyId == companyId;
    }

    function isAdmin(companyId) {
      return isCompanyMember(companyId) && getUserData().role == 'admin';
    }

    function isPayrollManager(companyId) {
      // Check if user document exists first to avoid errors
      return isAuthenticated() &&
             userDocumentExists() &&
             isCompanyMember(companyId) &&
             (getUserData().role == 'admin' || getUserData().role == 'payroll_manager');
    }

    function isActiveUser(companyId) {
      return isCompanyMember(companyId) && getUserData().isActive == true;
    }

    // Companies collection
    match /companies/{companyId} {
      // Allow creation for super admin, test companies, demo data, or admin users
      allow create: if isSuperAdminUser() || isLegitimateTestCompany() || isDemoDataCreation() ||
        (isDemoAdminUser() && request.resource.data.get('name', '') == 'WePaie Demo Company');

      // Allow reading by super admin (can read all companies), company members, admin users, or demo data
      allow read: if isSuperAdminUser() || isDemoDataCreation() || isDemoAdminUser() ||
        (isAuthenticated() && isCompanyMember(companyId));

      // Allow updates and deletes by super admin or company admins
      allow update, delete: if isSuperAdminUser() || (isAuthenticated() && isAdmin(companyId));
    }

    // Connection test collection (for development testing only)
    match /connection_test/{testId} {
      // Allow writes only with proper source marker, reads are blocked
      allow write: if request.resource != null &&
                      request.resource.data.keys().hasAny(['source']) &&
                      request.resource.data.get('source', '') == 'WePaie_Connection_Test';
      // Explicitly deny reads to test security
      allow read: if false;
    }

    // Users collection
    match /users/{userId} {
      // Users can read their own data, demo data, admin users, or super admin
      allow read: if isSuperAdminUser() || isDemoDataCreation() || isDemoAdminUser() ||
        (isAuthenticated() && request.auth.uid == userId);

      // Allow user creation for:
      // 1. Super admin (can create any user)
      // 2. Demo data creation (unauthenticated)
      // 3. Authenticated user creating their own user document (userId == auth.uid)
      // 4. Demo admin creating their user document
      // 5. Existing admin creating users for their company
      allow create: if isSuperAdminUser() || isDemoDataCreation() ||
        (isAuthenticated() && request.auth.uid == userId) ||
        (isDemoAdminUser() && request.resource.data.get('email', '') == '<EMAIL>') ||
        (isAuthenticated() &&
         exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
         isAdmin(request.resource.data.companyId));

      // Allow updates/deletes by super admin, the user themselves, or company admins
      allow update, delete: if isSuperAdminUser() || (isAuthenticated() &&
        (request.auth.uid == userId ||
         (exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
          isAdmin(resource.data.companyId))));
    }

    // Employees collection
    match /employees/{employeeId} {
      // Super admin, active company members, or demo admin can read employees
      allow read: if isSuperAdminUser() || isActiveUser(resource.data.companyId) || isDemoAdminUser();
      // Super admin, demo admin, or payroll managers can create/update/delete employees
      allow create, update, delete: if isSuperAdminUser() || isDemoAdminUserForCreation() ||
        (isAuthenticated() && userDocumentExists() && isPayrollManager(resource.data.companyId));
    }

    // Absences collection
    match /absences/{absenceId} {
      // All active company members can read absences
      allow read: if isActiveUser(resource.data.companyId);
      // Only payroll managers and admins can write
      allow create, update, delete: if isPayrollManager(resource.data.companyId);
    }

    // Overtime collection
    match /overtime/{overtimeId} {
      // All active company members can read overtime
      allow read: if isActiveUser(resource.data.companyId);
      // Only payroll managers and admins can write
      allow create, update, delete: if isPayrollManager(resource.data.companyId);
    }

    // Departments collection
    match /departments/{departmentId} {
      // Super admin, active company members, or demo admin can read departments
      allow read: if isSuperAdminUser() || isActiveUser(resource.data.companyId) || isDemoAdminUser();
      // Super admin, company admins, or demo admin can create, update, and delete departments
      allow create, update, delete: if isSuperAdminUser() || isAdmin(resource.data.companyId) || isDemoAdminUser();
    }

    // Services collection
    match /services/{serviceId} {
      // Super admin, active company members, or demo admin can read services
      allow read: if isSuperAdminUser() || isActiveUser(resource.data.companyId) || isDemoAdminUser();
      // Super admin, company admins, or demo admin can create, update, and delete services
      allow create, update, delete: if isSuperAdminUser() || isAdmin(resource.data.companyId) || isDemoAdminUser();
    }

    // Positions collection
    match /positions/{positionId} {
      // Super admin, active company members, or demo admin can read positions
      allow read: if isSuperAdminUser() || isActiveUser(resource.data.companyId) || isDemoAdminUser();
      // Super admin, company admins, or demo admin can create, update, and delete positions
      allow create, update, delete: if isSuperAdminUser() || isAdmin(resource.data.companyId) || isDemoAdminUser();
    }

    // Audit logs collection (optional)
    match /audit_logs/{logId} {
      // Only admins can read audit logs
      allow read: if isAdmin(resource.data.companyId);
      // System can write audit logs
      allow create: if isAuthenticated();
    }

    // Default deny all other documents
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
