"use client";

import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { useTranslation } from '@/context/i18nContext';
import { usePermissions } from '@/context/FirestoreContext';
import { useRBAC } from '@/context/RBACContext';
import { cn } from '@/lib/utils';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  User, 
  Building2, 
  Users, 
  Settings, 
  Shield,
  Crown
} from 'lucide-react';

interface SettingsNavItem {
  href: string;
  labelKey: string;
  icon: React.ComponentType<{ className?: string }>;
  description: string;
  adminOnly?: boolean;
  badge?: string;
}

/**
 * Settings Layout Component
 * 
 * Provides navigation and layout structure for settings pages.
 * Includes permission-based navigation items and responsive design.
 * 
 * Features:
 * - Permission-based navigation (admin-only items)
 * - Active state indication
 * - Responsive sidebar navigation
 * - Accessibility support
 */
export function SettingsLayout({ children }: { children: React.ReactNode }) {
  const { t } = useTranslation();
  const pathname = usePathname();
  const { isAdmin } = usePermissions(); // Legacy compatibility
  const { isSuperAdmin, isAdmin: rbacIsAdmin } = useRBAC();

  // Settings should be limited to personal preferences only
  // Company, user management, and system settings moved to /admin page
  const settingsNavItems: SettingsNavItem[] = [
    {
      href: '/settings/profile',
      labelKey: 'profileSettings',
      icon: User,
      description: 'Gérez vos informations de profil personnel et vos préférences',
    },
  ];

  // Use RBAC admin check with fallback to legacy
  const isAdminUser = isSuperAdmin() || rbacIsAdmin() || isAdmin();

  // Filter navigation items based on permissions
  const visibleNavItems = settingsNavItems.filter(item =>
    !item.adminOnly || isAdminUser
  );

  return (
    <div className="flex flex-col lg:flex-row gap-6 max-w-7xl mx-auto">
      {/* Settings Navigation Sidebar */}
      <div className="lg:w-80 space-y-4">
        <div>
          <h2 className="text-lg font-semibold mb-2">Paramètres Personnels</h2>
          <p className="text-sm text-muted-foreground">
            Gérez vos préférences personnelles et paramètres de compte.
          </p>
        </div>

        <Card>
          <CardContent className="p-0">
            <nav className="space-y-1">
              {visibleNavItems.map((item) => {
                const isActive = pathname === item.href;
                const Icon = item.icon;

                return (
                  <Link
                    key={item.href}
                    href={item.href}
                    className={cn(
                      "flex items-start gap-3 p-4 rounded-lg transition-colors",
                      "hover:bg-muted/50",
                      isActive && "bg-muted border-l-4 border-l-primary"
                    )}
                  >
                    <Icon className={cn(
                      "h-5 w-5 mt-0.5 flex-shrink-0",
                      isActive ? "text-primary" : "text-muted-foreground"
                    )} />
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <span className={cn(
                          "font-medium text-sm",
                          isActive ? "text-primary" : "text-foreground"
                        )}>
                          {t(item.labelKey)}
                        </span>
                        
                        {item.badge && (
                          <Badge 
                            variant="secondary" 
                            className="text-xs px-1.5 py-0.5 h-auto"
                          >
                            <Crown className="h-3 w-3 mr-1" />
                            {item.badge}
                          </Badge>
                        )}
                      </div>
                      
                      <p className="text-xs text-muted-foreground leading-relaxed">
                        {item.description}
                      </p>
                    </div>
                  </Link>
                );
              })}
            </nav>
          </CardContent>
        </Card>

        {/* Admin Notice */}
        <Card className="border-blue-200 bg-blue-50">
          <CardContent className="p-4">
            <div className="flex items-start gap-3">
              <Shield className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
              <div>
                <h4 className="font-medium text-blue-800 text-sm mb-1">
                  Gestion d'Entreprise et Système
                </h4>
                <p className="text-xs text-blue-700 leading-relaxed">
                  Pour gérer les entreprises, utilisateurs et paramètres système, visitez la page <Link href="/admin" className="underline font-medium">Administration</Link>.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Area */}
      <div className="flex-1 min-w-0">
        {children}
      </div>
    </div>
  );
}
