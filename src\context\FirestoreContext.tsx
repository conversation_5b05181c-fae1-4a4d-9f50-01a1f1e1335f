"use client";

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import type { Company, UserDocument } from '@/lib/firestore/types';
import { companyService, userService } from '@/lib/firestore';
import { initializeDemoSetup, shouldUseDemoData, getDemoIds } from '@/lib/demo-data';
import { useAuth } from './AuthContext';

interface FirestoreContextType {
  // Current company and user
  company: Company | null;
  user: UserDocument | null;
  
  // Loading states
  isLoading: boolean;
  isInitialized: boolean;
  
  // Actions
  setCompany: (company: Company | null) => void;
  setUser: (user: UserDocument | null) => void;
  refreshCompany: () => Promise<void>;
  refreshUser: () => Promise<void>;
  
  // Utility functions
  hasPermission: (permission: string) => boolean;
  isAdmin: () => boolean;
  isPayrollManager: () => boolean;
}

const FirestoreContext = createContext<FirestoreContextType | undefined>(undefined);

interface FirestoreProviderProps {
  children: ReactNode;
  initialCompanyId?: string;
  initialUserId?: string;
}

export function FirestoreProvider({
  children,
  initialCompanyId,
  initialUserId
}: FirestoreProviderProps) {
  const auth = useAuth();
  const [company, setCompanyState] = useState<Company | null>(null);
  const [user, setUserState] = useState<UserDocument | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isInitialized, setIsInitialized] = useState(false);

  // Initialize data based on authentication state
  useEffect(() => {
    async function initialize() {
      // Wait for auth to be initialized
      if (!auth.isInitialized) {
        return;
      }

      setIsLoading(true);

      try {
        let companyData: Company | null = null;
        let userData: UserDocument | null = null;

        // If user is authenticated, use auth data
        if (auth.firebaseUser && auth.user && auth.company) {
          console.log('🔐 Using authenticated user data');
          companyData = auth.company;
          userData = auth.user;
        }
        // If specific IDs provided (for backward compatibility), use them
        else if (initialCompanyId && initialUserId) {
          console.log('📊 Loading data with provided IDs');
          companyData = await companyService.getById(initialCompanyId);
          if (companyData) {
            userData = await userService.getById(initialUserId, initialCompanyId);
          }
        }
        // If no auth and no IDs provided and we should use demo data, initialize demo setup
        else if (!auth.firebaseUser && !initialCompanyId && !initialUserId && shouldUseDemoData()) {
          console.log('🚀 No auth/IDs provided, checking for demo setup...');
          try {
            // First try to get existing demo data
            const { companyId, userId } = getDemoIds();
            try {
              companyData = await companyService.getById(companyId);
              if (companyData) {
                userData = await userService.getById(userId, companyId);
              }
            } catch (error) {
              // Demo data doesn't exist, create it
              console.log('📝 Demo data not found, creating...');
            }

            // If we still don't have data, initialize demo setup
            if (!companyData || !userData) {
              const demoSetup = await initializeDemoSetup();
              companyData = demoSetup.company;
              userData = demoSetup.admin;
              console.log('✅ Demo setup initialized successfully');
            } else {
              console.log('✅ Using existing demo data');
            }
          } catch (demoError) {
            console.warn('⚠️ Failed to initialize demo setup:', demoError);
            // Continue without demo data - user will need to create company manually
          }
        }
        // If only company ID provided, load company and try to load user
        else if (initialCompanyId) {
          console.log('🏢 Loading company with provided ID');
          companyData = await companyService.getById(initialCompanyId);
          if (initialUserId && companyData) {
            userData = await userService.getById(initialUserId, initialCompanyId);
          }
        }

        setCompanyState(companyData);
        setUserState(userData);
      } catch (error) {
        console.error('❌ Error initializing Firestore context:', error);
      } finally {
        setIsLoading(false);
        setIsInitialized(true);
      }
    }

    initialize();
  }, [auth.isInitialized, auth.firebaseUser, auth.user, auth.company, initialCompanyId, initialUserId]);

  const setCompany = (newCompany: Company | null) => {
    setCompanyState(newCompany);
    // Clear user when company changes
    if (!newCompany) {
      setUserState(null);
    }
  };

  const setUser = (newUser: UserDocument | null) => {
    setUserState(newUser);
  };

  const refreshCompany = async () => {
    if (!company?.id) return;
    
    try {
      const updatedCompany = await companyService.getById(company.id);
      setCompanyState(updatedCompany);
    } catch (error) {
      console.error('Error refreshing company:', error);
    }
  };

  const refreshUser = async () => {
    if (!user?.id || !company?.id) return;
    
    try {
      const updatedUser = await userService.getById(user.id, company.id);
      setUserState(updatedUser);
    } catch (error) {
      console.error('Error refreshing user:', error);
    }
  };

  const hasPermission = (permission: string): boolean => {
    if (!user) return false;
    
    // Admin has all permissions
    if (user.role === 'admin') return true;
    
    // Define permissions for payroll manager
    const payrollManagerPermissions = [
      'view_employees',
      'edit_employees',
      'view_absences',
      'edit_absences',
      'view_overtime',
      'edit_overtime',
      'view_reports',
    ];
    
    if (user.role === 'payroll_manager') {
      return payrollManagerPermissions.includes(permission);
    }
    
    return false;
  };

  const isAdmin = (): boolean => {
    return user?.role === 'admin';
  };

  const isPayrollManager = (): boolean => {
    return user?.role === 'payroll_manager';
  };

  const value: FirestoreContextType = {
    company,
    user,
    isLoading,
    isInitialized,
    setCompany,
    setUser,
    refreshCompany,
    refreshUser,
    hasPermission,
    isAdmin,
    isPayrollManager,
  };

  return (
    <FirestoreContext.Provider value={value}>
      {children}
    </FirestoreContext.Provider>
  );
}

export function useFirestore() {
  const context = useContext(FirestoreContext);
  if (context === undefined) {
    throw new Error('useFirestore must be used within a FirestoreProvider');
  }
  return context;
}

// Hook for getting current company ID
export function useCompanyId(): string | null {
  // Use the legacy company from FirestoreContext for backward compatibility
  // But this should be replaced with CompanyContext usage
  const { company } = useFirestore();
  return company?.id || null;
}

// Hook for getting current user ID
export function useUserId(): string | null {
  const { user } = useFirestore();
  return user?.id || null;
}

// Hook for checking permissions
export function usePermissions() {
  const { hasPermission, isAdmin, isPayrollManager } = useFirestore();
  return { hasPermission, isAdmin, isPayrollManager };
}
