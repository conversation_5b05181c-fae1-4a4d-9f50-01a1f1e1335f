"use client";

import React, { useState, useEffect, useCallback } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useEmployees } from '@/hooks/useEmployeeData';
import { useServices, usePositions } from '@/hooks/useOrganizationalData';
import { useDebounce } from '@/hooks/useDebounce';

import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Switch } from '@/components/ui/switch';

import {
  Search,
  Filter,
  X,
  Users,
  Briefcase,
  Calendar as CalendarIcon,
  CheckCircle,
  XCircle
} from 'lucide-react';

/**
 * Employee Filters Component
 * 
 * Provides comprehensive filtering and search functionality for employees.
 * Maintains filter state in URL parameters for shareable links.
 * 
 * Features:
 * - Real-time search with debouncing
 * - Department and position filtering
 * - Employment status filtering
 * - Date range filtering
 * - Clear all filters
 * - URL state management
 */

interface EmployeeFiltersProps {
  onFiltersChange?: (filters: EmployeeFilterState) => void;
  onClearFilterRef?: (clearFunction: (key: keyof EmployeeFilterState) => void) => void;
  className?: string;
}

export interface EmployeeFilterActions {
  clearFilter: (key: keyof EmployeeFilterState) => void;
  clearAllFilters: () => void;
}

export interface EmployeeFilterState {
  search: string;
  service: string;
  position: string;
  status: 'all' | 'active' | 'inactive';
  startDateFrom: string;
  startDateTo: string;
}

// Special values for "all" options in selects
const ALL_SERVICES = '__all_services__';
const ALL_POSITIONS = '__all_positions__';

export const EmployeeFilters = React.memo(function EmployeeFilters({ onFiltersChange, onClearFilterRef, className }: EmployeeFiltersProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  
  // Note: We don't need to fetch employees here since we're using organizational data hooks
  // const { employees } = useEmployees(); // Removed to prevent unnecessary API calls
  
  const [filters, setFilters] = useState<EmployeeFilterState>({
    search: searchParams.get('search') || '',
    service: searchParams.get('service') || ALL_SERVICES,
    position: searchParams.get('position') || ALL_POSITIONS,
    status: (searchParams.get('status') as 'all' | 'active' | 'inactive') || 'all',
    startDateFrom: searchParams.get('startDateFrom') || '',
    startDateTo: searchParams.get('startDateTo') || '',
  });

  // Debounce search input
  const debouncedSearch = useDebounce(filters.search, 300);

  // Get organizational data for hierarchical filtering
  const { services } = useServices();

  // Get selected service for position filtering
  const selectedServiceName = filters.service === ALL_SERVICES ? undefined : filters.service;
  const selectedService = services.find(s => s.name === selectedServiceName);
  const selectedServiceId = selectedService?.id;

  // Get positions - filtered by service if selected, otherwise all positions
  const { positions: organizationalPositions } = usePositions(selectedServiceId);

  // Get unique values for filter options from actual data
  const positions = organizationalPositions.map(pos => pos.name).sort();

  // Update URL when filters change - memoized with stable reference
  const updateURL = useCallback((newFilters: EmployeeFilterState) => {
    const params = new URLSearchParams();

    Object.entries(newFilters).forEach(([key, value]) => {
      // Convert special "all" values back to empty strings for URL
      let urlValue = value;
      if (value === ALL_SERVICES || value === ALL_POSITIONS || value === 'all') {
        urlValue = '';
      }

      if (urlValue) {
        params.set(key, urlValue);
      }
    });

    const queryString = params.toString();
    const newURL = queryString ? `?${queryString}` : window.location.pathname;

    router.push(newURL, { scroll: false });
  }, [router]);

  // Stable reference for onFiltersChange to prevent re-renders
  const onFiltersChangeRef = React.useRef(onFiltersChange);
  React.useEffect(() => {
    onFiltersChangeRef.current = onFiltersChange;
  });

  // Handle filter changes - optimized to prevent unnecessary re-renders
  const handleFilterChange = useCallback((key: keyof EmployeeFilterState, value: string) => {
    setFilters(prevFilters => {
      const newFilters = { ...prevFilters, [key]: value };

      // Defer URL update and parent notification to avoid render-time state updates
      setTimeout(() => {
        updateURL(newFilters);
        onFiltersChangeRef.current?.(newFilters);
      }, 0);

      return newFilters;
    });
  }, [updateURL]);

  // Clear all filters - optimized
  const clearAllFilters = useCallback(() => {
    const emptyFilters: EmployeeFilterState = {
      search: '',
      service: ALL_SERVICES,
      position: ALL_POSITIONS,
      status: 'all',
      startDateFrom: '',
      startDateTo: '',
    };

    setFilters(emptyFilters);

    // Defer URL update and parent notification to avoid render-time state updates
    setTimeout(() => {
      updateURL(emptyFilters);
      onFiltersChangeRef.current?.(emptyFilters);
    }, 0);
  }, [updateURL]);

  // Clear individual filter - memoized to prevent re-renders
  const clearFilter = useCallback((key: keyof EmployeeFilterState) => {
    let clearValue: any = '';
    if (key === 'service') clearValue = ALL_SERVICES;
    else if (key === 'position') clearValue = ALL_POSITIONS;
    else if (key === 'status') clearValue = 'all';

    handleFilterChange(key, clearValue);
  }, [handleFilterChange]);

  // Notify parent of initial filter state on mount only
  useEffect(() => {
    // Use setTimeout to ensure this happens after the initial render
    const timer = setTimeout(() => {
      onFiltersChangeRef.current?.(filters);
    }, 0);

    return () => clearTimeout(timer);
  }, []); // Empty dependency array - only run on mount

  // Expose clearFilter function to parent - memoized to prevent re-renders
  useEffect(() => {
    onClearFilterRef?.(clearFilter);
  }, [onClearFilterRef, clearFilter]);

  // Count active filters
  const activeFilterCount = Object.entries(filters).filter(([key, value]) => {
    if (key === 'service') return value !== ALL_SERVICES;
    if (key === 'position') return value !== ALL_POSITIONS;
    if (key === 'status') return value !== 'all';
    return value !== '';
  }).length;

  return (
    <div className={`w-full bg-background border rounded-lg p-3 ${className || ''}`}>
      {/* Header */}
      <div className="flex items-center justify-between gap-2 mb-3">
        <h3 className="flex items-center gap-1.5 flex-shrink-0 text-sm font-medium">
          <Filter className="h-4 w-4" />
          Filtres
          {activeFilterCount > 0 && (
            <Badge variant="secondary" className="ml-1 text-xs px-1.5 py-0.5">
              {activeFilterCount}
            </Badge>
          )}
        </h3>

        {activeFilterCount > 0 && (
          <Button
            variant="ghost"
            size="sm"
            onClick={clearAllFilters}
            className="text-muted-foreground hover:text-foreground flex-shrink-0 h-6 w-6 p-0"
            title="Tout effacer"
          >
            <X className="h-3 w-3" />
          </Button>
        )}
      </div>

      {/* Filters Grid - Single Horizontal Line Layout */}
      <div className="grid grid-cols-1 sm:grid-cols-3 lg:grid-cols-6 gap-3">
        {/* Search - Single column for horizontal alignment */}
        <div className="space-y-1.5">
          <label className="text-xs font-medium text-muted-foreground">Recherche</label>
          <div className="relative">
            <Search className="absolute left-2.5 top-1/2 transform -translate-y-1/2 h-3.5 w-3.5 text-muted-foreground" />
            <Input
              placeholder="Rechercher par nom (ex: Ahmed, Fatima)..."
              value={filters.search}
              onChange={(e) => handleFilterChange('search', e.target.value)}
              className="pl-8 h-8 text-sm"
            />
            {filters.search && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => clearFilter('search')}
                className="absolute right-1 top-1/2 transform -translate-y-1/2 h-5 w-5 p-0"
              >
                <X className="h-2.5 w-2.5" />
              </Button>
            )}
          </div>
        </div>

        {/* Service Filter */}
        <div className="space-y-1.5">
          <label className="text-xs font-medium text-muted-foreground flex items-center gap-1.5">
            <Briefcase className="h-3.5 w-3.5" />
            Service
          </label>
          <Select
            value={filters.service}
            onValueChange={(value) => handleFilterChange('service', value)}
          >
            <SelectTrigger className="h-8 text-sm">
              <SelectValue placeholder="Tous les services" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value={ALL_SERVICES}>Tous les services</SelectItem>
              {services.map((service) => (
                <SelectItem key={service.id} value={service.name}>
                  {service.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Position Filter */}
        <div className="space-y-1.5">
          <label className="text-xs font-medium text-muted-foreground flex items-center gap-1.5">
            <Users className="h-3.5 w-3.5" />
            Fonction
          </label>
          <Select
            value={filters.position}
            onValueChange={(value) => handleFilterChange('position', value)}
          >
            <SelectTrigger className="h-8 text-sm">
              <SelectValue placeholder="Toutes les fonctions" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value={ALL_POSITIONS}>Toutes les fonctions</SelectItem>
              {positions.map((pos) => (
                <SelectItem key={pos} value={pos}>
                  {pos}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Status Filter */}
        <div className="space-y-1.5">
          <label className="text-xs font-medium text-muted-foreground">Statut d'emploi</label>
          <Select
            value={filters.status}
            onValueChange={(value) => handleFilterChange('status', value as 'all' | 'active' | 'inactive')}
          >
            <SelectTrigger className="h-8 text-sm">
              <SelectValue placeholder="Tous les statuts" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Tous les statuts</SelectItem>
              <SelectItem value="active">
                <div className="flex items-center gap-1.5">
                  <CheckCircle className="h-3.5 w-3.5 text-green-500" />
                  Actif seulement
                </div>
              </SelectItem>
              <SelectItem value="inactive">
                <div className="flex items-center gap-1.5">
                  <XCircle className="h-3.5 w-3.5 text-red-500" />
                  Inactif seulement
                </div>
              </SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Start Date Filter */}
        <div className="space-y-1.5">
          <label className="text-xs font-medium text-muted-foreground flex items-center gap-1.5">
            <CalendarIcon className="h-3.5 w-3.5" />
            Date de début
          </label>
          <div className="relative">
            <CalendarIcon className="absolute left-2.5 top-1/2 transform -translate-y-1/2 h-3.5 w-3.5 text-muted-foreground pointer-events-none" />
            <Input
              type="date"
              value={filters.startDateFrom}
              onChange={(e) => handleFilterChange('startDateFrom', e.target.value)}
              className="w-full h-8 text-sm pl-8"
            />
          </div>
        </div>

        {/* End Date Filter */}
        <div className="space-y-1.5">
          <label className="text-xs font-medium text-muted-foreground flex items-center gap-1.5">
            <CalendarIcon className="h-3.5 w-3.5" />
            Date de fin
          </label>
          <div className="relative">
            <CalendarIcon className="absolute left-2.5 top-1/2 transform -translate-y-1/2 h-3.5 w-3.5 text-muted-foreground pointer-events-none" />
            <Input
              type="date"
              value={filters.startDateTo}
              onChange={(e) => handleFilterChange('startDateTo', e.target.value)}
              className="w-full h-8 text-sm pl-8"
            />
          </div>
        </div>
      </div>
    </div>
  );
});
