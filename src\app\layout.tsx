import type { Metadata } from 'next';
import './globals.css';
import { Toaster } from "@/components/ui/toaster";
import { APP_NAME_DEFAULT } from '@/lib/constants';
import { I18nProvider } from '@/context/i18nContext';
import { AuthProvider } from '@/context/AuthContext';
import { FirestoreProvider } from '@/context/FirestoreContext';
import { RBACProvider } from '@/context/RBACContext';
import { CompanyProvider } from '@/context/CompanyContext';
import { ThemeProvider } from '@/context/ThemeContext';
import { FontProvider } from '@/context/FontContext';
import { RBACErrorBoundary } from '@/components/auth/RBACErrorBoundary';

export const metadata: Metadata = {
  title: {
    default: APP_NAME_DEFAULT,
    template: `%s | ${APP_NAME_DEFAULT}`,
  },
  description: 'Simplified Payroll and HR Management',
};

// The lang attribute will be updated by I18nProvider client-side.
// suppressHydrationWarning is used on the html tag.
export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (<html lang="en" suppressHydrationWarning>
      <head>
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap" rel="stylesheet" />
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;700&display=swap" rel="stylesheet" />
        <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;500;700&display=swap" rel="stylesheet" />
        <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;500;700&display=swap" rel="stylesheet" />
        <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@400;500;700&display=swap" rel="stylesheet" />
        <link href="https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&display=swap" rel="stylesheet" />
        <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@700;800&display=swap" rel="stylesheet" />
      </head>
      <body className="font-body antialiased min-h-screen flex flex-col">
        <I18nProvider>
          <ThemeProvider>
            <FontProvider>
              <AuthProvider>
                <FirestoreProvider>
                  <RBACErrorBoundary>
                    <RBACProvider>
                      <CompanyProvider>
                        {children}
                      </CompanyProvider>
                    </RBACProvider>
                  </RBACErrorBoundary>
                </FirestoreProvider>
              </AuthProvider>
            </FontProvider>
          </ThemeProvider>
        </I18nProvider>
        <Toaster />
      </body>
    </html>);
}
