/**
 * Role-Based Access Control (RBAC) Types for WePaie
 * 
 * Defines the complete role hierarchy and permission system
 * for multi-tenant authentication and authorization.
 */

// User Roles in the system
export type UserRole = 'super_admin' | 'company_admin' | 'editor' | 'viewer';

// Permission categories
export type PermissionCategory = 
  | 'users'           // User management
  | 'companies'       // Company management  
  | 'employees'       // Employee data
  | 'time'           // Time management
  | 'payroll'        // Payroll operations
  | 'settings'       // System settings
  | 'audit'          // Audit logs
  | 'analytics';     // Analytics and reports

// Specific permissions within each category
export type Permission = 
  // User management permissions
  | 'users.create'
  | 'users.read'
  | 'users.update'
  | 'users.delete'
  | 'users.assign_roles'
  
  // Company management permissions
  | 'companies.create'
  | 'companies.read'
  | 'companies.update'
  | 'companies.delete'
  | 'companies.manage_settings'
  
  // Employee data permissions
  | 'employees.create'
  | 'employees.read'
  | 'employees.update'
  | 'employees.delete'
  
  // Time management permissions
  | 'time.create'
  | 'time.read'
  | 'time.update'
  | 'time.delete'
  
  // Payroll permissions
  | 'payroll.create'
  | 'payroll.read'
  | 'payroll.update'
  | 'payroll.delete'
  | 'payroll.calculate'
  
  // Settings permissions
  | 'settings.read'
  | 'settings.update'
  | 'settings.system'
  
  // Audit permissions
  | 'audit.read'
  | 'audit.export'
  
  // Analytics permissions
  | 'analytics.read'
  | 'analytics.export'
  | 'analytics.global';

// Firebase Custom Claims structure
export interface CustomClaims {
  role: UserRole;
  companyId?: string;
  permissions?: Permission[];
  
  // Legacy compatibility flags
  isAdmin?: boolean;
  isSuperAdmin?: boolean;
  isPayrollManager?: boolean;
  isViewer?: boolean;
}

// User data structure with role information
export interface RBACUser {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: UserRole;
  companyId?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  lastLoginAt?: string;
  
  // Profile information
  phoneNumber?: string;
  profilePicture?: string;
  
  // Audit fields
  createdBy?: string;
  updatedBy?: string;
}

// Company data structure
export interface Company {
  id: string;
  name: string;
  email: string;
  phone?: string;
  address?: string;
  industry?: string;
  logo?: string;
  
  // Status and settings
  isActive: boolean;
  subscriptionStatus?: 'active' | 'inactive' | 'trial' | 'suspended';
  maxUsers?: number;
  
  // Timestamps
  createdAt: string;
  updatedAt: string;
  
  // Audit fields
  createdBy?: string;
  updatedBy?: string;
}

// Role configuration with permissions
export interface RoleConfig {
  role: UserRole;
  name: string;
  nameFr: string;
  description: string;
  descriptionFr: string;
  permissions: Permission[];
  canManageRoles?: UserRole[];
  dataScope: 'global' | 'company' | 'personal';
}

// Permission check context
export interface PermissionContext {
  user: RBACUser;
  targetCompanyId?: string;
  targetUserId?: string;
  resource?: any;
}

// Audit log entry
export interface AuditLogEntry {
  id: string;
  userId: string;
  userEmail: string;
  companyId?: string;
  action: string;
  resource: string;
  resourceId?: string;
  details?: Record<string, any>;
  timestamp: string;
  ipAddress?: string;
  userAgent?: string;
}

// User creation request
export interface CreateUserRequest {
  email: string;
  firstName: string;
  lastName: string;
  role: UserRole;
  companyId?: string;
  phoneNumber?: string;
  sendWelcomeEmail?: boolean;
}

// User update request
export interface UpdateUserRequest {
  firstName?: string;
  lastName?: string;
  role?: UserRole;
  companyId?: string;
  phoneNumber?: string;
  isActive?: boolean;
}

// Company creation request
export interface CreateCompanyRequest {
  name: string;
  email: string;
  phone?: string;
  address?: string;
  industry?: string;
  adminEmail: string;
  adminFirstName: string;
  adminLastName: string;
  maxUsers?: number;
}

// Company update request
export interface UpdateCompanyRequest {
  name?: string;
  email?: string;
  phone?: string;
  address?: string;
  industry?: string;
  logo?: string;
  isActive?: boolean;
  maxUsers?: number;
}

// Permission check result
export interface PermissionResult {
  allowed: boolean;
  reason?: string;
  requiredRole?: UserRole;
  requiredPermission?: Permission;
}

// Navigation item with role requirements
export interface RBACNavItem {
  href: string;
  labelKey: string;
  icon: any;
  requiredRole?: UserRole;
  requiredPermissions?: Permission[];
  matchExact?: boolean;
}

// Page access configuration
export interface PageAccess {
  path: string;
  requiredRole?: UserRole;
  requiredPermissions?: Permission[];
  allowedRoles?: UserRole[];
}
