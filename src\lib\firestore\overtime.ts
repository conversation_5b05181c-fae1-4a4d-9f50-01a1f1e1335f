import { BaseFirestoreService } from './base';
import type { OvertimeDocument, QueryOptions } from './types';
import type { Overtime, OvertimeCategory } from '@/types';

/**
 * Overtime service for managing overtime data
 */
export class OvertimeService extends BaseFirestoreService<OvertimeDocument> {
  constructor() {
    super('overtime');
  }

  /**
   * Create new overtime record
   */
  async createOvertime(
    overtimeData: Omit<Overtime, 'id'>, 
    companyId: string
  ): Promise<OvertimeDocument> {
    const data = {
      ...overtimeData,
      status: 'pending' as const,
    };

    return this.create(data as any, companyId);
  }

  /**
   * Create multiple overtime records (bulk operation)
   */
  async createBulkOvertime(
    employeeIds: string[],
    overtimeData: Omit<Overtime, 'id' | 'employeeId'>,
    companyId: string
  ): Promise<OvertimeDocument[]> {
    const promises = employeeIds.map(employeeId => 
      this.createOvertime({ ...overtimeData, employeeId }, companyId)
    );

    return Promise.all(promises);
  }

  /**
   * Update overtime record
   */
  async updateOvertime(
    id: string, 
    updates: Partial<Overtime>, 
    companyId: string
  ): Promise<OvertimeDocument> {
    return this.update(id, updates as any, companyId);
  }

  /**
   * Approve overtime
   */
  async approveOvertime(
    id: string, 
    approvedBy: string, 
    companyId: string
  ): Promise<OvertimeDocument> {
    return this.update(id, {
      status: 'approved',
      approvedBy,
      approvedAt: new Date(),
    } as any, companyId);
  }

  /**
   * Reject overtime
   */
  async rejectOvertime(
    id: string, 
    rejectedBy: string, 
    rejectionReason: string, 
    companyId: string
  ): Promise<OvertimeDocument> {
    return this.update(id, {
      status: 'rejected',
      approvedBy: rejectedBy,
      approvedAt: new Date(),
      rejectionReason,
    } as any, companyId);
  }

  /**
   * Get overtime records for specific employee
   */
  async getOvertimeByEmployee(
    employeeId: string, 
    companyId: string, 
    options: QueryOptions = {}
  ): Promise<OvertimeDocument[]> {
    const whereClause = [
      { field: 'employeeId', operator: '==' as const, value: employeeId },
      ...(options.where || [])
    ];

    return this.getAll(companyId, { ...options, where: whereClause });
  }

  /**
   * Get overtime records by status
   */
  async getOvertimeByStatus(
    status: 'pending' | 'approved' | 'rejected', 
    companyId: string, 
    options: QueryOptions = {}
  ): Promise<OvertimeDocument[]> {
    const whereClause = [
      { field: 'status', operator: '==' as const, value: status },
      ...(options.where || [])
    ];

    return this.getAll(companyId, { ...options, where: whereClause });
  }

  /**
   * Get overtime records by category
   */
  async getOvertimeByCategory(
    category: OvertimeCategory, 
    companyId: string, 
    options: QueryOptions = {}
  ): Promise<OvertimeDocument[]> {
    const whereClause = [
      { field: 'category', operator: '==' as const, value: category },
      ...(options.where || [])
    ];

    return this.getAll(companyId, { ...options, where: whereClause });
  }

  /**
   * Get overtime records in date range
   */
  async getOvertimeInDateRange(
    startDate: Date, 
    endDate: Date, 
    companyId: string, 
    options: QueryOptions = {}
  ): Promise<OvertimeDocument[]> {
    const whereClause = [
      { field: 'date', operator: '>=' as const, value: startDate.toISOString() },
      { field: 'date', operator: '<=' as const, value: endDate.toISOString() },
      ...(options.where || [])
    ];

    return this.getAll(companyId, { ...options, where: whereClause });
  }

  /**
   * Get pending overtime records (for approval workflow)
   */
  async getPendingOvertime(companyId: string, options: QueryOptions = {}): Promise<OvertimeDocument[]> {
    return this.getOvertimeByStatus('pending', companyId, options);
  }

  /**
   * Get overtime statistics
   */
  async getOvertimeStats(companyId: string, year?: number): Promise<{
    total: number;
    byStatus: Record<string, number>;
    byCategory: Record<OvertimeCategory, number>;
    byMonth: Record<string, number>;
    totalHours: number;
    averageHours: number;
  }> {
    let overtime = await this.getAll(companyId);

    // Filter by year if specified
    if (year) {
      overtime = overtime.filter(ot => {
        const otYear = new Date(ot.date).getFullYear();
        return otYear === year;
      });
    }

    const stats = {
      total: overtime.length,
      byStatus: {} as Record<string, number>,
      byCategory: {} as Record<OvertimeCategory, number>,
      byMonth: {} as Record<string, number>,
      totalHours: 0,
      averageHours: 0,
    };

    overtime.forEach(ot => {
      // Count by status
      stats.byStatus[ot.status] = (stats.byStatus[ot.status] || 0) + 1;

      // Count by category
      stats.byCategory[ot.category] = (stats.byCategory[ot.category] || 0) + 1;

      // Count by month
      const month = new Date(ot.date).toLocaleString('default', { month: 'long' });
      stats.byMonth[month] = (stats.byMonth[month] || 0) + 1;

      // Sum total hours
      stats.totalHours += ot.hours;
    });

    // Calculate average hours
    stats.averageHours = overtime.length > 0 ? stats.totalHours / overtime.length : 0;

    return stats;
  }

  /**
   * Get overtime hours for employee in a specific period
   */
  async getEmployeeOvertimeHours(
    employeeId: string,
    startDate: Date,
    endDate: Date,
    companyId: string
  ): Promise<{
    totalHours: number;
    byCategory: Record<OvertimeCategory, number>;
    records: OvertimeDocument[];
  }> {
    const whereClause = [
      { field: 'employeeId', operator: '==' as const, value: employeeId },
      { field: 'date', operator: '>=' as const, value: startDate.toISOString() },
      { field: 'date', operator: '<=' as const, value: endDate.toISOString() },
      { field: 'status', operator: '==' as const, value: 'approved' }, // Only approved overtime
    ];

    const records = await this.getAll(companyId, { where: whereClause });

    const result = {
      totalHours: 0,
      byCategory: {} as Record<OvertimeCategory, number>,
      records,
    };

    records.forEach(ot => {
      result.totalHours += ot.hours;
      result.byCategory[ot.category] = (result.byCategory[ot.category] || 0) + ot.hours;
    });

    return result;
  }

  /**
   * Check for duplicate overtime entries
   */
  async checkDuplicateOvertime(
    employeeId: string,
    date: Date,
    companyId: string,
    excludeId?: string
  ): Promise<OvertimeDocument[]> {
    const dateString = date.toISOString().split('T')[0]; // Get date part only
    const employeeOvertime = await this.getOvertimeByEmployee(employeeId, companyId);

    return employeeOvertime.filter(ot => {
      if (excludeId && ot.id === excludeId) {
        return false;
      }

      const otDateString = new Date(ot.date).toISOString().split('T')[0];
      return otDateString === dateString;
    });
  }

  /**
   * Convert OvertimeDocument to Overtime (for compatibility with existing code)
   */
  toOvertime(overtimeDoc: OvertimeDocument): Overtime {
    const { 
      approvedBy, 
      approvedAt, 
      status, 
      rejectionReason, 
      createdAt, 
      updatedAt, 
      companyId, 
      ...overtime 
    } = overtimeDoc;
    
    return overtime as Overtime;
  }

  /**
   * Convert multiple OvertimeDocuments to Overtime records
   */
  toOvertimes(overtimeDocs: OvertimeDocument[]): Overtime[] {
    return overtimeDocs.map(doc => this.toOvertime(doc));
  }
}

// Export singleton instance
export const overtimeService = new OvertimeService();
