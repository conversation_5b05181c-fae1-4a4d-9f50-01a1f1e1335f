// Thème par défaut - Mode clair
export const defaultLight = {
  '--background': '200 20% 98%', // Light cool gray
  '--foreground': '210 10% 23%', // Dark gray for text
  
  '--card': '0 0% 100%', // White card
  '--card-foreground': '210 10% 23%', // Dark text on card
  
  '--popover': '0 0% 100%',
  '--popover-foreground': '210 10% 23%',
  
  '--primary': '207 68% 53%', // Vibrant Blue #3498db
  '--primary-foreground': '210 40% 98%', // Light text on primary
  
  '--secondary': '210 17% 95%', // Clean White/Light Gray #ecf0f1
  '--secondary-foreground': '210 10% 23%', // Dark text on secondary
  
  '--muted': '210 17% 95%', // Same as secondary for muted backgrounds
  '--muted-foreground': '210 10% 45%', // Slightly lighter dark gray for muted text
  
  '--accent': '186 80% 56%', // Bright Sky Blue #34d9eb
  '--accent-foreground': '210 10% 23%', // Dark text on accent
  
  '--destructive': '0 84.2% 60.2%',
  '--destructive-foreground': '0 0% 98%',
  
  '--border': '210 15% 88%',
  '--input': '210 15% 88%',
  '--ring': '186 80% 56%', // Accent color for rings
  
  '--radius': '0.375rem',
  
  '--chart-1': '207 68% 53%',
  '--chart-2': '186 80% 56%',
  '--chart-3': '210 30% 60%',
  '--chart-4': '200 50% 70%',
  '--chart-5': '190 60% 65%',
  
  // Sidebar specific colors - Light Theme
  '--sidebar-background': '210 20% 96%',
  '--sidebar-foreground': '210 10% 30%',
  '--sidebar-primary': '207 68% 53%', // Active item background
  '--sidebar-primary-foreground': '210 40% 98%', // Active item text
  '--sidebar-accent': '200 20% 92%', // Hover item background
  '--sidebar-accent-foreground': '207 68% 45%', // Hover item text
  '--sidebar-border': '210 15% 88%',
  '--sidebar-ring': '186 80% 56%',
};
