"use client";

import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useCompany } from '@/context/CompanyContext';
import { useRBAC } from '@/context/RBACContext';
import { 
  Building2, 
  Users, 
  Shield, 
  CheckCircle,
  AlertTriangle,
  RefreshCw
} from 'lucide-react';

/**
 * Company Context Display Component
 * 
 * Shows the current company context in the UI and provides
 * company selection interface for super admins.
 */
export function CompanyContextDisplay() {
  const { 
    selectedCompany, 
    availableCompanies, 
    selectCompany, 
    clearCompany,
    isLoading 
  } = useCompany();
  
  const { isSuperAdmin, user: rbacUser } = useRBAC();

  if (isLoading) {
    return (
      <Card className="border-muted">
        <CardContent className="p-3">
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <RefreshCw className="h-4 w-4 animate-spin" />
            <span>Chargement du contexte d'entreprise...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Super admin with company selection
  if (isSuperAdmin()) {
    return (
      <Card className="border-blue-200 bg-blue-50">
        <CardContent className="p-3">
          <div className="flex items-center justify-between gap-3">
            <div className="flex items-center gap-2">
              <Shield className="h-4 w-4 text-blue-600" />
              <span className="text-sm font-medium text-blue-800">Super Admin</span>
            </div>
            
            {availableCompanies.length > 0 ? (
              <div className="flex items-center gap-2">
                <span className="text-xs text-blue-700">Entreprise:</span>
                <Select
                  value={selectedCompany?.id || "none"}
                  onValueChange={(value) => {
                    if (value === "none") {
                      clearCompany();
                    } else {
                      const company = availableCompanies.find(c => c.id === value);
                      if (company) {
                        selectCompany(company);
                      }
                    }
                  }}
                >
                  <SelectTrigger className="w-48 h-8 text-xs">
                    <SelectValue placeholder="Sélectionner une entreprise" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">
                      <span className="text-muted-foreground">Aucune sélection</span>
                    </SelectItem>
                    {availableCompanies.map((company) => (
                      <SelectItem key={company.id} value={company.id}>
                        <div className="flex items-center gap-2">
                          <span>{company.name}</span>
                          {company.isActive ? (
                            <CheckCircle className="h-3 w-3 text-green-600" />
                          ) : (
                            <AlertTriangle className="h-3 w-3 text-amber-600" />
                          )}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            ) : (
              <span className="text-xs text-blue-700">Aucune entreprise disponible</span>
            )}
          </div>
        </CardContent>
      </Card>
    );
  }

  // Company admin or regular user with auto-selected company
  if (selectedCompany) {
    const isAutoSelected = localStorage.getItem('autoSelectedCompany') === 'true';
    
    return (
      <Card className="border-green-200 bg-green-50">
        <CardContent className="p-3">
          <div className="flex items-center justify-between gap-3">
            <div className="flex items-center gap-2">
              <Building2 className="h-4 w-4 text-green-600" />
              <span className="text-sm font-medium text-green-800">{selectedCompany.name}</span>
              {isAutoSelected && (
                <Badge variant="outline" className="text-xs bg-white border-green-300 text-green-700">
                  Auto-sélectionnée
                </Badge>
              )}
            </div>
            
            <div className="flex items-center gap-2 text-xs text-green-700">
              {selectedCompany.isActive ? (
                <>
                  <CheckCircle className="h-3 w-3" />
                  <span>Active</span>
                </>
              ) : (
                <>
                  <AlertTriangle className="h-3 w-3" />
                  <span>Inactive</span>
                </>
              )}
            </div>
          </div>
          
          {rbacUser && (
            <div className="mt-2 pt-2 border-t border-green-200">
              <div className="flex items-center gap-2 text-xs text-green-700">
                <Users className="h-3 w-3" />
                <span>
                  {rbacUser.role === 'company_admin' ? 'Administrateur d\'Entreprise' :
                   rbacUser.role === 'editor' ? 'Éditeur' : 'Observateur'}
                </span>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    );
  }

  // No company selected
  return (
    <Card className="border-amber-200 bg-amber-50">
      <CardContent className="p-3">
        <div className="flex items-center gap-2 text-sm text-amber-800">
          <AlertTriangle className="h-4 w-4" />
          <span>Aucune entreprise sélectionnée</span>
        </div>
      </CardContent>
    </Card>
  );
}

/**
 * Compact Company Context Display for Header
 *
 * Minimal display suitable for navigation headers.
 */
export function CompanyContextHeader() {
  const { selectedCompany, isLoading } = useCompany();
  const { isSuperAdmin, user: rbacUser } = useRBAC();

  if (isLoading) {
    return (
      <div className="flex items-center gap-2 text-sm text-muted-foreground">
        <RefreshCw className="h-4 w-4 animate-spin" />
        <span>Chargement...</span>
      </div>
    );
  }

  if (isSuperAdmin()) {
    return (
      <div className="flex items-center gap-2 text-sm">
        <Shield className="h-4 w-4 text-blue-600" />
        <span className="font-medium text-blue-800">Super Admin</span>
        {selectedCompany && (
          <>
            <span className="text-muted-foreground">•</span>
            <span className="text-muted-foreground">{selectedCompany.name}</span>
          </>
        )}
      </div>
    );
  }

  if (selectedCompany) {
    return (
      <div className="flex items-center gap-2 text-sm">
        <Building2 className="h-4 w-4 text-green-600" />
        <span className="font-medium">{selectedCompany.name}</span>
        {rbacUser && (
          <Badge variant="outline" className="text-xs">
            {rbacUser.role === 'company_admin' ? 'Admin' :
             rbacUser.role === 'editor' ? 'Éditeur' : 'Observateur'}
          </Badge>
        )}
      </div>
    );
  }

  return (
    <div className="flex items-center gap-2 text-sm text-amber-600">
      <AlertTriangle className="h-4 w-4" />
      <span>Aucune entreprise</span>
    </div>
  );
}

/**
 * Company Header Selector for Superuser
 *
 * Compact company selector for the main header, only visible to superusers.
 */
export function CompanyHeaderSelector() {
  const {
    selectedCompany,
    availableCompanies,
    selectCompany,
    clearCompany,
    isLoading
  } = useCompany();

  const { isSuperAdmin } = useRBAC();

  // Only show for superuser
  if (!isSuperAdmin()) {
    return null;
  }

  if (isLoading) {
    return (
      <div className="flex items-center gap-2 text-sm text-muted-foreground">
        <RefreshCw className="h-4 w-4 animate-spin" />
        <span>Chargement...</span>
      </div>
    );
  }

  const handleCompanyChange = (value: string) => {
    if (value === "none") {
      clearCompany();
    } else {
      const company = availableCompanies.find(c => c.id === value);
      if (company) {
        selectCompany(company);
      }
    }
  };

  return (
    <div className="flex items-center gap-2">
      <Shield className="h-4 w-4 text-blue-600" />
      <Select
        value={selectedCompany?.id || "none"}
        onValueChange={handleCompanyChange}
      >
        <SelectTrigger className="w-48 h-8 text-xs">
          <SelectValue placeholder="Sélectionner une entreprise" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="none">
            <span className="text-muted-foreground">Aucune sélection</span>
          </SelectItem>
          {availableCompanies.map((company) => (
            <SelectItem key={company.id} value={company.id}>
              <div className="flex items-center gap-2">
                <span>{company.name}</span>
                {company.isActive ? (
                  <CheckCircle className="h-3 w-3 text-green-600" />
                ) : (
                  <AlertTriangle className="h-3 w-3 text-amber-600" />
                )}
              </div>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
}

/**
 * Company Context Selector for Superuser
 * 
 * Dedicated component for superuser company selection in specific contexts.
 */
export function CompanyContextSelector({ 
  onCompanySelected,
  placeholder = "Sélectionner une entreprise pour continuer"
}: {
  onCompanySelected?: (companyId: string) => void;
  placeholder?: string;
}) {
  const { availableCompanies, selectCompany, selectedCompany } = useCompany();
  const { isSuperAdmin } = useRBAC();

  if (!isSuperAdmin()) {
    return null;
  }

  return (
    <Card>
      <CardContent className="p-6">
        <div className="text-center space-y-4">
          <div className="flex items-center justify-center gap-2 text-blue-600">
            <Shield className="h-6 w-6" />
            <span className="font-medium">Sélection d'Entreprise Requise</span>
          </div>
          
          <p className="text-sm text-muted-foreground">
            {placeholder}
          </p>
          
          {availableCompanies.length > 0 ? (
            <Select
              value={selectedCompany?.id || ""}
              onValueChange={(value) => {
                const company = availableCompanies.find(c => c.id === value);
                if (company) {
                  selectCompany(company);
                  onCompanySelected?.(company.id);
                }
              }}
            >
              <SelectTrigger className="w-full max-w-sm mx-auto">
                <SelectValue placeholder="Choisir une entreprise" />
              </SelectTrigger>
              <SelectContent>
                {availableCompanies.map((company) => (
                  <SelectItem key={company.id} value={company.id}>
                    <div className="flex items-center gap-2">
                      <span>{company.name}</span>
                      {company.isActive ? (
                        <CheckCircle className="h-3 w-3 text-green-600" />
                      ) : (
                        <AlertTriangle className="h-3 w-3 text-amber-600" />
                      )}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          ) : (
            <div className="text-center">
              <p className="text-sm text-muted-foreground mb-4">
                Aucune entreprise disponible.
              </p>
              <Button onClick={() => window.location.href = '/admin'}>
                Créer une Entreprise
              </Button>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
