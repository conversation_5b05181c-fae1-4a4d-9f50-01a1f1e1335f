"use client";

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import { Loader2, Building2, Users, CheckCircle } from 'lucide-react';
import { initializeCompany, createAdminUser, employeeService } from '@/lib/firestore';
import { useFirestore } from '@/context/FirestoreContext';
import type { Employee } from '@/types';

interface CompanySetupProps {
  onComplete?: (companyId: string) => void;
}

export function CompanySetup({ onComplete }: CompanySetupProps) {
  const [step, setStep] = useState<'company' | 'admin' | 'sample' | 'complete'>('company');
  const [isLoading, setIsLoading] = useState(false);
  const [companyId, setCompanyId] = useState<string | null>(null);
  const { toast } = useToast();
  const { setCompany, setUser } = useFirestore();

  // Form data
  const [companyData, setCompanyData] = useState({
    name: '',
    email: '',
    phone: '',
    address: ''
  });

  const [adminData, setAdminData] = useState({
    firstName: '',
    lastName: '',
    email: '',
  });

  const handleCompanySubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const company = await initializeCompany(companyData);
      setCompanyId(company.id);
      setCompany(company);
      
      toast({
        title: "Company Created",
        description: `${company.name} has been successfully created.`
      });
      
      setStep('admin');
    } catch (error) {
      console.error('Error creating company:', error);
      toast({
        title: "Error",
        description: "Failed to create company. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleAdminSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!companyId) return;

    setIsLoading(true);

    try {
      const admin = await createAdminUser(companyId, {
        ...adminData,
        name: `${adminData.firstName} ${adminData.lastName}`
      });
      
      setUser(admin);
      
      toast({
        title: "Admin User Created",
        description: `Admin user ${admin.firstName} ${admin.lastName} has been created.`
      });
      
      setStep('sample');
    } catch (error) {
      console.error('Error creating admin user:', error);
      toast({
        title: "Error",
        description: "Failed to create admin user. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const createSampleData = async () => {
    if (!companyId) return;

    setIsLoading(true);

    try {
      const sampleEmployees: Omit<Employee, 'id'>[] = [
        {
          firstName: 'Alice',
          lastName: 'Johnson',
          email: '<EMAIL>',
          department: 'Engineering',
          service: 'Frontend',
          position: 'Senior Developer',
          hourlyRate: 55,
          hoursPerWeek: 40,
          overtimeRate: 82.5,
          weeklySalary: 2200,
          status: 'Active',
          hireDate: '2022-01-15',
          phoneNumber: '555-0101',
          address: '123 Tech Street, San Francisco, CA'
        },
        {
          firstName: 'Bob',
          lastName: 'Smith',
          email: '<EMAIL>',
          department: 'Operations',
          service: 'Management',
          position: 'Operations Manager',
          hourlyRate: 60,
          hoursPerWeek: 40,
          overtimeRate: 90,
          weeklySalary: 2400,
          status: 'Active',
          hireDate: '2021-03-10',
          phoneNumber: '555-0102',
          address: '456 Business Ave, New York, NY'
        },
        {
          firstName: 'Carol',
          lastName: 'Davis',
          email: '<EMAIL>',
          department: 'Marketing',
          service: 'Digital Marketing',
          position: 'Marketing Specialist',
          hourlyRate: 45,
          hoursPerWeek: 35,
          overtimeRate: 67.5,
          weeklySalary: 1575,
          status: 'Active',
          hireDate: '2023-05-20',
          phoneNumber: '555-0103',
          address: '789 Creative Blvd, Los Angeles, CA'
        }
      ];

      // Create sample employees
      await Promise.all(
        sampleEmployees.map(employee => 
          employeeService.createEmployee(employee, companyId)
        )
      );

      toast({
        title: "Sample Data Created",
        description: `${sampleEmployees.length} sample employees have been added.`
      });

      setStep('complete');
    } catch (error) {
      console.error('Error creating sample data:', error);
      toast({
        title: "Error",
        description: "Failed to create sample data. You can add employees manually later.",
        variant: "destructive"
      });
      setStep('complete');
    } finally {
      setIsLoading(false);
    }
  };

  const handleComplete = () => {
    if (companyId && onComplete) {
      onComplete(companyId);
    }
  };

  if (step === 'complete') {
    return (
      <Card className="w-full max-w-md mx-auto">
        <CardHeader className="text-center">
          <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
          <CardTitle className="font-headline text-2xl">Setup Complete!</CardTitle>
          <CardDescription>
            Your company has been successfully set up and you're ready to start using WePaie.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Button onClick={handleComplete} className="w-full">
            Get Started
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="w-full max-w-md mx-auto space-y-6">
      {/* Progress indicator */}
      <div className="flex items-center justify-center space-x-4">
        <div className={`flex items-center ${step === 'company' ? 'text-primary' : 'text-muted-foreground'}`}>
          <Building2 className="h-5 w-5" />
          <span className="ml-2 text-sm">Company</span>
        </div>
        <div className="h-px bg-border flex-1" />
        <div className={`flex items-center ${step === 'admin' ? 'text-primary' : 'text-muted-foreground'}`}>
          <Users className="h-5 w-5" />
          <span className="ml-2 text-sm">Admin</span>
        </div>
        <div className="h-px bg-border flex-1" />
        <div className={`flex items-center ${step === 'sample' ? 'text-primary' : 'text-muted-foreground'}`}>
          <CheckCircle className="h-5 w-5" />
          <span className="ml-2 text-sm">Setup</span>
        </div>
      </div>

      {/* Company setup */}
      {step === 'company' && (
        <Card>
          <CardHeader>
            <CardTitle className="font-headline">Create Your Company</CardTitle>
            <CardDescription>
              Enter your company information to get started with WePaie.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleCompanySubmit} className="space-y-4">
              <div>
                <Label htmlFor="companyName">Company Name *</Label>
                <Input
                  id="companyName"
                  value={companyData.name}
                  onChange={(e) => setCompanyData(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="Your Company LLC"
                  required
                />
              </div>
              <div>
                <Label htmlFor="companyEmail">Company Email *</Label>
                <Input
                  id="companyEmail"
                  type="email"
                  value={companyData.email}
                  onChange={(e) => setCompanyData(prev => ({ ...prev, email: e.target.value }))}
                  placeholder="<EMAIL>"
                  required
                />
              </div>
              <div>
                <Label htmlFor="companyPhone">Phone</Label>
                <Input
                  id="companyPhone"
                  value={companyData.phone}
                  onChange={(e) => setCompanyData(prev => ({ ...prev, phone: e.target.value }))}
                  placeholder="+****************"
                />
              </div>
              <div>
                <Label htmlFor="companyAddress">Address</Label>
                <Input
                  id="companyAddress"
                  value={companyData.address}
                  onChange={(e) => setCompanyData(prev => ({ ...prev, address: e.target.value }))}
                  placeholder="123 Business St, City, State"
                />
              </div>
              <Button type="submit" className="w-full" disabled={isLoading}>
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Creating Company...
                  </>
                ) : (
                  'Create Company'
                )}
              </Button>
            </form>
          </CardContent>
        </Card>
      )}

      {/* Admin setup */}
      {step === 'admin' && (
        <Card>
          <CardHeader>
            <CardTitle className="font-headline">Create Admin User</CardTitle>
            <CardDescription>
              Create the first admin user for your company.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleAdminSubmit} className="space-y-4">
              <div>
                <Label htmlFor="firstName">First Name *</Label>
                <Input
                  id="firstName"
                  value={adminData.firstName}
                  onChange={(e) => setAdminData(prev => ({ ...prev, firstName: e.target.value }))}
                  placeholder="John"
                  required
                />
              </div>
              <div>
                <Label htmlFor="lastName">Last Name *</Label>
                <Input
                  id="lastName"
                  value={adminData.lastName}
                  onChange={(e) => setAdminData(prev => ({ ...prev, lastName: e.target.value }))}
                  placeholder="Doe"
                  required
                />
              </div>
              <div>
                <Label htmlFor="adminEmail">Email *</Label>
                <Input
                  id="adminEmail"
                  type="email"
                  value={adminData.email}
                  onChange={(e) => setAdminData(prev => ({ ...prev, email: e.target.value }))}
                  placeholder="<EMAIL>"
                  required
                />
              </div>
              <Button type="submit" className="w-full" disabled={isLoading}>
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Creating Admin...
                  </>
                ) : (
                  'Create Admin User'
                )}
              </Button>
            </form>
          </CardContent>
        </Card>
      )}

      {/* Sample data setup */}
      {step === 'sample' && (
        <Card>
          <CardHeader>
            <CardTitle className="font-headline">Add Sample Data</CardTitle>
            <CardDescription>
              Would you like to add some sample employees to get started?
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button onClick={createSampleData} className="w-full" disabled={isLoading}>
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Creating Sample Data...
                </>
              ) : (
                'Add Sample Employees'
              )}
            </Button>
            <Button 
              variant="outline" 
              onClick={() => setStep('complete')} 
              className="w-full"
              disabled={isLoading}
            >
              Skip Sample Data
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
