"use client";

import * as React from "react";
import type { Employee } from "@/types";
import { useTranslation } from "@/context/i18nContext";
import { useFirestore, useCompanyId, useUserId, usePermissions } from "@/context/FirestoreContext";
import { useRBAC } from "@/context/RBACContext";
import { useCompany } from "@/context/CompanyContext";
import { CompanySelectionGuard } from "@/components/company/CompanySelectionGuard";
import { useEmployees, useDeleteEmployee } from "@/hooks/useEmployeeData";
import { employeeService } from "@/lib/firestore";
import type { EmployeeDocument } from "@/lib/firestore/types";
import { EmployeeFilters, type EmployeeFilterState } from "./EmployeeFilters";
import { ActiveFiltersDisplay } from "./ActiveFiltersDisplay";

// Import the special filter constants
const ALL_POSITIONS = '__all_positions__';
const ALL_STATUSES = '__all_statuses__';
import { CompanySelector } from "@/components/company/CompanySelector";
import { LoadingScreen } from "@/components/ui/loading-screen";
import { TableSkeleton, PageSkeleton } from "@/components/ui/skeleton-loaders";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { MoreHorizontal, Edit, Trash2, UserPlus, Search, Filter, Loader2, ArrowUpDown, SortAsc, SortDesc, Eye, Users, AlertCircle, Shield } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogTrigger } from "@/components/ui/dialog";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { EmployeeForm } from "./EmployeeForm";
import { useToast } from "@/hooks/use-toast";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { cn } from "@/lib/utils";

export function EmployeeTable() {
  const [searchTerm, setSearchTerm] = React.useState("");
  const [isAddModalOpen, setIsAddModalOpen] = React.useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = React.useState(false);
  const [isFiltersOpen, setIsFiltersOpen] = React.useState(false);
  const [selectedEmployee, setSelectedEmployee] = React.useState<EmployeeDocument | null>(null);
  const [filters, setFilters] = React.useState<EmployeeFilterState>({
    search: '',
    service: '__all_services__',
    position: ALL_POSITIONS,
    status: 'all',
    startDateFrom: '',
    startDateTo: '',
  });
  const [sortConfig, setSortConfig] = React.useState<{
    key: keyof EmployeeDocument;
    direction: 'asc' | 'desc';
  } | null>(null);

  const { toast } = useToast();
  const { t } = useTranslation();
  const companyId = useCompanyId(); // Legacy compatibility
  const userId = useUserId();
  const { isAdmin } = usePermissions();
  const { isLoading: contextLoading, isInitialized } = useFirestore();
  const { isSuperAdmin, isAdmin: rbacIsAdmin, user: rbacUser } = useRBAC();
  const { selectedCompany, requiresCompanySelection } = useCompany();

  // Use company context for data integrity
  const effectiveCompanyId = selectedCompany?.id || companyId;
  const canManageEmployees = isSuperAdmin() || rbacIsAdmin() || isAdmin();

  // Validate company context for data operations
  const validateCompanyContext = () => {
    if (!effectiveCompanyId) {
      toast({
        title: "Erreur de contexte",
        description: "Aucune entreprise sélectionnée pour cette opération.",
        variant: "destructive"
      });
      return false;
    }
    return true;
  };

  // Memoize the orderBy object to prevent unnecessary re-renders
  const orderBy = React.useMemo(() => {
    return sortConfig ? { field: sortConfig.key, direction: sortConfig.direction } : { field: 'updatedAt', direction: 'desc' };
  }, [sortConfig?.key, sortConfig?.direction]);

  // Fetch employees using our new hook
  const { employees: employeeDocuments, isLoading, error, refetch } = useEmployees({
    enabled: !!effectiveCompanyId,
    orderBy
  });

  // Delete employee hook
  const { deleteEmployee, isLoading: isDeleting } = useDeleteEmployee();

  // Filter employees based on all filters
  const filteredEmployees = React.useMemo(() => {
    // Handle empty dataset gracefully
    if (!employeeDocuments || employeeDocuments.length === 0) {
      return [];
    }

    let filtered = employeeDocuments;

    // Apply search filter
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      filtered = filtered.filter(employee =>
        `${employee.firstName} ${employee.lastName}`.toLowerCase().includes(searchLower) ||
        (employee.email && employee.email.toLowerCase().includes(searchLower)) ||
        (employee.service && employee.service.toLowerCase().includes(searchLower)) ||
        (employee.position && employee.position.toLowerCase().includes(searchLower))
      );
    }

    // Apply service filter
    if (filters.service && filters.service !== '__all_services__') {
      filtered = filtered.filter(employee => employee.service === filters.service);
    }

    // Apply position filter
    if (filters.position && filters.position !== ALL_POSITIONS) {
      filtered = filtered.filter(employee => employee.position === filters.position);
    }

    // Apply status filter
    if (filters.status && filters.status !== 'all') {
      if (filters.status === 'active') {
        filtered = filtered.filter(employee => employee.status === 'Active');
      } else if (filters.status === 'inactive') {
        filtered = filtered.filter(employee => employee.status === 'Inactive');
      }
    }

    // Apply date range filter
    if (filters.startDateFrom) {
      filtered = filtered.filter(employee => employee.hireDate >= filters.startDateFrom);
    }
    if (filters.startDateTo) {
      filtered = filtered.filter(employee => employee.hireDate <= filters.startDateTo);
    }

    return filtered;
  }, [employeeDocuments, filters]);

  // Handle sorting
  const handleSort = (key: keyof EmployeeDocument) => {
    let direction: 'asc' | 'desc' = 'asc';
    if (sortConfig && sortConfig.key === key && sortConfig.direction === 'asc') {
      direction = 'desc';
    }
    setSortConfig({ key, direction });
  };

  // Handle filter changes - memoized to prevent unnecessary re-renders
  const handleFiltersChange = React.useCallback((newFilters: EmployeeFilterState) => {
    setFilters(newFilters);
  }, []);

  // Store reference to EmployeeFilters clear function - memoized callback
  const [employeeFiltersClearFunction, setEmployeeFiltersClearFunction] = React.useState<((key: keyof EmployeeFilterState) => void) | null>(null);

  // Memoized callback for setting clear function reference
  const handleClearFilterRef = React.useCallback((clearFunction: (key: keyof EmployeeFilterState) => void) => {
    setEmployeeFiltersClearFunction(() => clearFunction);
  }, []);

  // Handle clearing individual filters - memoized to prevent re-renders
  const handleClearFilter = React.useCallback((key: keyof EmployeeFilterState) => {
    if (employeeFiltersClearFunction) {
      // Use the EmployeeFilters component's clear function to ensure proper URL sync
      employeeFiltersClearFunction(key);
    } else {
      // Fallback to manual clearing if the function is not available
      let clearValue: any = '';
      if (key === 'service') clearValue = '__all_services__';
      else if (key === 'position') clearValue = ALL_POSITIONS;
      else if (key === 'status') clearValue = 'all';

      setFilters(prevFilters => ({ ...prevFilters, [key]: clearValue }));
    }
  }, [employeeFiltersClearFunction]);

  const handleAddSuccess = (employee: EmployeeDocument) => {
    setIsAddModalOpen(false);
    refetch(); // Refetch data from Firestore
  };

  const handleEditSuccess = (employee: EmployeeDocument) => {
    setIsEditModalOpen(false);
    setSelectedEmployee(null);
    refetch(); // Refetch data from Firestore
  };

  const openEditModal = (employee: EmployeeDocument) => {
    setSelectedEmployee(employee);
    setIsEditModalOpen(true);
  };

  const handleDeleteEmployee = async (employeeId: string) => {
    const employeeToDelete = filteredEmployees.find(emp => emp.id === employeeId);

    try {
      await deleteEmployee(employeeId);
      refetch(); // Refetch data to update the list
    } catch (error: any) {
      console.error('Error deleting employee:', error);
      toast({
        title: "Échec de la suppression",
        description: error.message || "Impossible de supprimer l'employé. Veuillez réessayer.",
        variant: "destructive"
      });
    }
  };

  // Show loading state for context initialization
  if (contextLoading || !isInitialized) {
    return (
      <LoadingScreen
        message="Setting up your workspace..."
        submessage="Initializing company data and user context"
      />
    );
  }

  // Show loading state for employee data
  if (isLoading) {
    return (
      <div className="space-y-6">
        {/* Header skeleton */}
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <div className="h-8 w-48 bg-muted animate-pulse rounded" />
            <div className="h-4 w-64 bg-muted animate-pulse rounded" />
          </div>
          <div className="h-10 w-32 bg-muted animate-pulse rounded" />
        </div>

        {/* Filters skeleton */}
        <div className="h-20 w-full bg-muted animate-pulse rounded" />

        {/* Table skeleton */}
        <TableSkeleton rows={8} columns={6} />
      </div>
    );
  }

  // Show error state (but not for empty datasets)
  if (error && !error.message.includes('No employees found')) {
    return (
      <div className="flex flex-col items-center justify-center p-8 text-center">
        <p className="text-destructive mb-4">Erreur lors du chargement des employés: {error.message}</p>
        <Button onClick={refetch} variant="outline">
          Réessayer
        </Button>
      </div>
    );
  }

  // Wrap in company selection guard for data integrity
  return (
    <CompanySelectionGuard
      requireCompany={true}
      fallbackMessage="Sélectionnez une entreprise pour gérer les employés."
    >
    <>
      {/* Top Filters */}
      <EmployeeFilters
        onFiltersChange={handleFiltersChange}
        onClearFilterRef={handleClearFilterRef}
      />

      {/* Header with Active Filters and Add Button */}
      <div className="flex flex-col gap-3 sm:flex-row sm:items-center sm:gap-4">
          {/* Active Filters Display - Takes available space */}
          <div className="flex-1 min-w-0 order-2 sm:order-1">
            <ActiveFiltersDisplay
              filters={filters}
              onClearFilter={handleClearFilter}
            />
          </div>

          {/* Add Employee Button - Fixed position */}
          <div className="flex justify-end sm:justify-start flex-shrink-0 order-1 sm:order-2">
            {canManageEmployees && (
              <Dialog open={isAddModalOpen} onOpenChange={setIsAddModalOpen}>
                <DialogTrigger asChild>
                  <Button>
                    <UserPlus className="mr-2 h-4 w-4" />
                    Ajouter un employé
                  </Button>
                </DialogTrigger>
                <DialogContent className="sm:max-w-4xl max-h-[90vh] overflow-y-auto">
                  <DialogHeader>
                    <DialogTitle>Ajouter un nouvel employé</DialogTitle>
                  </DialogHeader>
                  <EmployeeForm
                    mode="create"
                    onSuccess={handleAddSuccess}
                    onCancel={() => setIsAddModalOpen(false)}
                  />
                </DialogContent>
                </Dialog>
            )}
          </div>
      </div>

      {/* Employee Table */}
      <Card>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>
                    <Button
                      variant="ghost"
                      onClick={() => handleSort('firstName')}
                      className="h-auto p-0 font-semibold"
                    >
                      Nom
                      {sortConfig?.key === 'firstName' ? (
                        sortConfig.direction === 'asc' ? <SortAsc className="ml-2 h-4 w-4" /> : <SortDesc className="ml-2 h-4 w-4" />
                      ) : (
                        <ArrowUpDown className="ml-2 h-4 w-4" />
                      )}
                    </Button>
                  </TableHead>
                  <TableHead className="hidden lg:table-cell">Téléphone</TableHead>
                  <TableHead>
                    <Button
                      variant="ghost"
                      onClick={() => handleSort('department')}
                      className="h-auto p-0 font-semibold"
                    >
                      Département
                      {sortConfig?.key === 'department' ? (
                        sortConfig.direction === 'asc' ? <SortAsc className="ml-2 h-4 w-4" /> : <SortDesc className="ml-2 h-4 w-4" />
                      ) : (
                        <ArrowUpDown className="ml-2 h-4 w-4" />
                      )}
                    </Button>
                  </TableHead>
                  <TableHead className="hidden md:table-cell">
                    <Button
                      variant="ghost"
                      onClick={() => handleSort('position')}
                      className="h-auto p-0 font-semibold"
                    >
                      Fonction
                      {sortConfig?.key === 'position' ? (
                        sortConfig.direction === 'asc' ? <SortAsc className="ml-2 h-4 w-4" /> : <SortDesc className="ml-2 h-4 w-4" />
                      ) : (
                        <ArrowUpDown className="ml-2 h-4 w-4" />
                      )}
                    </Button>
                  </TableHead>
                  <TableHead>
                    <Button
                      variant="ghost"
                      onClick={() => handleSort('hireDate')}
                      className="h-auto p-0 font-semibold"
                    >
                      Date d'embauche
                      {sortConfig?.key === 'hireDate' ? (
                        sortConfig.direction === 'asc' ? <SortAsc className="ml-2 h-4 w-4" /> : <SortDesc className="ml-2 h-4 w-4" />
                      ) : (
                        <ArrowUpDown className="ml-2 h-4 w-4" />
                      )}
                    </Button>
                  </TableHead>
                  <TableHead>Statut</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredEmployees.length > 0 ? (
                  filteredEmployees.map((employee) => (
                    <TableRow key={employee.id}>
                      <TableCell>
                        <div className="font-medium">{employee.firstName} {employee.lastName}</div>
                        <div className="text-sm text-muted-foreground lg:hidden">{employee.phoneNumber || '-'}</div>
                      </TableCell>
                      <TableCell className="hidden lg:table-cell text-muted-foreground">{employee.phoneNumber || '-'}</TableCell>
                      <TableCell>
                        <div className="font-medium">{employee.department}</div>
                        <div className="text-sm text-muted-foreground md:hidden">{employee.position}</div>
                      </TableCell>
                      <TableCell className="hidden md:table-cell">{employee.position}</TableCell>
                      <TableCell className="text-sm">
                        {new Date(employee.hireDate).toLocaleDateString()}
                      </TableCell>
                      <TableCell>
                        <Badge variant={employee.status === "Active" ? "default" : "secondary"}
                               className={cn(
                                "text-xs",
                                employee.status === "Active"
                                   ? "bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-300 border-green-300/50"
                                   : "bg-red-100 text-red-800 dark:bg-red-900/50 dark:text-red-300 border-red-300/50"
                               )}>
                          {employee.status === "Active" ? "Actif" : "Inactif"}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        {canManageEmployees ? (
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="icon" className="h-8 w-8">
                                <span className="sr-only">Open menu</span>
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Actions</DropdownMenuLabel>
                              <DropdownMenuItem onClick={() => openEditModal(employee)}>
                                <Edit className="mr-2 h-4 w-4" />
                                Modifier l'employé
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <AlertDialog>
                                <AlertDialogTrigger asChild>
                                  <DropdownMenuItem
                                    onSelect={(e) => e.preventDefault()}
                                    className="text-red-600 hover:!text-red-600 focus:!text-red-600"
                                  >
                                    <Trash2 className="mr-2 h-4 w-4" />
                                    Supprimer l'employé
                                  </DropdownMenuItem>
                                </AlertDialogTrigger>
                                <AlertDialogContent>
                                  <AlertDialogHeader>
                                    <AlertDialogTitle>Êtes-vous sûr ?</AlertDialogTitle>
                                    <AlertDialogDescription>
                                      Cela supprimera définitivement {employee.firstName} {employee.lastName} de votre équipe.
                                      Cette action ne peut pas être annulée.
                                    </AlertDialogDescription>
                                  </AlertDialogHeader>
                                  <AlertDialogFooter>
                                    <AlertDialogCancel>Annuler</AlertDialogCancel>
                                    <AlertDialogAction
                                      onClick={() => handleDeleteEmployee(employee.id)}
                                      className="bg-destructive hover:bg-destructive/90"
                                      disabled={isDeleting}
                                    >
                                      {isDeleting ? (
                                        <>
                                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                          Suppression...
                                        </>
                                      ) : (
                                        'Supprimer l\'employé'
                                      )}
                                    </AlertDialogAction>
                                  </AlertDialogFooter>
                                </AlertDialogContent>
                              </AlertDialog>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        ) : (
                          <Button variant="ghost" size="sm" disabled>
                            <Eye className="h-4 w-4 mr-2" />
                            Lecture seule
                          </Button>
                        )}
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={7} className="h-24 text-center">
                      <div className="flex flex-col items-center gap-2">
                        <Users className="h-8 w-8 text-muted-foreground" />
                        <p className="text-muted-foreground">
                          {employeeDocuments.length === 0
                            ? "Aucun employé trouvé. Utilisez le bouton 'Ajouter un employé' pour commencer."
                            : "Aucun employé ne correspond à vos filtres actuels."
                          }
                        </p>
                      </div>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </Card>

        {/* Edit Employee Modal */}
        {selectedEmployee && (
          <Dialog open={isEditModalOpen} onOpenChange={(isOpen) => {
            setIsEditModalOpen(isOpen);
            if (!isOpen) setSelectedEmployee(null);
          }}>
            <DialogContent className="sm:max-w-4xl max-h-[90vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>Modifier l'employé</DialogTitle>
              </DialogHeader>
              <EmployeeForm
                mode="edit"
                employee={selectedEmployee}
                onSuccess={handleEditSuccess}
                onCancel={() => setIsEditModalOpen(false)}
              />
            </DialogContent>
          </Dialog>
        )}
    </>
    </CompanySelectionGuard>
  );
}
