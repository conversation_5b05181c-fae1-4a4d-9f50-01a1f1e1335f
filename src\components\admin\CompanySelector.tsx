"use client";

import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { useCompany } from '@/context/CompanyContext';
import { useRBAC } from '@/context/RBACContext';
import { 
  Building2, 
  Shield, 
  CheckCircle,
  AlertTriangle,
  Users
} from 'lucide-react';

interface CompanySelectorProps {
  onCompanyChange?: (companyId: string | null) => void;
  placeholder?: string;
  showLabel?: boolean;
}

/**
 * Company Selector Component for Admin Interface
 * 
 * Provides company selection for superuser operations in admin tabs.
 * Only visible to superuser accounts. Company admins are automatically
 * scoped to their assigned company.
 */
export function CompanySelector({ 
  onCompanyChange, 
  placeholder = "Sélectionner une entreprise",
  showLabel = true 
}: CompanySelectorProps) {
  const { 
    selectedCompany, 
    availableCompanies, 
    selectCompany, 
    clearCompany,
    isLoading 
  } = useCompany();
  
  const { isSuperAdmin, user: rbacUser } = useRBAC();

  // Only show for superuser
  if (!isSuperAdmin()) {
    return null;
  }

  const handleCompanyChange = (value: string) => {
    if (value === "none") {
      clearCompany();
      onCompanyChange?.(null);
    } else {
      const company = availableCompanies.find(c => c.id === value);
      if (company) {
        selectCompany(company);
        onCompanyChange?.(company.id);
      }
    }
  };

  if (isLoading) {
    return (
      <Card className="border-blue-200 bg-blue-50">
        <CardContent className="p-3">
          <div className="flex items-center gap-2 text-sm text-blue-700">
            <Shield className="h-4 w-4 animate-pulse" />
            <span>Chargement des entreprises...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="border-blue-200 bg-blue-50">
      <CardContent className="p-3">
        <div className="flex items-center justify-between gap-3">
          <div className="flex items-center gap-2">
            <Shield className="h-4 w-4 text-blue-600" />
            {showLabel && (
              <span className="text-sm font-medium text-blue-800">
                Contexte d'Entreprise (Super Admin)
              </span>
            )}
          </div>
          
          <div className="flex items-center gap-2">
            {availableCompanies.length > 0 ? (
              <Select
                value={selectedCompany?.id || "none"}
                onValueChange={handleCompanyChange}
              >
                <SelectTrigger className="w-64 h-8 text-xs bg-white border-blue-300">
                  <SelectValue placeholder={placeholder} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">
                    <div className="flex items-center gap-2">
                      <AlertTriangle className="h-3 w-3 text-amber-600" />
                      <span className="text-muted-foreground">Aucune sélection</span>
                    </div>
                  </SelectItem>
                  {availableCompanies.map((company) => (
                    <SelectItem key={company.id} value={company.id}>
                      <div className="flex items-center gap-2">
                        <Building2 className="h-3 w-3 text-blue-600" />
                        <span>{company.name}</span>
                        {company.isActive ? (
                          <CheckCircle className="h-3 w-3 text-green-600" />
                        ) : (
                          <AlertTriangle className="h-3 w-3 text-amber-600" />
                        )}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            ) : (
              <span className="text-xs text-blue-700">Aucune entreprise disponible</span>
            )}
          </div>
        </div>
        
        {selectedCompany && (
          <div className="mt-2 pt-2 border-t border-blue-200">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2 text-xs text-blue-700">
                <Building2 className="h-3 w-3" />
                <span>Entreprise sélectionnée: {selectedCompany.name}</span>
              </div>
              <Badge 
                variant={selectedCompany.isActive ? "default" : "secondary"}
                className={`text-xs ${selectedCompany.isActive ? "bg-green-100 text-green-800" : "bg-amber-100 text-amber-800"}`}
              >
                {selectedCompany.isActive ? "Active" : "Inactive"}
              </Badge>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

/**
 * Compact Company Selector for Tab Headers
 */
export function CompactCompanySelector({ 
  onCompanyChange,
  placeholder = "Choisir une entreprise"
}: CompanySelectorProps) {
  const { 
    selectedCompany, 
    availableCompanies, 
    selectCompany, 
    clearCompany,
    isLoading 
  } = useCompany();
  
  const { isSuperAdmin } = useRBAC();

  // Only show for superuser
  if (!isSuperAdmin()) {
    return null;
  }

  const handleCompanyChange = (value: string) => {
    if (value === "none") {
      clearCompany();
      onCompanyChange?.(null);
    } else {
      const company = availableCompanies.find(c => c.id === value);
      if (company) {
        selectCompany(company);
        onCompanyChange?.(company.id);
      }
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center gap-2 text-sm text-muted-foreground">
        <Shield className="h-4 w-4 animate-pulse" />
        <span>Chargement...</span>
      </div>
    );
  }

  return (
    <div className="flex items-center gap-2">
      <Shield className="h-4 w-4 text-blue-600" />
      <span className="text-sm text-blue-800">Entreprise:</span>
      <Select
        value={selectedCompany?.id || "none"}
        onValueChange={handleCompanyChange}
      >
        <SelectTrigger className="w-48 h-8 text-xs">
          <SelectValue placeholder={placeholder} />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="none">
            <span className="text-muted-foreground">Aucune sélection</span>
          </SelectItem>
          {availableCompanies.map((company) => (
            <SelectItem key={company.id} value={company.id}>
              <div className="flex items-center gap-2">
                <span>{company.name}</span>
                {company.isActive ? (
                  <CheckCircle className="h-3 w-3 text-green-600" />
                ) : (
                  <AlertTriangle className="h-3 w-3 text-amber-600" />
                )}
              </div>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
}

/**
 * Company Selection Requirement Notice
 */
export function CompanySelectionNotice({ 
  message = "Veuillez sélectionner une entreprise pour continuer.",
  showCreateButton = false 
}: {
  message?: string;
  showCreateButton?: boolean;
}) {
  const { isSuperAdmin } = useRBAC();

  if (!isSuperAdmin()) {
    return null;
  }

  return (
    <Card className="border-amber-200 bg-amber-50">
      <CardContent className="p-6">
        <div className="text-center space-y-4">
          <div className="flex items-center justify-center gap-2 text-amber-800">
            <AlertTriangle className="h-6 w-6" />
            <span className="font-medium">Sélection d'Entreprise Requise</span>
          </div>
          
          <p className="text-sm text-amber-700">
            {message}
          </p>
          
          {showCreateButton && (
            <div className="pt-2">
              <button
                onClick={() => window.location.href = '/admin'}
                className="text-sm text-amber-800 underline hover:text-amber-900"
              >
                Créer une nouvelle entreprise
              </button>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
