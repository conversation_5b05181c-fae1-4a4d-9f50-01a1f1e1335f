"use client";

import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useTranslation } from '@/context/i18nContext';
import { useFirestore, useCompanyId, useUserId, usePermissions } from '@/context/FirestoreContext';
import { useFirestoreDocument, useFirestoreMutation } from '@/hooks/useFirestoreData';
import { companyService } from '@/lib/firestore';
import { useToast } from '@/hooks/use-toast';
import type { Company } from '@/lib/firestore/types';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { 
  Form, 
  FormControl, 
  FormDescription, 
  FormField, 
  FormItem, 
  FormLabel, 
  FormMessage 
} from '@/components/ui/form';
import { 
  Loader2, 
  Save, 
  RotateCcw, 
  Building2, 
  Clock, 
  Globe, 
  CreditCard,
  AlertCircle,
  Shield
} from 'lucide-react';

/**
 * Company settings form validation schema
 * Validates all editable company settings fields
 */
const companySettingsSchema = z.object({
  // Basic company information
  name: z.string().min(1, 'Company name is required').max(100, 'Company name too long'),
  email: z.string().email('Invalid email address'),
  phone: z.string().optional(),
  address: z.string().optional(),
  
  // Work schedule settings
  workingDays: z.array(z.number().min(0).max(6)).min(1, 'At least one working day required'),
  workingHours: z.object({
    start: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Invalid time format (HH:MM)'),
    end: z.string().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Invalid time format (HH:MM)'),
  }),
  
  // Localization settings
  timezone: z.string().min(1, 'Timezone is required'),
  currency: z.string().min(1, 'Currency is required'),
  dateFormat: z.string().min(1, 'Date format is required'),
});

type CompanySettingsFormData = z.infer<typeof companySettingsSchema>;

/**
 * Company Settings Management Component
 * 
 * Provides a comprehensive interface for managing company settings including:
 * - Basic company information (name, email, phone, address)
 * - Work schedule configuration (working days and hours)
 * - Localization settings (timezone, currency, date format)
 * - Read-only system information (subscription, creation date)
 * 
 * Features:
 * - Permission-based access control (admin only)
 * - Real-time form validation with Zod
 * - Optimistic updates with error handling
 * - Multi-tenant security with company ID isolation
 * - Responsive design with loading states
 */
export function CompanySettings() {
  const { t } = useTranslation();
  const { toast } = useToast();
  const companyId = useCompanyId();
  const userId = useUserId();
  const { isAdmin } = usePermissions();
  const { company, refreshCompany } = useFirestore();
  
  const [isEditing, setIsEditing] = useState(false);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  // Fetch company data using our established hook pattern
  const {
    data: companyData,
    isLoading: isLoadingCompany,
    error: companyError,
    refetch: refetchCompany
  } = useFirestoreDocument(
    {
      getById: companyService.getById.bind(companyService)
    },
    companyId,
    !!companyId && isAdmin()
  );

  // Company update mutation using our established pattern
  const {
    update: updateCompany,
    isLoading: isUpdating,
    error: updateError
  } = useFirestoreMutation({
    update: companyService.update.bind(companyService)
  });

  // Initialize form with react-hook-form and Zod validation
  const form = useForm<CompanySettingsFormData>({
    resolver: zodResolver(companySettingsSchema),
    defaultValues: {
      name: '',
      email: '',
      phone: '',
      address: '',
      workingDays: [1, 2, 3, 4, 5], // Monday to Friday
      workingHours: {
        start: '09:00',
        end: '17:00',
      },
      timezone: 'UTC',
      currency: 'USD',
      dateFormat: 'MM/DD/YYYY',
    },
  });

  // Watch form changes to detect unsaved changes
  const watchedValues = form.watch();
  
  useEffect(() => {
    if (isEditing) {
      setHasUnsavedChanges(form.formState.isDirty);
    }
  }, [watchedValues, isEditing, form.formState.isDirty]);

  // Populate form when company data is loaded
  useEffect(() => {
    if (companyData) {
      form.reset({
        name: companyData.name,
        email: companyData.email,
        phone: companyData.phone || '',
        address: companyData.address || '',
        workingDays: companyData.settings.workingDays,
        workingHours: companyData.settings.workingHours,
        timezone: companyData.settings.timezone,
        currency: companyData.settings.currency,
        dateFormat: companyData.settings.dateFormat,
      });
      setHasUnsavedChanges(false);
    }
  }, [companyData, form]);

  // Permission check - only admins can access settings
  if (!isAdmin()) {
    return (
      <Card className="w-full max-w-4xl mx-auto">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-destructive">
            <Shield className="h-5 w-5" />
            Access Denied
          </CardTitle>
          <CardDescription>
            You don't have permission to access company settings. Only administrators can modify company settings.
          </CardDescription>
        </CardHeader>
      </Card>
    );
  }

  // Loading state
  if (isLoadingCompany) {
    return (
      <Card className="w-full max-w-4xl mx-auto">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building2 className="h-5 w-5" />
            Company Settings
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center p-8">
            <Loader2 className="h-8 w-8 animate-spin" />
            <span className="ml-2">Loading company settings...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Error state
  if (companyError || !companyData) {
    return (
      <Card className="w-full max-w-4xl mx-auto">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-destructive">
            <AlertCircle className="h-5 w-5" />
            Error Loading Settings
          </CardTitle>
          <CardDescription>
            {companyError?.message || 'Failed to load company settings. Please try again.'}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Button onClick={refetchCompany} variant="outline">
            <RotateCcw className="mr-2 h-4 w-4" />
            Retry
          </Button>
        </CardContent>
      </Card>
    );
  }

  /**
   * Handle form submission
   * Updates company settings in Firestore and provides user feedback
   */
  const onSubmit = async (data: CompanySettingsFormData) => {
    if (!companyId || !userId) {
      toast({
        title: "Error",
        description: "Missing company or user information",
        variant: "destructive"
      });
      return;
    }

    try {
      const updateData = {
        name: data.name,
        email: data.email,
        phone: data.phone,
        address: data.address,
        settings: {
          ...companyData.settings,
          workingDays: data.workingDays,
          workingHours: data.workingHours,
          timezone: data.timezone,
          currency: data.currency,
          dateFormat: data.dateFormat,
        },
      };

      const updatedCompany = await updateCompany(companyId, updateData);
      
      if (updatedCompany) {
        toast({
          title: "Settings Updated",
          description: "Company settings have been successfully updated.",
        });
        
        // Refresh company context
        await refreshCompany();
        
        // Reset form state
        setIsEditing(false);
        setHasUnsavedChanges(false);
        form.reset(data);
      }
    } catch (error: any) {
      console.error('Error updating company settings:', error);
      toast({
        title: "Update Failed",
        description: error.message || "Failed to update company settings. Please try again.",
        variant: "destructive"
      });
    }
  };

  /**
   * Handle cancel action
   * Resets form to original values and exits edit mode
   */
  const handleCancel = () => {
    if (companyData) {
      form.reset({
        name: companyData.name,
        email: companyData.email,
        phone: companyData.phone || '',
        address: companyData.address || '',
        workingDays: companyData.settings.workingDays,
        workingHours: companyData.settings.workingHours,
        timezone: companyData.settings.timezone,
        currency: companyData.settings.currency,
        dateFormat: companyData.settings.dateFormat,
      });
    }
    setIsEditing(false);
    setHasUnsavedChanges(false);
  };

  // Day names for working days selection
  const dayNames = [
    'Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'
  ];

  // Common timezone options
  const timezoneOptions = [
    'UTC',
    'America/New_York',
    'America/Chicago',
    'America/Denver',
    'America/Los_Angeles',
    'Europe/London',
    'Europe/Paris',
    'Europe/Berlin',
    'Asia/Tokyo',
    'Asia/Shanghai',
    'Australia/Sydney',
  ];

  // Common currency options
  const currencyOptions = [
    { value: 'USD', label: 'USD - US Dollar' },
    { value: 'EUR', label: 'EUR - Euro' },
    { value: 'GBP', label: 'GBP - British Pound' },
    { value: 'CAD', label: 'CAD - Canadian Dollar' },
    { value: 'AUD', label: 'AUD - Australian Dollar' },
    { value: 'JPY', label: 'JPY - Japanese Yen' },
    { value: 'CNY', label: 'CNY - Chinese Yuan' },
  ];

  // Date format options
  const dateFormatOptions = [
    { value: 'MM/DD/YYYY', label: 'MM/DD/YYYY (US)' },
    { value: 'DD/MM/YYYY', label: 'DD/MM/YYYY (EU)' },
    { value: 'YYYY-MM-DD', label: 'YYYY-MM-DD (ISO)' },
    { value: 'DD-MM-YYYY', label: 'DD-MM-YYYY' },
  ];

  return (
    <div className="w-full max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Company Settings</h1>
          <p className="text-muted-foreground">
            Manage your company information, work schedule, and system preferences.
          </p>
        </div>
        
        {!isEditing ? (
          <Button onClick={() => setIsEditing(true)}>
            Edit Settings
          </Button>
        ) : (
          <div className="flex gap-2">
            <Button 
              variant="outline" 
              onClick={handleCancel}
              disabled={isUpdating}
            >
              <RotateCcw className="mr-2 h-4 w-4" />
              Cancel
            </Button>
            <Button 
              onClick={form.handleSubmit(onSubmit)}
              disabled={isUpdating || !hasUnsavedChanges}
            >
              {isUpdating ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Saving...
                </>
              ) : (
                <>
                  <Save className="mr-2 h-4 w-4" />
                  Save Changes
                </>
              )}
            </Button>
          </div>
        )}
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* Basic Company Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Building2 className="h-5 w-5" />
                Basic Information
              </CardTitle>
              <CardDescription>
                Essential company details and contact information.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Company Name *</FormLabel>
                      <FormControl>
                        <Input 
                          {...field} 
                          disabled={!isEditing}
                          placeholder="Enter company name"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email Address *</FormLabel>
                      <FormControl>
                        <Input 
                          {...field} 
                          type="email"
                          disabled={!isEditing}
                          placeholder="<EMAIL>"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="phone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Phone Number</FormLabel>
                      <FormControl>
                        <Input 
                          {...field} 
                          disabled={!isEditing}
                          placeholder="+****************"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="address"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Address</FormLabel>
                      <FormControl>
                        <Input 
                          {...field} 
                          disabled={!isEditing}
                          placeholder="123 Business St, City, State"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </CardContent>
          </Card>

          {/* Work Schedule Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="h-5 w-5" />
                Work Schedule
              </CardTitle>
              <CardDescription>
                Configure working days and hours for your organization.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Working Days */}
              <FormField
                control={form.control}
                name="workingDays"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Working Days</FormLabel>
                    <FormDescription>
                      Select the days when your company operates.
                    </FormDescription>
                    <FormControl>
                      <div className="grid grid-cols-7 gap-2">
                        {dayNames.map((day, index) => (
                          <div key={index} className="flex items-center space-x-2">
                            <Checkbox
                              id={`day-${index}`}
                              checked={field.value.includes(index)}
                              disabled={!isEditing}
                              onCheckedChange={(checked) => {
                                if (checked) {
                                  field.onChange([...field.value, index].sort());
                                } else {
                                  field.onChange(field.value.filter(d => d !== index));
                                }
                              }}
                            />
                            <Label 
                              htmlFor={`day-${index}`} 
                              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                            >
                              {day.slice(0, 3)}
                            </Label>
                          </div>
                        ))}
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Working Hours */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="workingHours.start"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Start Time</FormLabel>
                      <FormControl>
                        <Input 
                          {...field} 
                          type="time"
                          disabled={!isEditing}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="workingHours.end"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>End Time</FormLabel>
                      <FormControl>
                        <Input 
                          {...field} 
                          type="time"
                          disabled={!isEditing}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </CardContent>
          </Card>

          {/* Localization Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Globe className="h-5 w-5" />
                Localization
              </CardTitle>
              <CardDescription>
                Configure timezone, currency, and date format preferences.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <FormField
                  control={form.control}
                  name="timezone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Timezone</FormLabel>
                      <Select 
                        onValueChange={field.onChange} 
                        defaultValue={field.value}
                        disabled={!isEditing}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select timezone" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {timezoneOptions.map((tz) => (
                            <SelectItem key={tz} value={tz}>
                              {tz}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="currency"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Currency</FormLabel>
                      <Select 
                        onValueChange={field.onChange} 
                        defaultValue={field.value}
                        disabled={!isEditing}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select currency" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {currencyOptions.map((currency) => (
                            <SelectItem key={currency.value} value={currency.value}>
                              {currency.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="dateFormat"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Date Format</FormLabel>
                      <Select 
                        onValueChange={field.onChange} 
                        defaultValue={field.value}
                        disabled={!isEditing}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select date format" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {dateFormatOptions.map((format) => (
                            <SelectItem key={format.value} value={format.value}>
                              {format.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </CardContent>
          </Card>

          {/* System Information (Read-only) */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CreditCard className="h-5 w-5" />
                System Information
              </CardTitle>
              <CardDescription>
                Read-only system and subscription information.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium">Subscription Plan</Label>
                  <div className="mt-1">
                    <Badge variant={companyData.subscription.status === 'active' ? 'default' : 'secondary'}>
                      {companyData.subscription.plan.toUpperCase()} - {companyData.subscription.status}
                    </Badge>
                  </div>
                </div>

                <div>
                  <Label className="text-sm font-medium">Company ID</Label>
                  <div className="mt-1">
                    <code className="text-sm bg-muted px-2 py-1 rounded">
                      {companyData.id}
                    </code>
                  </div>
                </div>

                <div>
                  <Label className="text-sm font-medium">Created</Label>
                  <div className="mt-1 text-sm text-muted-foreground">
                    {companyData.createdAt.toLocaleDateString()}
                  </div>
                </div>

                <div>
                  <Label className="text-sm font-medium">Last Updated</Label>
                  <div className="mt-1 text-sm text-muted-foreground">
                    {companyData.updatedAt.toLocaleDateString()}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </form>
      </Form>

      {/* Unsaved Changes Warning */}
      {hasUnsavedChanges && (
        <Card className="border-yellow-200 bg-yellow-50">
          <CardContent className="pt-6">
            <div className="flex items-center gap-2 text-yellow-800">
              <AlertCircle className="h-4 w-4" />
              <span className="text-sm font-medium">
                You have unsaved changes. Don't forget to save your settings.
              </span>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
