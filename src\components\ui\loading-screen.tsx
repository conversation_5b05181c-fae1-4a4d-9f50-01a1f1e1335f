"use client";

import React from 'react';
import { Building2, Loader2 } from 'lucide-react';
import { Skeleton } from './skeleton';

interface LoadingScreenProps {
  message?: string;
  submessage?: string;
  showProgress?: boolean;
}

export function LoadingScreen({
  message = "Chargement de WePaie...",
  submessage = "Veuillez patienter pendant que nous configurons votre espace de travail",
  showProgress = true
}: LoadingScreenProps) {
  return (
    <div className="min-h-screen flex items-center justify-center bg-background p-4">
      <div className="text-center space-y-8 max-w-md mx-auto">
        {/* Logo/Icon with clean animation */}
        <div className="flex justify-center">
          <Building2 className="h-20 w-20 text-primary animate-pulse" />
        </div>

        {/* Main message with better typography */}
        <div className="space-y-3">
          <h1 className="text-2xl sm:text-3xl font-bold text-foreground tracking-tight">{message}</h1>
          <p className="text-muted-foreground text-sm sm:text-base leading-relaxed">{submessage}</p>
        </div>

        {/* Enhanced loading indicator */}
        {showProgress && (
          <div className="flex items-center justify-center space-x-3 py-2">
            <Loader2 className="h-5 w-5 animate-spin text-primary" />
            <span className="text-sm text-muted-foreground font-medium animate-pulse">Initialisation...</span>
          </div>
        )}

        {/* Optimized skeleton elements with staggered animation */}
        <div className="space-y-3 mt-8">
          <Skeleton className="h-2 w-3/4 mx-auto animate-pulse" style={{ animationDelay: '0ms' }} />
          <Skeleton className="h-2 w-1/2 mx-auto animate-pulse" style={{ animationDelay: '150ms' }} />
          <Skeleton className="h-2 w-2/3 mx-auto animate-pulse" style={{ animationDelay: '300ms' }} />
        </div>
      </div>
    </div>
  );
}
