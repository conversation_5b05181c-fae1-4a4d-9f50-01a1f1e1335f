"use client";

import React, { useState, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent } from '@/components/ui/card';
import { Upload, X, FileImage } from 'lucide-react';
import { useTranslation } from '@/context/i18nContext';
import { useToast } from '@/hooks/use-toast';

interface LogoUploadProps {
  currentLogo?: string;
  onLogoChange: (logo: string | null) => void;
  disabled?: boolean;
}

export function LogoUpload({ currentLogo, onLogoChange, disabled = false }: LogoUploadProps) {
  const { t } = useTranslation();
  const { toast } = useToast();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [previewLogo, setPreviewLogo] = useState<string | null>(currentLogo || null);

  const validateSVG = (content: string): boolean => {
    // Basic SVG validation
    const svgRegex = /^<svg[\s\S]*<\/svg>$/i;
    return svgRegex.test(content.trim());
  };

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (file.type !== 'image/svg+xml') {
      toast({
        title: t('logo.error.invalidType', 'Type de fichier invalide'),
        description: t('logo.error.svgOnly', 'Seuls les fichiers SVG sont acceptés.'),
        variant: 'destructive'
      });
      return;
    }

    // Validate file size (max 100KB)
    if (file.size > 100 * 1024) {
      toast({
        title: t('logo.error.tooLarge', 'Fichier trop volumineux'),
        description: t('logo.error.maxSize', 'La taille maximale est de 100KB.'),
        variant: 'destructive'
      });
      return;
    }

    try {
      const content = await file.text();
      
      if (!validateSVG(content)) {
        toast({
          title: t('logo.error.invalidSVG', 'SVG invalide'),
          description: t('logo.error.malformed', 'Le fichier SVG est malformé.'),
          variant: 'destructive'
        });
        return;
      }

      setPreviewLogo(content);
      onLogoChange(content);
      
      toast({
        title: t('logo.success.uploaded', 'Logo téléchargé'),
        description: t('logo.success.description', 'Le logo a été téléchargé avec succès.'),
      });
    } catch (error) {
      toast({
        title: t('logo.error.readFailed', 'Erreur de lecture'),
        description: t('logo.error.readDescription', 'Impossible de lire le fichier.'),
        variant: 'destructive'
      });
    }

    // Reset input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleRemoveLogo = () => {
    setPreviewLogo(null);
    onLogoChange(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const renderLogoPreview = (logoContent: string) => {
    return (
      <div 
        className="w-full h-16 flex items-center justify-center bg-background border rounded"
        dangerouslySetInnerHTML={{ __html: logoContent }}
      />
    );
  };

  return (
    <div className="space-y-3">
      <Label className="text-sm font-medium">
        {t('company.logo', 'Logo de l\'entreprise')}
      </Label>
      
      {previewLogo ? (
        <Card>
          <CardContent className="p-3">
            <div className="space-y-3">
              <div className="text-xs text-muted-foreground">
                {t('logo.preview', 'Aperçu du logo')}
              </div>
              {renderLogoPreview(previewLogo)}
              <div className="flex gap-2">
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => fileInputRef.current?.click()}
                  disabled={disabled}
                >
                  <Upload className="h-4 w-4 mr-2" />
                  {t('logo.change', 'Changer')}
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={handleRemoveLogo}
                  disabled={disabled}
                >
                  <X className="h-4 w-4 mr-2" />
                  {t('logo.remove', 'Supprimer')}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      ) : (
        <Card className="border-dashed">
          <CardContent className="p-6">
            <div className="text-center space-y-3">
              <FileImage className="h-12 w-12 mx-auto text-muted-foreground" />
              <div className="space-y-1">
                <div className="text-sm font-medium">
                  {t('logo.upload', 'Télécharger un logo')}
                </div>
                <div className="text-xs text-muted-foreground">
                  {t('logo.requirements', 'Fichier SVG uniquement, max 100KB')}
                </div>
              </div>
              <Button
                type="button"
                variant="outline"
                onClick={() => fileInputRef.current?.click()}
                disabled={disabled}
              >
                <Upload className="h-4 w-4 mr-2" />
                {t('logo.selectFile', 'Sélectionner un fichier')}
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      <Input
        ref={fileInputRef}
        type="file"
        accept=".svg,image/svg+xml"
        onChange={handleFileSelect}
        className="hidden"
        disabled={disabled}
      />
    </div>
  );
}
