"use client";

import AppShell from '@/components/layout/AppShell';
import { EmployeeForm } from '@/components/employees/EmployeeForm';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { UserPlus } from 'lucide-react';

/**
 * New Employee Page
 *
 * Dedicated page for creating new employees.
 * Provides a clean, focused interface for employee creation.
 */
export default function NewEmployeePage() {
  return (
    <ProtectedRoute requireAuth={true}>
      <AppShell pageTitle="Add New Employee">
        <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <UserPlus className="h-5 w-5" />
                Add New Employee
              </CardTitle>
              <CardDescription>
                Enter employee details to add them to your team.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <EmployeeForm
                mode="create"
                onSuccess={(employee) => {
                  // Redirect to employee list or detail page
                  window.location.href = '/employees';
                }}
                onCancel={() => {
                  // Redirect back to employee list
                  window.location.href = '/employees';
                }}
              />
            </CardContent>
          </Card>
      </AppShell>
    </ProtectedRoute>
  );
}
