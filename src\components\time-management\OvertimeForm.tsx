"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import type { OvertimeFormData } from "@/lib/schemas";
import { overtimeSchema } from "@/lib/schemas";
import type { Employee } from "@/types";
import { useTranslation } from "@/context/i18nContext";

import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { Textarea } from "@/components/ui/textarea";
import { CalendarIcon } from "lucide-react";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import { useToast } from "@/hooks/use-toast";

// Mock data for employees to pass to forms - updated with new Employee structure
const mockEmployeesData: Employee[] = [
  { id: '1', firstName: 'Alice', lastName: 'Wonderland', email: '<EMAIL>', department: 'Engineering', service: 'Frontend', position: 'Software Developer', hourlyRate: 50, hoursPerWeek: 40, overtimeRate: 75, weeklySalary: 2000, status: 'Active', hireDate: '2022-01-15', phoneNumber: '555-0101', address: '123 Rabbit Hole Lane' },
  { id: '2', firstName: 'Bob', lastName: 'The Builder', email: '<EMAIL>', department: 'Operations', service: 'Construction', position: 'Manager', hourlyRate: 60, hoursPerWeek: 40, overtimeRate: 90, weeklySalary: 2400, status: 'Active', hireDate: '2021-03-10', phoneNumber: '555-0102', address: '456 Fixit Ave' },
];

interface OvertimeFormProps {
  employees: Employee[];
  onSuccess?: () => void;
}

export function OvertimeForm({ employees = mockEmployeesData, onSuccess }: OvertimeFormProps) {
  const { toast } = useToast();
  const { t } = useTranslation();
  const form = useForm<OvertimeFormData>({
    resolver: zodResolver(overtimeSchema),
    defaultValues: {
      employeeIds: [],
      date: new Date(),
      hours: 0,
      category: "Regular Overtime",
      reason: "",
    },
  });

  const employeeOptions = employees.map(emp => ({
    value: emp.id,
    label: `${emp.firstName} ${emp.lastName}`
  }));

  async function onSubmit(data: OvertimeFormData) {
    console.log("Overtime data:", data);
    toast({ title: t('overtimeForm.toast.loggedTitle', "Overtime Logged"), description: t('overtimeForm.toast.loggedDescription', `Overtime for selected employee(s) has been logged. (Simulated)`) });
    form.reset();
    if (onSuccess) onSuccess();
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4"> {/* Reduced space-y-6 to space-y-4 */}
        <FormField
          control={form.control}
          name="employeeIds"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t('overtimeForm.label.employees', 'Employee(s)')}</FormLabel>
              <Select onValueChange={(value) => field.onChange([value])} defaultValue={field.value?.[0]}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder={t('overtimeForm.placeholder.selectEmployee', 'Select employee(s)')} />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {employeeOptions.map(emp => (
                    <SelectItem key={emp.value} value={emp.value}>{emp.label}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormDescription>{t('overtimeForm.description.selectEmployee', 'Select one or more employees.')}</FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4"> {/* Reduced gap-6 to gap-4 */}
          <FormField
            control={form.control}
            name="date"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>{t('overtimeForm.label.date', 'Date')}</FormLabel>
                <Popover>
                  <PopoverTrigger asChild>
                    <FormControl>
                      <Button
                        variant={"outline"}
                        className={cn("pl-3 text-left font-normal", !field.value && "text-muted-foreground")}
                      >
                        {field.value ? format(field.value, "PPP") : <span>{t('form.placeholder.pickDate', 'Pick a date')}</span>}
                        <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                      </Button>
                    </FormControl>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar mode="single" selected={field.value} onSelect={field.onChange} initialFocus />
                  </PopoverContent>
                </Popover>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="hours"
            render={({ field }) => (
              <FormItem>
                <FormLabel>{t('overtimeForm.label.hours', 'Hours')}</FormLabel>
                <FormControl>
                  <Input type="number" step="0.1" placeholder="2.5" {...field} onChange={e => field.onChange(parseFloat(e.target.value))} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="category"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t('overtimeForm.label.category', 'Overtime Category')}</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder={t('overtimeForm.placeholder.selectCategory', 'Select overtime category')} />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="Regular Overtime">{t('overtimeForm.category.regular', 'Regular Overtime')}</SelectItem>
                  <SelectItem value="Holiday Overtime">{t('overtimeForm.category.holiday', 'Holiday Overtime')}</SelectItem>
                  <SelectItem value="Special Project">{t('overtimeForm.category.specialProject', 'Special Project')}</SelectItem>
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="reason"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t('overtimeForm.label.reason', 'Reason (Optional)')}</FormLabel>
              <FormControl>
                <Textarea placeholder={t('overtimeForm.placeholder.reason', 'Reason for overtime, project code, etc.')} {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <Button type="submit" className="w-full md:w-auto">{t('overtimeForm.button.logOvertime', 'Log Overtime')}</Button>
      </form>
    </Form>
  );
}
