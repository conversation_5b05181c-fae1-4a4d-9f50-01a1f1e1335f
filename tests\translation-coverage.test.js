/**
 * Translation Coverage Test
 * 
 * This test ensures all translation keys are present across all supported languages
 * and identifies missing translations to maintain internationalization completeness.
 */

const fs = require('fs');
const path = require('path');

// Supported languages
const LANGUAGES = ['en', 'fr', 'ar'];
const LOCALES_DIR = path.join(__dirname, '../src/locales');

/**
 * Load translation file for a given language
 */
function loadTranslations(language) {
  try {
    const filePath = path.join(LOCALES_DIR, `${language}.json`);
    const content = fs.readFileSync(filePath, 'utf8');
    return JSON.parse(content);
  } catch (error) {
    console.error(`Error loading translations for ${language}:`, error.message);
    return {};
  }
}

/**
 * Get all translation keys from an object (including nested keys)
 */
function getAllKeys(obj, prefix = '') {
  const keys = [];
  
  for (const [key, value] of Object.entries(obj)) {
    const fullKey = prefix ? `${prefix}.${key}` : key;
    
    if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
      keys.push(...getAllKeys(value, fullKey));
    } else {
      keys.push(fullKey);
    }
  }
  
  return keys;
}

/**
 * Compare translation keys across languages
 */
function compareTranslations() {
  const translations = {};
  const allKeys = new Set();
  
  // Load all translations
  for (const lang of LANGUAGES) {
    translations[lang] = loadTranslations(lang);
    const keys = getAllKeys(translations[lang]);
    keys.forEach(key => allKeys.add(key));
  }
  
  // Convert to sorted array for consistent reporting
  const sortedKeys = Array.from(allKeys).sort();
  
  // Find missing translations
  const missingTranslations = {};
  const report = {
    totalKeys: sortedKeys.length,
    languages: {},
    missingByLanguage: {},
    missingByKey: {},
    completeness: {}
  };
  
  // Initialize missing translations tracking
  LANGUAGES.forEach(lang => {
    missingTranslations[lang] = [];
    report.languages[lang] = {
      totalKeys: getAllKeys(translations[lang]).length,
      missingKeys: []
    };
  });
  
  // Check each key across all languages
  sortedKeys.forEach(key => {
    const missingInLanguages = [];
    
    LANGUAGES.forEach(lang => {
      const hasKey = hasNestedKey(translations[lang], key);
      if (!hasKey) {
        missingTranslations[lang].push(key);
        report.languages[lang].missingKeys.push(key);
        missingInLanguages.push(lang);
      }
    });
    
    if (missingInLanguages.length > 0) {
      report.missingByKey[key] = missingInLanguages;
    }
  });
  
  // Calculate completeness percentages
  LANGUAGES.forEach(lang => {
    const totalKeys = sortedKeys.length;
    const missingKeys = missingTranslations[lang].length;
    const completeness = ((totalKeys - missingKeys) / totalKeys * 100).toFixed(2);
    
    report.completeness[lang] = {
      percentage: parseFloat(completeness),
      present: totalKeys - missingKeys,
      missing: missingKeys,
      total: totalKeys
    };
    
    report.missingByLanguage[lang] = missingTranslations[lang];
  });
  
  return report;
}

/**
 * Check if a nested key exists in an object
 */
function hasNestedKey(obj, key) {
  const keys = key.split('.');
  let current = obj;
  
  for (const k of keys) {
    if (current === null || current === undefined || typeof current !== 'object') {
      return false;
    }
    if (!(k in current)) {
      return false;
    }
    current = current[k];
  }
  
  return true;
}

/**
 * Generate a detailed report
 */
function generateReport(report) {
  console.log('\n🌍 TRANSLATION COVERAGE REPORT');
  console.log('=====================================\n');
  
  // Overall statistics
  console.log('📊 OVERALL STATISTICS:');
  console.log(`Total translation keys: ${report.totalKeys}`);
  console.log('');
  
  // Language completeness
  console.log('📈 LANGUAGE COMPLETENESS:');
  LANGUAGES.forEach(lang => {
    const comp = report.completeness[lang];
    const status = comp.percentage === 100 ? '✅' : comp.percentage >= 95 ? '⚠️' : '❌';
    console.log(`${status} ${lang.toUpperCase()}: ${comp.percentage}% (${comp.present}/${comp.total} keys)`);
  });
  console.log('');
  
  // Missing translations by language
  LANGUAGES.forEach(lang => {
    const missing = report.missingByLanguage[lang];
    if (missing.length > 0) {
      console.log(`❌ MISSING IN ${lang.toUpperCase()} (${missing.length} keys):`);
      missing.forEach(key => console.log(`   - ${key}`));
      console.log('');
    }
  });
  
  // Keys missing in multiple languages
  const multiLanguageMissing = Object.entries(report.missingByKey)
    .filter(([key, langs]) => langs.length > 1);
    
  if (multiLanguageMissing.length > 0) {
    console.log('🚨 KEYS MISSING IN MULTIPLE LANGUAGES:');
    multiLanguageMissing.forEach(([key, langs]) => {
      console.log(`   - ${key}: missing in ${langs.join(', ')}`);
    });
    console.log('');
  }
  
  // Recommendations
  console.log('💡 RECOMMENDATIONS:');
  const worstLanguage = LANGUAGES.reduce((worst, lang) => 
    report.completeness[lang].percentage < report.completeness[worst].percentage ? lang : worst
  );
  
  if (report.completeness[worstLanguage].percentage < 100) {
    console.log(`   - Focus on completing ${worstLanguage.toUpperCase()} translations`);
    console.log(`   - ${report.completeness[worstLanguage].missing} keys need translation`);
  }
  
  if (multiLanguageMissing.length > 0) {
    console.log(`   - ${multiLanguageMissing.length} keys are missing in multiple languages`);
    console.log('   - These should be prioritized for translation');
  }
  
  console.log('');
  
  return report;
}

/**
 * Main test function
 */
function runTranslationCoverageTest() {
  console.log('🔍 Running Translation Coverage Test...\n');
  
  // Check if locales directory exists
  if (!fs.existsSync(LOCALES_DIR)) {
    console.error(`❌ Locales directory not found: ${LOCALES_DIR}`);
    process.exit(1);
  }
  
  // Check if all language files exist
  const missingFiles = [];
  LANGUAGES.forEach(lang => {
    const filePath = path.join(LOCALES_DIR, `${lang}.json`);
    if (!fs.existsSync(filePath)) {
      missingFiles.push(lang);
    }
  });
  
  if (missingFiles.length > 0) {
    console.error(`❌ Missing translation files: ${missingFiles.join(', ')}`);
    process.exit(1);
  }
  
  // Run the comparison
  const report = compareTranslations();
  generateReport(report);
  
  // Determine test result
  const allComplete = LANGUAGES.every(lang => 
    report.completeness[lang].percentage === 100
  );
  
  if (allComplete) {
    console.log('✅ All translations are complete!');
    return true;
  } else {
    console.log('❌ Some translations are missing. Please complete them.');
    return false;
  }
}

// Export for use in other files
module.exports = {
  runTranslationCoverageTest,
  compareTranslations,
  generateReport,
  loadTranslations,
  getAllKeys,
  hasNestedKey
};

// Run the test if this file is executed directly
if (require.main === module) {
  const success = runTranslationCoverageTest();
  process.exit(success ? 0 : 1);
}
