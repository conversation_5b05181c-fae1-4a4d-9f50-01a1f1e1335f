"use client";

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, XCircle, Loader2, Shield, AlertTriangle } from 'lucide-react';
import { db } from '@/lib/firebase';
import { collection, doc, setDoc, getDoc } from 'firebase/firestore';

interface SecurityTestResult {
  name: string;
  status: 'pending' | 'success' | 'error';
  message: string;
  details?: string;
  critical?: boolean;
}

export function SecurityTest() {
  const [tests, setTests] = useState<SecurityTestResult[]>([
    { name: 'Unauthorized Company Read', status: 'pending', message: 'Testing unauthorized read access...', critical: true },
    { name: 'Unauthorized Company Write', status: 'pending', message: 'Testing unauthorized write access...', critical: true },
    { name: 'Malicious Company Creation', status: 'pending', message: 'Testing malicious company creation...', critical: true },
    { name: 'Connection Test Collection', status: 'pending', message: 'Testing connection test security...', critical: false },
  ]);
  
  const [isRunning, setIsRunning] = useState(false);

  const updateTest = (index: number, updates: Partial<SecurityTestResult>) => {
    setTests(prev => prev.map((test, i) => i === index ? { ...test, ...updates } : test));
  };

  const runSecurityTests = async () => {
    setIsRunning(true);
    
    try {
      // Test 1: Unauthorized Company Read
      updateTest(0, { status: 'pending', message: 'Attempting unauthorized read...' });
      
      try {
        const unauthorizedRef = doc(collection(db, 'companies'), 'non-existent-company');
        const docSnap = await getDoc(unauthorizedRef);
        
        // If we can read without authentication, that's a security issue
        updateTest(0, { 
          status: 'error', 
          message: 'SECURITY VULNERABILITY: Unauthorized read allowed',
          details: 'Unauthenticated users can read company data',
          critical: true
        });
      } catch (error: any) {
        if (error.code === 'permission-denied') {
          updateTest(0, { 
            status: 'success', 
            message: 'Unauthorized read properly blocked',
            details: 'Security rules correctly deny unauthenticated reads'
          });
        } else {
          updateTest(0, { 
            status: 'error', 
            message: 'Unexpected error during read test',
            details: `Error: ${error.code} - ${error.message}`
          });
        }
      }

      // Test 2: Unauthorized Company Write (Generic)
      updateTest(1, { status: 'pending', message: 'Attempting unauthorized write...' });
      
      try {
        const unauthorizedRef = doc(collection(db, 'companies'), 'malicious-company');
        await setDoc(unauthorizedRef, {
          name: 'Malicious Company',
          email: '<EMAIL>',
          createdAt: new Date()
        });
        
        updateTest(1, { 
          status: 'error', 
          message: 'CRITICAL SECURITY VULNERABILITY',
          details: 'Unauthorized write access allowed - anyone can create companies',
          critical: true
        });
      } catch (error: any) {
        if (error.code === 'permission-denied') {
          updateTest(1, { 
            status: 'success', 
            message: 'Unauthorized write properly blocked',
            details: 'Security rules correctly deny unauthorized company creation'
          });
        } else {
          updateTest(1, { 
            status: 'error', 
            message: 'Unexpected error during write test',
            details: `Error: ${error.code} - ${error.message}`
          });
        }
      }

      // Test 3: Malicious Company Creation (Trying to bypass with test names)
      updateTest(2, { status: 'pending', message: 'Attempting to bypass security with test names...' });
      
      try {
        const maliciousRef = doc(collection(db, 'companies'), 'fake-test-company');
        await setDoc(maliciousRef, {
          name: 'Fake Test Company (Malicious)',
          email: '<EMAIL>',
          createdAt: new Date()
        });
        
        updateTest(2, { 
          status: 'error', 
          message: 'SECURITY BYPASS DETECTED',
          details: 'Malicious actors can create companies by using test name patterns',
          critical: true
        });
      } catch (error: any) {
        if (error.code === 'permission-denied') {
          updateTest(2, { 
            status: 'success', 
            message: 'Malicious bypass attempt blocked',
            details: 'Security rules properly validate legitimate test patterns'
          });
        } else {
          updateTest(2, { 
            status: 'error', 
            message: 'Unexpected error during bypass test',
            details: `Error: ${error.code} - ${error.message}`
          });
        }
      }

      // Test 4: Connection Test Collection Security
      updateTest(3, { status: 'pending', message: 'Testing connection test collection security...' });
      
      try {
        const testRef = doc(collection(db, 'connection_test'), 'unauthorized-test');
        await setDoc(testRef, {
          malicious: 'data',
          timestamp: new Date()
        });
        
        updateTest(3, { 
          status: 'error', 
          message: 'Connection test collection vulnerable',
          details: 'Unauthorized writes allowed to connection_test collection'
        });
      } catch (error: any) {
        if (error.code === 'permission-denied') {
          updateTest(3, { 
            status: 'success', 
            message: 'Connection test collection secured',
            details: 'Only legitimate connection tests can write to this collection'
          });
        } else {
          updateTest(3, { 
            status: 'error', 
            message: 'Unexpected error during connection test',
            details: `Error: ${error.code} - ${error.message}`
          });
        }
      }

    } catch (error: any) {
      console.error('Security test suite error:', error);
    } finally {
      setIsRunning(false);
    }
  };

  const getStatusIcon = (status: SecurityTestResult['status']) => {
    switch (status) {
      case 'pending':
        return <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />;
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" />;
    }
  };

  const getStatusBadge = (status: SecurityTestResult['status'], critical?: boolean) => {
    switch (status) {
      case 'pending':
        return <Badge variant="secondary">Testing</Badge>;
      case 'success':
        return <Badge variant="default" className="bg-green-500">Secure</Badge>;
      case 'error':
        return critical ? 
          <Badge variant="destructive" className="bg-red-600">CRITICAL</Badge> :
          <Badge variant="destructive">Error</Badge>;
    }
  };

  const criticalIssues = tests.filter(test => test.status === 'error' && test.critical).length;
  const allSecurityTestsPassed = tests.every(test => test.status === 'success');

  return (
    <Card className="w-full max-w-3xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Shield className="h-5 w-5" />
          Security Vulnerability Test
        </CardTitle>
        <CardDescription>
          Test for critical security vulnerabilities in Firestore security rules. All tests should show "Secure" status.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-3">
          {tests.map((test, index) => (
            <div key={index} className={`flex items-start gap-3 p-4 border rounded-lg ${
              test.status === 'error' && test.critical ? 'border-red-500 bg-red-50' : ''
            }`}>
              <div className="mt-0.5">
                {getStatusIcon(test.status)}
              </div>
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between gap-2">
                  <h4 className="font-medium">{test.name}</h4>
                  {getStatusBadge(test.status, test.critical)}
                </div>
                <p className="text-sm text-muted-foreground mt-1">{test.message}</p>
                {test.details && (
                  <p className={`text-xs mt-2 p-2 rounded font-mono ${
                    test.status === 'error' && test.critical 
                      ? 'bg-red-100 text-red-800 border border-red-200' 
                      : 'bg-muted text-muted-foreground'
                  }`}>
                    {test.details}
                  </p>
                )}
              </div>
            </div>
          ))}
        </div>

        <Button 
          onClick={runSecurityTests} 
          disabled={isRunning}
          className="w-full"
          size="lg"
        >
          {isRunning ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Running Security Tests...
            </>
          ) : (
            <>
              <Shield className="mr-2 h-4 w-4" />
              Run Security Vulnerability Tests
            </>
          )}
        </Button>

        {allSecurityTestsPassed && !isRunning && (
          <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
            <div className="flex items-center gap-2 text-green-800">
              <CheckCircle className="h-5 w-5" />
              <h4 className="font-medium">All Security Tests Passed!</h4>
            </div>
            <p className="text-sm text-green-700 mt-1">
              ✅ Unauthorized access is properly blocked<br/>
              ✅ Security rules are active and working correctly<br/>
              ✅ No critical vulnerabilities detected
            </p>
          </div>
        )}

        {criticalIssues > 0 && !isRunning && (
          <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-center gap-2 text-red-800">
              <AlertTriangle className="h-5 w-5" />
              <h4 className="font-medium">CRITICAL SECURITY ISSUES DETECTED</h4>
            </div>
            <p className="text-sm text-red-700 mt-1">
              <strong>{criticalIssues} critical security vulnerability(ies) found.</strong><br/>
              These issues must be resolved immediately before using the application in production.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
