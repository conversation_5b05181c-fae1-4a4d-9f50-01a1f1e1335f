// Thème Warm - Mode sombre
export const warmDark = {
  '--background': '25 20% 12%', // Dark warm background
  '--foreground': '35 25% 85%', // Light warm text
  
  '--card': '25 20% 15%', // Dark warm cards
  '--card-foreground': '35 25% 85%',
  
  '--popover': '25 20% 15%',
  '--popover-foreground': '35 25% 85%',
  
  '--primary': '25 70% 65%', // Brighter warm brown
  '--primary-foreground': '25 20% 12%',
  
  '--secondary': '35 30% 20%', // Dark warm beige
  '--secondary-foreground': '35 25% 85%',
  
  '--muted': '35 30% 20%',
  '--muted-foreground': '35 20% 65%',
  
  '--accent': '45 90% 75%', // Brighter golden yellow
  '--accent-foreground': '25 20% 12%',
  
  '--destructive': '0 62.8% 30.6%',
  '--destructive-foreground': '0 0% 98%',
  
  '--border': '25 25% 25%',
  '--input': '25 25% 25%',
  '--ring': '45 90% 75%',
  
  '--radius': '0.375rem',
  
  '--chart-1': '25 70% 65%',
  '--chart-2': '45 90% 75%',
  '--chart-3': '35 30% 50%',
  '--chart-4': '45 50% 60%',
  '--chart-5': '35 60% 55%',
  
  // Sidebar colors for warm dark theme
  '--sidebar-background': '25 20% 8%',
  '--sidebar-foreground': '35 25% 80%',
  '--sidebar-primary': '25 70% 65%',
  '--sidebar-primary-foreground': '25 20% 12%',
  '--sidebar-accent': '35 30% 18%',
  '--sidebar-accent-foreground': '25 70% 65%',
  '--sidebar-border': '25 25% 22%',
  '--sidebar-ring': '45 90% 75%',
};
