import { NextRequest, NextResponse } from 'next/server';
import { getAuth } from 'firebase-admin/auth';
import { getFirestore } from 'firebase-admin/firestore';
import { initializeApp, getApps, cert } from 'firebase-admin/app';

// Initialize Firebase Admin SDK
if (!getApps().length) {
  const serviceAccount = {
    type: 'service_account',
    project_id: process.env.FIREBASE_PROJECT_ID,
    private_key_id: process.env.FIREBASE_PRIVATE_KEY_ID,
    private_key: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
    client_email: process.env.FIREBASE_CLIENT_EMAIL,
    client_id: process.env.FIREBASE_CLIENT_ID,
    auth_uri: 'https://accounts.google.com/o/oauth2/auth',
    token_uri: 'https://oauth2.googleapis.com/token',
    auth_provider_x509_cert_url: 'https://www.googleapis.com/oauth2/v1/certs',
    client_x509_cert_url: `https://www.googleapis.com/robot/v1/metadata/x509/${process.env.FIREBASE_CLIENT_EMAIL}`,
  };

  initializeApp({
    credential: cert(serviceAccount as any),
    projectId: process.env.FIREBASE_PROJECT_ID,
  });
}

const auth = getAuth();
const db = getFirestore();

export interface DeleteUserRequest {
  userId: string;
  companyId: string;
}

export async function DELETE(request: NextRequest) {
  try {
    // Verify the request is from an authenticated admin user
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const idToken = authHeader.split('Bearer ')[1];
    
    // Verify the ID token
    const decodedToken = await auth.verifyIdToken(idToken);

    console.log('🔍 Delete User API: Decoded token claims:', {
      uid: decodedToken.uid,
      email: decodedToken.email,
      customClaims: decodedToken,
      isAdmin: decodedToken.isAdmin,
      isSuperAdmin: decodedToken.isSuperAdmin,
      role: decodedToken.role
    });

    // Check if user has admin permissions
    const hasAdminPermission = decodedToken.isAdmin === true || 
                              decodedToken.isSuperAdmin === true ||
                              decodedToken.role === 'super_admin' ||
                              decodedToken.role === 'company_admin';

    // Double-check with Firestore document if custom claims are not set
    if (!hasAdminPermission) {
      try {
        const userDoc = await db.collection('users').doc(decodedToken.uid).get();
        if (userDoc.exists) {
          const userData = userDoc.data();
          const isFirestoreAdmin = userData?.role === 'super_admin' || 
                                  userData?.role === 'company_admin';
          if (!isFirestoreAdmin) {
            console.log('❌ Delete User API: User lacks admin privileges');
            return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
          }
        } else {
          console.log('❌ Delete User API: User document not found');
          return NextResponse.json({ error: 'User not found' }, { status: 403 });
        }
      } catch (error) {
        console.error('❌ Delete User API: Error checking user permissions:', error);
        return NextResponse.json({ error: 'Permission check failed' }, { status: 500 });
      }
    }

    const body: DeleteUserRequest = await request.json();
    const { userId, companyId } = body;

    // Validate required fields
    if (!userId || !companyId) {
      return NextResponse.json({ error: 'Missing required fields: userId and companyId' }, { status: 400 });
    }

    // Prevent deletion of the current user
    if (userId === decodedToken.uid) {
      return NextResponse.json({ error: 'Cannot delete your own account' }, { status: 400 });
    }

    // Get user data before deletion for validation
    const userDoc = await db.collection('users').doc(userId).get();
    if (!userDoc.exists) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    const userData = userDoc.data();
    
    // Prevent deletion of superuser accounts (unless current user is superuser)
    if (userData?.role === 'super_admin' && decodedToken.role !== 'super_admin') {
      return NextResponse.json({ error: 'Only superuser can delete superuser accounts' }, { status: 403 });
    }

    // Company admin can only delete users from their own company
    if (decodedToken.role === 'company_admin' && userData?.companyId !== decodedToken.companyId) {
      return NextResponse.json({ error: 'Cannot delete users from other companies' }, { status: 403 });
    }

    console.log('🗑️ Delete User API: Deleting user:', userId, userData?.email);

    // Step 1: Delete from Firebase Authentication
    try {
      await auth.deleteUser(userId);
      console.log('✅ Delete User API: Deleted Firebase Auth user:', userId);
    } catch (authError: any) {
      // If user doesn't exist in Auth, continue with Firestore cleanup
      if (authError.code !== 'auth/user-not-found') {
        console.error('❌ Delete User API: Error deleting from Firebase Auth:', authError);
        throw authError;
      }
      console.log('⚠️ Delete User API: User not found in Firebase Auth, continuing with Firestore cleanup');
    }

    // Step 2: Delete user document from Firestore
    await db.collection('users').doc(userId).delete();
    console.log('✅ Delete User API: Deleted Firestore user document:', userId);

    // Step 3: Clean up related data (if any)
    // TODO: Add cleanup for any user-related data in other collections
    // For example: user preferences, audit logs, etc.

    return NextResponse.json({
      success: true,
      message: 'User deleted successfully',
      deletedUser: {
        id: userId,
        email: userData?.email,
        firstName: userData?.firstName,
        lastName: userData?.lastName
      }
    });

  } catch (error) {
    console.error('❌ Delete User API: Error deleting user:', error);
    
    if (error instanceof Error) {
      if (error.message.includes('user-not-found')) {
        return NextResponse.json({ error: 'Utilisateur non trouvé' }, { status: 404 });
      }
      if (error.message.includes('insufficient-permission')) {
        return NextResponse.json({ error: 'Permissions insuffisantes' }, { status: 403 });
      }
    }

    return NextResponse.json({ 
      error: 'Erreur lors de la suppression de l\'utilisateur',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
