# Firestore Integration Guide

This document explains how to use the Firestore integration in WePaie.

## Overview

WePaie uses Google Firestore as its database solution with the following features:

- **Multi-tenancy**: Data isolation by company
- **Type-safe operations**: Full TypeScript support
- **Real-time updates**: React hooks for live data
- **Security**: Firestore security rules
- **Offline support**: Built-in with Firestore
- **Scalability**: Automatic scaling with Firestore

## Setup

### 1. Environment Variables

Create a `.env.local` file with your Firebase configuration:

```env
NEXT_PUBLIC_FIREBASE_API_KEY=your_api_key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_project_id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your_project.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
NEXT_PUBLIC_FIREBASE_APP_ID=your_app_id
```

### 2. Firebase Project Setup

1. Create a Firebase project at https://console.firebase.google.com
2. Enable Firestore Database
3. Enable Authentication (if using auth)
4. Copy your config values to `.env.local`

### 3. Development with Emulators

For local development, you can use Firebase emulators:

```bash
# Start emulators
npm run firebase:emulators

# Start app with emulators
npm run dev:emulators
```

## Data Models

### Company
- Multi-tenant root entity
- Contains settings and subscription info
- Each company has isolated data

### Users
- Belong to a company
- Have roles: `admin` or `payroll_manager`
- Include preferences and profile data

### Employees
- Core entity for HR management
- Soft delete support
- Full employee lifecycle tracking

### Absences & Overtime
- Time tracking entities
- Approval workflow support
- Category-based organization

## Usage Examples

### Using Firestore Services

```typescript
import { employeeService, companyService } from '@/lib/firestore';

// Create a new employee
const employee = await employeeService.createEmployee({
  firstName: 'John',
  lastName: 'Doe',
  email: '<EMAIL>',
  department: 'Engineering',
  position: 'Developer',
  status: 'Active',
  hireDate: '2024-01-15'
}, companyId);

// Get all active employees
const employees = await employeeService.getActiveEmployees(companyId);

// Search employees
const results = await employeeService.searchEmployees(companyId, 'john');
```

### Using React Hooks

```typescript
import { useFirestoreData, useFirestoreMutation } from '@/hooks/useFirestoreData';
import { employeeService } from '@/lib/firestore';

function EmployeeList() {
  // Fetch employees
  const { data: employees, isLoading, refetch } = useFirestoreData({
    service: {
      getAll: (companyId) => employeeService.getActiveEmployees(companyId)
    }
  });

  // Mutation for CRUD operations
  const { create, update, delete: deleteEmployee } = useFirestoreMutation({
    create: employeeService.createEmployee.bind(employeeService),
    update: employeeService.updateEmployee.bind(employeeService),
    delete: employeeService.softDeleteEmployee.bind(employeeService)
  });

  // Create new employee
  const handleCreate = async (data) => {
    const newEmployee = await create(data);
    if (newEmployee) {
      refetch(); // Refresh the list
    }
  };

  return (
    <div>
      {isLoading ? 'Loading...' : employees.map(emp => (
        <div key={emp.id}>{emp.firstName} {emp.lastName}</div>
      ))}
    </div>
  );
}
```

### Using Context

```typescript
import { useFirestore, useCompanyId } from '@/context/FirestoreContext';

function MyComponent() {
  const { company, user, hasPermission } = useFirestore();
  const companyId = useCompanyId();

  if (!companyId) {
    return <div>Please select a company</div>;
  }

  if (!hasPermission('edit_employees')) {
    return <div>Access denied</div>;
  }

  return <div>Welcome {user?.firstName}!</div>;
}
```

## Security

### Firestore Rules

The security rules ensure:
- Users can only access their company's data
- Role-based permissions (admin vs payroll_manager)
- Data validation and sanitization

### Permissions

- `admin`: Full access to all company data
- `payroll_manager`: Access to employees, absences, overtime

## Best Practices

### 1. Always Use Company Context
```typescript
// ✅ Good
const companyId = useCompanyId();
const employees = await employeeService.getActiveEmployees(companyId);

// ❌ Bad
const employees = await employeeService.getActiveEmployees('hardcoded-id');
```

### 2. Handle Loading States
```typescript
const { data, isLoading, error } = useFirestoreData({...});

if (isLoading) return <LoadingSpinner />;
if (error) return <ErrorMessage error={error} />;
return <DataComponent data={data} />;
```

### 3. Use Optimistic Updates
```typescript
const { create, isLoading } = useFirestoreMutation({...});

const handleSubmit = async (data) => {
  const result = await create(data);
  if (result) {
    // Success handling
    refetch();
  }
};
```

### 4. Implement Proper Error Handling
```typescript
try {
  await employeeService.createEmployee(data, companyId);
  toast.success('Employee created successfully');
} catch (error) {
  console.error('Error creating employee:', error);
  toast.error('Failed to create employee');
}
```

## Troubleshooting

### Common Issues

1. **"Missing company ID"**: Ensure FirestoreProvider is properly set up
2. **"Permission denied"**: Check Firestore security rules and user permissions
3. **"Document not found"**: Verify the document exists and user has access

### Debug Mode

Enable debug logging in development:

```typescript
// In firebase.ts
if (process.env.NODE_ENV === 'development') {
  console.log('Firebase initialized with project:', firebaseConfig.projectId);
}
```

## Migration from Mock Data

To migrate existing components:

1. Replace mock data with `useFirestoreData` hook
2. Add loading and error states
3. Update CRUD operations to use Firestore services
4. Add permission checks
5. Test with Firebase emulators

See `src/components/employees/EmployeeTable.tsx` for a complete example.
