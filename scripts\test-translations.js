#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

/**
 * Translation Coverage Testing Script
 * 
 * This script compares translation files to ensure complete coverage
 * across all supported languages. It uses en.json as the reference
 * and checks fr.json and ar.json for missing translations.
 */

const TRANSLATIONS_DIR = path.join(__dirname, '../src/locales');
const REFERENCE_LANG = 'en';
const TARGET_LANGS = ['fr', 'ar'];

// ANSI color codes for console output
const colors = {
  reset: '\x1b[0m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m',
  bold: '\x1b[1m'
};

function colorize(text, color) {
  return `${colors[color]}${text}${colors.reset}`;
}

function loadTranslationFile(lang) {
  const filePath = path.join(TRANSLATIONS_DIR, `${lang}.json`);
  
  if (!fs.existsSync(filePath)) {
    throw new Error(`Translation file not found: ${filePath}`);
  }
  
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    return JSON.parse(content);
  } catch (error) {
    throw new Error(`Failed to parse ${lang}.json: ${error.message}`);
  }
}

function flattenObject(obj, prefix = '') {
  const flattened = {};
  
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      const newKey = prefix ? `${prefix}.${key}` : key;
      
      if (typeof obj[key] === 'object' && obj[key] !== null && !Array.isArray(obj[key])) {
        Object.assign(flattened, flattenObject(obj[key], newKey));
      } else {
        flattened[newKey] = obj[key];
      }
    }
  }
  
  return flattened;
}

function findMissingKeys(reference, target) {
  const referenceKeys = Object.keys(reference);
  const targetKeys = Object.keys(target);
  
  return referenceKeys.filter(key => !targetKeys.includes(key));
}

function findExtraKeys(reference, target) {
  const referenceKeys = Object.keys(reference);
  const targetKeys = Object.keys(target);
  
  return targetKeys.filter(key => !referenceKeys.includes(key));
}

function calculateCoverage(reference, target) {
  const referenceCount = Object.keys(reference).length;
  const targetCount = Object.keys(target).length;
  const missingCount = findMissingKeys(reference, target).length;
  const presentCount = referenceCount - missingCount;
  
  return {
    total: referenceCount,
    present: presentCount,
    missing: missingCount,
    extra: findExtraKeys(reference, target).length,
    percentage: referenceCount > 0 ? Math.round((presentCount / referenceCount) * 100) : 0
  };
}

function printHeader() {
  console.log(colorize('\n🌍 WePaie Translation Coverage Report', 'bold'));
  console.log(colorize('=' .repeat(50), 'cyan'));
}

function printLanguageReport(lang, coverage, missingKeys, extraKeys) {
  const langName = {
    'fr': 'French (Français)',
    'ar': 'Arabic (العربية)'
  }[lang] || lang;
  
  console.log(colorize(`\n📋 ${langName} Translation Report`, 'bold'));
  console.log(colorize('-'.repeat(40), 'blue'));
  
  // Coverage summary
  const coverageColor = coverage.percentage >= 100 ? 'green' : 
                       coverage.percentage >= 90 ? 'yellow' : 'red';
  
  console.log(`${colorize('Coverage:', 'white')} ${colorize(`${coverage.percentage}%`, coverageColor)} (${coverage.present}/${coverage.total})`);
  console.log(`${colorize('Missing:', 'white')} ${colorize(coverage.missing.toString(), coverage.missing > 0 ? 'red' : 'green')}`);
  console.log(`${colorize('Extra:', 'white')} ${colorize(coverage.extra.toString(), coverage.extra > 0 ? 'yellow' : 'green')}`);
  
  // Missing keys
  if (missingKeys.length > 0) {
    console.log(colorize('\n❌ Missing Translation Keys:', 'red'));
    missingKeys.forEach(key => {
      console.log(`  • ${key}`);
    });
  }
  
  // Extra keys
  if (extraKeys.length > 0) {
    console.log(colorize('\n⚠️  Extra Translation Keys (not in reference):', 'yellow'));
    extraKeys.forEach(key => {
      console.log(`  • ${key}`);
    });
  }
  
  if (missingKeys.length === 0 && extraKeys.length === 0) {
    console.log(colorize('\n✅ Perfect translation coverage!', 'green'));
  }
}

function printSummary(results) {
  console.log(colorize('\n📊 Summary', 'bold'));
  console.log(colorize('-'.repeat(20), 'cyan'));
  
  let allPerfect = true;
  let totalMissing = 0;
  
  results.forEach(result => {
    const status = result.coverage.percentage === 100 ? 
      colorize('✅ Complete', 'green') : 
      colorize(`❌ ${result.coverage.missing} missing`, 'red');
    
    console.log(`${result.lang}: ${status}`);
    
    if (result.coverage.percentage < 100) {
      allPerfect = false;
      totalMissing += result.coverage.missing;
    }
  });
  
  console.log(colorize('\n' + '='.repeat(50), 'cyan'));
  
  if (allPerfect) {
    console.log(colorize('🎉 All translations are complete!', 'green'));
    console.log(colorize('✅ Ready for production deployment.', 'green'));
  } else {
    console.log(colorize(`❌ Translation coverage incomplete: ${totalMissing} total missing keys`, 'red'));
    console.log(colorize('⚠️  Please complete translations before production deployment.', 'yellow'));
  }
}

function main() {
  try {
    printHeader();
    
    // Load reference translation file
    console.log(colorize(`\n📖 Loading reference language: ${REFERENCE_LANG}`, 'blue'));
    const referenceTranslations = loadTranslationFile(REFERENCE_LANG);
    const flatReference = flattenObject(referenceTranslations);
    
    console.log(colorize(`✅ Reference loaded: ${Object.keys(flatReference).length} keys`, 'green'));
    
    const results = [];
    
    // Check each target language
    for (const lang of TARGET_LANGS) {
      console.log(colorize(`\n🔍 Checking ${lang} translations...`, 'blue'));
      
      try {
        const targetTranslations = loadTranslationFile(lang);
        const flatTarget = flattenObject(targetTranslations);
        
        const missingKeys = findMissingKeys(flatReference, flatTarget);
        const extraKeys = findExtraKeys(flatReference, flatTarget);
        const coverage = calculateCoverage(flatReference, flatTarget);
        
        results.push({
          lang,
          coverage,
          missingKeys,
          extraKeys
        });
        
        printLanguageReport(lang, coverage, missingKeys, extraKeys);
        
      } catch (error) {
        console.error(colorize(`❌ Error processing ${lang}: ${error.message}`, 'red'));
        results.push({
          lang,
          coverage: { percentage: 0, missing: Object.keys(flatReference).length, total: Object.keys(flatReference).length },
          missingKeys: Object.keys(flatReference),
          extraKeys: []
        });
      }
    }
    
    printSummary(results);
    
    // Exit with error code if translations are incomplete
    const hasIncompleteTranslations = results.some(result => result.coverage.percentage < 100);
    
    if (hasIncompleteTranslations) {
      console.log(colorize('\n💡 To fix missing translations:', 'cyan'));
      console.log('1. Add missing keys to the respective language files');
      console.log('2. Ensure all keys from en.json are present in fr.json and ar.json');
      console.log('3. Run this script again to verify completion');
      
      process.exit(1);
    } else {
      process.exit(0);
    }
    
  } catch (error) {
    console.error(colorize(`❌ Fatal error: ${error.message}`, 'red'));
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = {
  loadTranslationFile,
  flattenObject,
  findMissingKeys,
  calculateCoverage
};
