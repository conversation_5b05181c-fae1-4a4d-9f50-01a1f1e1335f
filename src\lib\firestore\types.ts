// Extended types for Firestore documents that include metadata
import type { 
  Employee, 
  Absence, 
  Overtime, 
  User, 
  UserRole,
  AbsenceCategory,
  OvertimeCategory 
} from '@/types';

// Base document interface with Firestore metadata
export interface FirestoreDocument {
  id: string;
  createdAt: Date;
  updatedAt: Date;
  companyId: string; // For multi-tenancy
}

// Company document for multi-tenancy
export interface Company extends FirestoreDocument {
  name: string;
  email: string;
  phone?: string;
  address?: string;
  settings: {
    workingHours: {
      start: string;
      end: string;
      daysPerWeek: number;
    };
    overtime: {
      enabled: boolean;
      multiplier: number;
    };
    localization?: {
      currency: string;
      timezone: string;
      dateFormat: string;
      timeFormat: string;
      defaultLanguage: string;
    };
  };
  subscription: {
    plan: 'free' | 'basic' | 'premium';
    status: 'active' | 'inactive' | 'suspended';
    expiresAt?: Date;
  };
}

// Extended Employee with Firestore metadata
export interface EmployeeDocument extends Omit<Employee, 'id'>, FirestoreDocument {
  // Additional fields for Firestore
  isDeleted: boolean;
  deletedAt?: Date;
  deletedBy?: string;
}

// Extended User with Firestore metadata
export interface UserDocument extends Omit<User, 'id'>, FirestoreDocument {
  // Additional authentication fields
  isActive: boolean;
  lastLoginAt?: Date;
  emailVerified: boolean;
  // Additional profile fields
  firstName: string;
  lastName: string;
  phone?: string;
  avatar?: string;
  preferences: {
    language: 'en' | 'fr' | 'ar';
    timezone: string;
    notifications: {
      email: boolean;
      push: boolean;
    };
  };
}

// Organizational structure types
export interface Department extends FirestoreDocument {
  name: string;
  description?: string;
  isActive: boolean;
  order: number;
}

export interface Service extends FirestoreDocument {
  name: string;
  description?: string;
  departmentId?: string; // Made optional for simplified hierarchy
  isActive: boolean;
  order: number;
}

export interface Position extends FirestoreDocument {
  name: string;
  description?: string;
  serviceId?: string; // Optional - positions can be service-specific or standalone
  departmentId?: string; // Made optional for simplified hierarchy
  isActive: boolean;
  order: number;
}

// Extended Absence with Firestore metadata
export interface AbsenceDocument extends Omit<Absence, 'id'>, FirestoreDocument {
  // Additional tracking fields
  approvedBy?: string;
  approvedAt?: Date;
  status: 'pending' | 'approved' | 'rejected';
  rejectionReason?: string;
}

// Extended Overtime with Firestore metadata
export interface OvertimeDocument extends Omit<Overtime, 'id'>, FirestoreDocument {
  // Additional tracking fields
  approvedBy?: string;
  approvedAt?: Date;
  status: 'pending' | 'approved' | 'rejected';
  rejectionReason?: string;
}

// Query options for Firestore operations
export interface QueryOptions {
  limit?: number;
  orderBy?: {
    field: string;
    direction: 'asc' | 'desc';
  };
  where?: {
    field: string;
    operator: '==' | '!=' | '<' | '<=' | '>' | '>=' | 'in' | 'not-in' | 'array-contains';
    value: any;
  }[];
  startAfter?: any;
}

// Response wrapper for paginated queries
export interface PaginatedResponse<T> {
  data: T[];
  hasMore: boolean;
  lastDoc?: any;
  total?: number;
}

// Error types for better error handling
export interface FirestoreError {
  code: string;
  message: string;
  details?: any;
}

// Audit log for tracking changes
export interface AuditLog extends FirestoreDocument {
  action: 'create' | 'update' | 'delete';
  resource: 'employee' | 'absence' | 'overtime' | 'user' | 'company';
  resourceId: string;
  userId: string;
  changes?: {
    before?: any;
    after?: any;
  };
  metadata?: {
    userAgent?: string;
    ip?: string;
  };
}
