"use client";

import React from 'react';
import { useTranslation } from '@/context/i18nContext';
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { AbsenceForm } from './AbsenceForm';
import { OvertimeForm } from './OvertimeForm';
import type { Employee } from '@/types';

// Mock data for employees to pass to forms - to be updated with new Employee structure
const mockEmployees: Employee[] = [
  { id: '1', firstName: 'Alice', lastName: 'Wonderland', email: '<EMAIL>', department: 'Engineering', service: 'Frontend', position: 'Software Developer', hourlyRate: 50, hoursPerWeek: 40, overtimeRate: 75, weeklySalary: 2000, status: 'Active', hireDate: '2022-01-15', phoneNumber: '555-0101', address: '123 Rabbit Hole Lane' },
  { id: '2', firstName: '<PERSON>', lastName: 'The Builder', email: '<EMAIL>', department: 'Operations', service: 'Construction', position: 'Manager', hourlyRate: 60, hoursPerWeek: 40, overtimeRate: 90, weeklySalary: 2400, status: 'Active', hireDate: '2021-03-10', phoneNumber: '555-0102', address: '456 Fixit Ave' },
  { id: '3', firstName: 'Charlie', lastName: 'Chaplin', email: '<EMAIL>', department: 'Marketing', service: 'Campaigns', position: 'Specialist', hourlyRate: 45, hoursPerWeek: 35, overtimeRate: 67.5, weeklySalary: 1575, status: 'Inactive', hireDate: '2023-05-20', phoneNumber: '555-0103', address: '789 Tramp St' },
];

export function TimeManagementClient() {
  const { t } = useTranslation();

  return (
    <div className="space-y-3">
      <Tabs defaultValue="absences" className="w-full">
        <TabsList className="grid w-full grid-cols-2 md:w-[360px]">
          <TabsTrigger value="absences">{t('timeManagement.logAbsence', 'Log Absences')}</TabsTrigger>
          <TabsTrigger value="overtime">{t('timeManagement.logOvertime', 'Log Overtime')}</TabsTrigger>
        </TabsList>
        
        <TabsContent value="absences">
          <Card>
            <CardHeader>
              <CardTitle className="font-headline text-base">
                {t('absenceForm.title', 'Log Employee Absences')}
              </CardTitle>
              <CardDescription className="text-sm">
                {t('absenceForm.description', 'Record sick leave, annual leave, or other types of absences for employees.')}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              <AbsenceForm employees={mockEmployees} />
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="overtime">
          <Card>
            <CardHeader>
              <CardTitle className="font-headline text-base">
                {t('overtimeForm.title', 'Log Employee Overtime')}
              </CardTitle>
              <CardDescription className="text-sm">
                {t('overtimeForm.description', 'Record overtime hours worked by employees. Specify date, hours, and category.')}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              <OvertimeForm employees={mockEmployees} />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <Card className="mt-6">
        <CardHeader>
          <CardTitle className="font-headline text-base">
            {t('timeManagement.overview', 'Time Log Overview')}
          </CardTitle>
          <CardDescription className="text-sm">
            {t('timeManagement.overviewDescription', 'A summary of logged absences and overtime. (Placeholder)')}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">
            {t('timeManagement.placeholder', 'Time log data will be displayed here. This could include a calendar view, a list of recent entries, or summary charts.')}
          </p>
        </CardContent>
      </Card>
    </div>
  );
}
