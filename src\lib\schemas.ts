import { z } from 'zod';

export const employeeSchema = z.object({
  firstName: z.string().min(2, { message: "Prénom must be at least 2 characters." }).max(50),
  lastName: z.string().min(2, { message: "Nom must be at least 2 characters." }).max(50),
  phoneNumber: z.string().optional().or(z.literal('')), // Allow empty string
  address: z.string().optional().or(z.literal('')),
  service: z.string().optional().or(z.literal('')),
  position: z.string().min(1, { message: "Please select a position." }),
  hoursPerWeek: z.coerce.number().min(1, { message: "Weekly hours must be at least 1." }).max(168, { message: "Weekly hours cannot exceed 168." }),
  weeklySalary: z.coerce.number().min(0.01, { message: "Weekly salary must be greater than 0." }),
  hourlyRate: z.coerce.number().min(0).optional(), // This will be calculated automatically
  overtimeRate: z.coerce.number().min(0, { message: "Overtime rate must be a positive number." }).optional().or(z.literal('')),
  hireDate: z.string().refine((date) => {
    const parsedDate = new Date(date);
    const today = new Date();
    today.setHours(23, 59, 59, 999); // End of today
    return !isNaN(parsedDate.getTime()) && parsedDate <= today;
  }, { message: "Invalid hire date or date cannot be in the future."}),
  status: z.enum(['Active', 'Inactive']),
}).transform(data => {
  // Automatically calculate hourly rate from weekly salary and hours
  const hourlyRate = data.weeklySalary / data.hoursPerWeek;
  return {
    ...data,
    hourlyRate: Math.round(hourlyRate * 100) / 100, // Round to 2 decimal places
  };
});

export type EmployeeFormData = z.infer<typeof employeeSchema>;


export const absenceSchema = z.object({
  employeeIds: z.array(z.string()).min(1, { message: "At least one employee must be selected." }),
  category: z.enum(['Sick Leave', 'Annual Leave', 'Unpaid Leave', 'Other']),
  startDate: z.date({ required_error: "Start date is required." }),
  endDate: z.date({ required_error: "End date is required." }),
  notes: z.string().max(500).optional(),
}).refine(data => data.endDate >= data.startDate, {
  message: "End date cannot be before start date.",
  path: ["endDate"],
});

export type AbsenceFormData = z.infer<typeof absenceSchema>;


export const overtimeSchema = z.object({
  employeeIds: z.array(z.string()).min(1, { message: "At least one employee must be selected." }),
  date: z.date({ required_error: "Date is required." }),
  hours: z.coerce.number().min(0.1, { message: "Hours must be greater than 0."}).max(24),
  category: z.enum(['Regular Overtime', 'Holiday Overtime', 'Special Project']),
  reason: z.string().max(500).optional(),
});

export type OvertimeFormData = z.infer<typeof overtimeSchema>;
