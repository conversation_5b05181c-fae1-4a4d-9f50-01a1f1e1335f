"use client";

import React, { createContext, useContext, useEffect } from 'react';
import { useTranslation } from './i18nContext';

interface FontContextType {
  applyFonts: () => void;
}

const FontContext = createContext<FontContextType | undefined>(undefined);

export function useFonts() {
  const context = useContext(FontContext);
  if (context === undefined) {
    throw new Error('useFonts must be used within a FontProvider');
  }
  return context;
}

export function FontProvider({ children }: { children: React.ReactNode }) {
  const { language } = useTranslation();

  const applyFonts = () => {
    const root = document.documentElement;
    const html = document.querySelector('html');

    // Load font preferences from localStorage
    const storedLatinFont = localStorage.getItem('wepaie_latin_font') || 'roboto';
    const storedArabicFont = localStorage.getItem('wepaie_arabic_font') || 'noto-arabic';
    const storedTextSize = localStorage.getItem('wepaie_text_size') || 'medium';
    const storedFontStyle = localStorage.getItem('wepaie_font_style') || 'normal';

    // Font mappings
    const latinFontMap: Record<string, string> = {
      'roboto': 'Roboto, sans-serif',
      'inter': 'Inter, sans-serif',
      'opensans': 'Open Sans, sans-serif'
    };

    const arabicFontMap: Record<string, string> = {
      'noto-arabic': 'Noto Sans Arabic, sans-serif',
      'cairo': 'Cairo, sans-serif',
      'amiri': 'Amiri, serif'
    };

    // Apply font families
    const currentFont = language === 'ar' ? arabicFontMap[storedArabicFont] : latinFontMap[storedLatinFont];
    root.style.setProperty('--font-family', currentFont || latinFontMap.roboto);
    root.style.setProperty('--font-latin', latinFontMap[storedLatinFont] || latinFontMap.roboto);
    root.style.setProperty('--font-arabic', arabicFontMap[storedArabicFont] || arabicFontMap['noto-arabic']);

    // Add font family override class to ensure fonts apply
    root.classList.add('font-family-override');

    // Apply text size to html element (where CSS classes are defined)
    if (html) {
      html.classList.remove('text-size-small', 'text-size-medium', 'text-size-large');
      html.classList.add(`text-size-${storedTextSize}`);
    }

    // Apply font style
    root.classList.remove('font-style-normal', 'font-style-medium', 'font-style-bold');
    root.classList.add(`font-style-${storedFontStyle}`);

    // Apply font weight based on style
    const fontWeightMap: Record<string, string> = {
      'normal': '400',
      'medium': '500',
      'bold': '700'
    };
    root.style.setProperty('--font-weight', fontWeightMap[storedFontStyle] || '400');

    console.log('🎨 FontProvider: Applied fonts, text size, and font style', {
      latinFont: latinFontMap[storedLatinFont],
      arabicFont: arabicFontMap[storedArabicFont],
      currentFont,
      textSize: storedTextSize,
      fontStyle: storedFontStyle,
      language,
      htmlElement: !!html
    });
  };

  // Apply fonts on mount and when language changes
  useEffect(() => {
    applyFonts();
  }, [language]);

  // Apply fonts on initial load
  useEffect(() => {
    // Delay to ensure DOM is ready
    const timer = setTimeout(applyFonts, 100);
    return () => clearTimeout(timer);
  }, []);

  return (
    <FontContext.Provider value={{ applyFonts }}>
      {children}
    </FontContext.Provider>
  );
}
