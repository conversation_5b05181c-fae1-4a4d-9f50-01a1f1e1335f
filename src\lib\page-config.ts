// src/lib/page-config.ts

export interface PageConfig {
  title: string;
  description?: string;
  requireAuth?: boolean;
  requireCompany?: boolean;
  showCompanySelector?: boolean;
  headerActions?: React.ReactNode;
}

// Configuration centralisée des pages - inspirée de votre approche
export const pageConfigs: Record<string, PageConfig> = {
  '/': {
    title: 'dashboard.title',
    description: 'dashboard.description',
    requireAuth: true,
  },
  '/dashboard': {
    title: 'dashboard.title', 
    description: 'dashboard.description',
    requireAuth: true,
  },
  '/employees': {
    title: 'employees.title',
    description: 'employees.description', 
    requireAuth: true,
    requireCompany: true,
  },
  '/employees/new': {
    title: 'employees.new.title',
    description: 'employees.new.description',
    requireAuth: true,
    requireCompany: true,
  },
  '/admin': {
    title: 'admin.title',
    description: 'admin.description',
    requireAuth: true,
    showCompanySelector: true,
  },
  '/settings': {
    title: 'settings.title',
    description: 'settings.description',
    requireAuth: true,
  },
  '/time-management': {
    title: 'timeManagement.title',
    description: 'timeManagement.description',
    requireAuth: true,
    requireCompany: true,
  },
  '/login': {
    title: 'auth.login.title',
    requireAuth: false,
  },
  '/signup': {
    title: 'auth.signup.title',
    requireAuth: false,
  },
  '/example-structured': {
    title: 'example.structured.title',
    description: 'example.structured.description',
    requireAuth: true,
    showCompanySelector: false,
  },
};

// Fonction utilitaire pour obtenir la configuration d'une page
export function getPageConfig(pathname: string): PageConfig {
  const config = pageConfigs[pathname];
  
  if (config) {
    return config;
  }

  // Gestion des routes dynamiques
  const pathParts = pathname.split('/').filter(Boolean);
  if (pathParts.length > 0) {
    const basePath = `/${pathParts[0]}`;
    const baseConfig = pageConfigs[basePath];
    
    if (baseConfig) {
      return {
        ...baseConfig,
        title: `${baseConfig.title}.detail`,
      };
    }
  }
  
  // Configuration par défaut
  return {
    title: 'common.unknownPage',
    requireAuth: true,
  };
}

// Fonction pour déduire le titre depuis le chemin (fallback)
export function getTitleFromPath(path: string): string {
  const pathParts = path.split('/').filter(Boolean);
  if (pathParts.length > 0) {
    const lastPart = pathParts[pathParts.length - 1];
    return lastPart.charAt(0).toUpperCase() + lastPart.slice(1).replace(/-/g, ' ');
  }
  return 'Page Inconnue';
}
