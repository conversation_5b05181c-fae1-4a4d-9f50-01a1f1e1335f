/**
 * Demo Data Management
 * 
 * Provides utilities for managing demo company and user data
 * to ensure the application has a working context for development and testing.
 */

import { companyService, userService } from '@/lib/firestore';
import type { Company, UserDocument } from '@/lib/firestore/types';

// Demo company and user IDs for consistent reference
export const DEMO_COMPANY_ID = 'demo-company-wepaie';
export const DEMO_ADMIN_ID = 'demo-admin-wepaie';

/**
 * Demo company data
 */
export const DEMO_COMPANY_DATA = {
  name: 'WePaie Demo Company',
  email: '<EMAIL>',
  phone: '+1 (555) 123-DEMO',
  address: '123 Demo Street, Demo City, DC 12345'
};

/**
 * Demo admin user data
 */
export const DEMO_ADMIN_DATA = {
  firstName: 'Demo',
  lastName: 'Admin',
  email: '<EMAIL>',
  name: 'Demo Admin',
  role: 'admin' as const
};

/**
 * Check if demo company exists
 */
export async function demoCompanyExists(): Promise<boolean> {
  try {
    const company = await companyService.getById(DEMO_COMPANY_ID);
    return company !== null;
  } catch (error) {
    return false;
  }
}

/**
 * Check if demo admin user exists
 */
export async function demoAdminExists(): Promise<boolean> {
  try {
    const user = await userService.getById(DEMO_ADMIN_ID, DEMO_COMPANY_ID);
    return user !== null;
  } catch (error) {
    return false;
  }
}

/**
 * Create demo company if it doesn't exist
 */
export async function ensureDemoCompany(): Promise<Company> {
  try {
    // Try to get existing demo company
    console.log('🔍 Checking for existing demo company...');
    const existingCompany = await companyService.getById(DEMO_COMPANY_ID);
    if (existingCompany) {
      console.log('✅ Found existing demo company:', existingCompany.name);
      return existingCompany;
    }
  } catch (error: any) {
    // Company doesn't exist, create it
    console.log('📝 Demo company not found, creating new one...');
    if (error.code !== 'not-found') {
      console.warn('⚠️ Unexpected error checking for demo company:', error);
    }
  }

  try {
    // Create demo company with specific ID
    console.log('🏢 Creating demo company with data:', DEMO_COMPANY_DATA);
    const company = await companyService.create({
      ...DEMO_COMPANY_DATA,
      settings: companyService.createDefaultSettings(),
      subscription: companyService.createDefaultSubscription(),
    }, DEMO_COMPANY_ID);

    console.log('✅ Demo company created successfully:', company.id);
    return company;
  } catch (error: any) {
    console.error('❌ Failed to create demo company:', error);
    throw new Error(`Failed to create demo company: ${error.message}`);
  }
}

/**
 * Create demo admin user if it doesn't exist
 */
export async function ensureDemoAdmin(companyId: string = DEMO_COMPANY_ID): Promise<UserDocument> {
  try {
    // Try to get existing demo admin
    console.log('🔍 Checking for existing demo admin...');
    const existingAdmin = await userService.getById(DEMO_ADMIN_ID, companyId);
    if (existingAdmin) {
      console.log('✅ Found existing demo admin:', existingAdmin.email);
      return existingAdmin;
    }
  } catch (error: any) {
    // User doesn't exist, create it
    console.log('👤 Demo admin not found, creating new one...');
    if (error.code !== 'not-found') {
      console.warn('⚠️ Unexpected error checking for demo admin:', error);
    }
  }

  try {
    // Create demo admin user with specific ID
    console.log('👨‍💼 Creating demo admin with data:', DEMO_ADMIN_DATA);
    const admin = await userService.createUser({
      ...DEMO_ADMIN_DATA,
    }, companyId, DEMO_ADMIN_ID);

    console.log('✅ Demo admin created successfully:', admin.id);
    return admin;
  } catch (error: any) {
    console.error('❌ Failed to create demo admin:', error);
    throw new Error(`Failed to create demo admin: ${error.message}`);
  }
}

/**
 * Initialize complete demo setup
 * Creates both demo company and admin user if they don't exist
 */
export async function initializeDemoSetup(): Promise<{ company: Company; admin: UserDocument }> {
  console.log('🚀 Initializing demo setup...');

  try {
    // Ensure demo company exists
    console.log('📊 Creating/verifying demo company...');
    const company = await ensureDemoCompany();
    console.log('✅ Demo company ready:', company.id, company.name);

    // Ensure demo admin exists
    console.log('👤 Creating/verifying demo admin...');
    const admin = await ensureDemoAdmin(company.id);
    console.log('✅ Demo admin ready:', admin.id, admin.email);

    console.log('🎉 Demo setup completed successfully!');
    return { company, admin };
  } catch (error: any) {
    console.error('❌ Demo setup failed:', error);

    // Provide more specific error messages
    if (error.code === 'permission-denied') {
      throw new Error(
        'Permission denied: Unable to create demo data. ' +
        'This might be due to Firestore security rules. ' +
        'Please ensure the security rules allow demo data creation.'
      );
    } else if (error.code === 'not-found') {
      throw new Error(
        'Resource not found: Unable to access Firestore. ' +
        'Please check your Firebase configuration and network connection.'
      );
    } else if (error.code === 'unavailable') {
      throw new Error(
        'Service unavailable: Firestore is temporarily unavailable. ' +
        'Please try again in a few moments.'
      );
    } else {
      throw new Error(
        `Demo setup failed: ${error.message || 'Unknown error occurred'}. ` +
        'Please check the browser console for more details.'
      );
    }
  }
}

/**
 * Get demo company and admin IDs for FirestoreProvider initialization
 */
export function getDemoIds(): { companyId: string; userId: string } {
  return {
    companyId: DEMO_COMPANY_ID,
    userId: DEMO_ADMIN_ID
  };
}

/**
 * Check if we're in development mode and should use demo data
 */
export function shouldUseDemoData(): boolean {
  // Disable automatic demo data creation to prevent unauthorized data creation
  // Demo data should only be created through explicit user actions
  return false;

  // Previous logic (disabled):
  // return process.env.NODE_ENV === 'development' ||
  //        process.env.NEXT_PUBLIC_USE_DEMO_DATA === 'true';
}
