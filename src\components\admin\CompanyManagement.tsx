"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { useToast } from '@/hooks/use-toast';
import { useRBAC } from '@/context/RBACContext';
import { useCompany } from '@/context/CompanyContext';
import { companyService, userService } from '@/lib/firestore';
import type { Company } from '@/lib/firestore/types';
import {
  Building2,
  Plus,
  MoreHorizontal,
  Edit,
  Trash2,
  Users,
  Calendar,
  CheckCircle,
  XCircle,
  Loader2,
  Search,
  UserPlus,
  Shield
} from 'lucide-react';

interface CompanyFormData {
  name: string;
  email: string;
  phone: string;
  address: string;
  description: string;
  currency: string;
  timezone: string;
  dateFormat: string;
  timeFormat: string;
  defaultLanguage: string;
}

interface AdminFormData {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  password: string;
  confirmPassword: string;
}

export function CompanyManagement() {
  const [companies, setCompanies] = useState<Company[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isCreateAdminModalOpen, setIsCreateAdminModalOpen] = useState(false);
  const [selectedCompany, setSelectedCompany] = useState<Company | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState<CompanyFormData>({
    name: '',
    email: '',
    phone: '',
    address: '',
    description: '',
    currency: 'MAD',
    timezone: 'Africa/Casablanca',
    dateFormat: 'DD/MM/YYYY',
    timeFormat: '24h',
    defaultLanguage: 'fr'
  });
  const [adminFormData, setAdminFormData] = useState<AdminFormData>({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    password: '',
    confirmPassword: ''
  });

  const { toast } = useToast();
  const { isSuperAdmin } = useRBAC();
  const { refreshAvailableCompanies } = useCompany();

  // Load companies on component mount
  useEffect(() => {
    loadCompanies();
  }, []);

  const loadCompanies = async () => {
    try {
      setIsLoading(true);
      console.log('🏢 CompanyManagement: Loading companies for superuser');

      // Load all companies for superuser
      const companies = await companyService.getAll();
      setCompanies(companies);
      console.log('🏢 CompanyManagement: Loaded companies:', companies.length);

    } catch (error: any) {
      console.error('🏢 CompanyManagement: Error loading companies:', error);
      toast({
        title: "Erreur de chargement",
        description: "Impossible de charger la liste des entreprises.",
        variant: "destructive"
      });
      setCompanies([]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateCompany = async () => {
    if (!formData.name.trim()) {
      toast({
        title: "Erreur de validation",
        description: "Le nom de l'entreprise est requis.",
        variant: "destructive"
      });
      return;
    }

    try {
      setIsSubmitting(true);
      console.log('🏢 CompanyManagement: Creating company:', formData.name);

      // Generate a company ID from the name
      const companyId = formData.name.toLowerCase()
        .replace(/[^a-z0-9]/g, '-')
        .replace(/-+/g, '-')
        .replace(/^-|-$/g, '')
        .substring(0, 50) + '-' + Date.now();

      const newCompany: Company = {
        id: companyId,
        name: formData.name.trim(),
        email: formData.email.trim(),
        phone: formData.phone.trim(),
        address: formData.address.trim(),
        description: formData.description.trim(),
        isActive: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        settings: {
          workingHours: {
            start: '08:00',
            end: '17:00',
            daysPerWeek: 5
          },
          overtime: {
            enabled: true,
            multiplier: 1.5
          },
          localization: {
            currency: formData.currency,
            timezone: formData.timezone,
            dateFormat: formData.dateFormat,
            timeFormat: formData.timeFormat,
            defaultLanguage: formData.defaultLanguage
          }
        }
      };

      // Use the base service to create with explicit ID
      await companyService.createWithId(companyId, newCompany);

      console.log('🏢 CompanyManagement: Company created successfully:', companyId);

      toast({
        title: "Entreprise créée",
        description: `L'entreprise "${formData.name}" a été créée avec succès.`
      });

      // Reset form and close modal
      setFormData({
        name: '',
        email: '',
        phone: '',
        address: '',
        description: '',
        currency: 'MAD',
        timezone: 'Africa/Casablanca',
        dateFormat: 'DD/MM/YYYY',
        timeFormat: '24h',
        defaultLanguage: 'fr'
      });
      setIsCreateModalOpen(false);

      // Reload companies list and refresh company context
      await loadCompanies();
      await refreshAvailableCompanies();

    } catch (error: any) {
      console.error('🏢 CompanyManagement: Error creating company:', error);
      toast({
        title: "Erreur de création",
        description: error.message || "Impossible de créer l'entreprise.",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleEditCompany = async () => {
    if (!selectedCompany || !formData.name.trim()) {
      return;
    }

    try {
      setIsSubmitting(true);
      
      const updatedData = {
        name: formData.name.trim(),
        email: formData.email.trim(),
        phone: formData.phone.trim(),
        address: formData.address.trim(),
        description: formData.description.trim(),
        settings: {
          ...selectedCompany.settings,
          localization: {
            currency: formData.currency,
            timezone: formData.timezone,
            dateFormat: formData.dateFormat,
            timeFormat: formData.timeFormat,
            defaultLanguage: formData.defaultLanguage
          }
        }
      };

      await companyService.update(selectedCompany.id, updatedData);
      
      toast({
        title: "Entreprise modifiée",
        description: `L'entreprise "${formData.name}" a été modifiée avec succès.`
      });

      setIsEditModalOpen(false);
      setSelectedCompany(null);
      await loadCompanies();
      await refreshAvailableCompanies();
      
    } catch (error: any) {
      console.error('Error updating company:', error);
      toast({
        title: "Erreur de modification",
        description: error.message || "Impossible de modifier l'entreprise.",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDeleteCompany = async (company: Company) => {
    try {
      await companyService.delete(company.id);
      
      toast({
        title: "Entreprise supprimée",
        description: `L'entreprise "${company.name}" a été supprimée avec succès.`
      });

      await loadCompanies();
      await refreshAvailableCompanies();
      
    } catch (error: any) {
      console.error('Error deleting company:', error);
      toast({
        title: "Erreur de suppression",
        description: error.message || "Impossible de supprimer l'entreprise.",
        variant: "destructive"
      });
    }
  };

  const handleToggleCompanyStatus = async (company: Company) => {
    try {
      await companyService.update(company.id, { isActive: !company.isActive });

      toast({
        title: "Statut modifié",
        description: `L'entreprise "${company.name}" a été ${!company.isActive ? 'activée' : 'désactivée'}.`
      });

      await loadCompanies();
      await refreshAvailableCompanies();

    } catch (error: any) {
      console.error('Error toggling company status:', error);
      toast({
        title: "Erreur de modification",
        description: error.message || "Impossible de modifier le statut de l'entreprise.",
        variant: "destructive"
      });
    }
  };

  const handleCreateCompanyAdmin = async () => {
    if (!selectedCompany) return;

    // Validate form
    if (!adminFormData.firstName.trim() || !adminFormData.lastName.trim() || !adminFormData.email.trim()) {
      toast({
        title: "Erreur de validation",
        description: "Veuillez remplir tous les champs obligatoires.",
        variant: "destructive"
      });
      return;
    }

    if (adminFormData.password !== adminFormData.confirmPassword) {
      toast({
        title: "Erreur de validation",
        description: "Les mots de passe ne correspondent pas.",
        variant: "destructive"
      });
      return;
    }

    if (adminFormData.password.length < 6) {
      toast({
        title: "Erreur de validation",
        description: "Le mot de passe doit contenir au moins 6 caractères.",
        variant: "destructive"
      });
      return;
    }

    try {
      setIsSubmitting(true);

      // Create company admin user
      const newAdmin = {
        firstName: adminFormData.firstName.trim(),
        lastName: adminFormData.lastName.trim(),
        email: adminFormData.email.trim(),
        phone: adminFormData.phone.trim(),
        role: 'company_admin' as const,
        companyId: selectedCompany.id,
        isActive: true,
        permissions: [
          'users.read', 'users.create', 'users.update', 'users.delete',
          'employees.read', 'employees.create', 'employees.update', 'employees.delete',
          'services.read', 'services.create', 'services.update', 'services.delete',
          'positions.read', 'positions.create', 'positions.update', 'positions.delete',
          'company.read', 'company.update'
        ]
      };

      // In a real implementation, this would create the user in Firebase Auth
      // and then create the user document in Firestore
      console.log('Creating company admin:', newAdmin);

      toast({
        title: "Administrateur créé",
        description: `L'administrateur ${adminFormData.firstName} ${adminFormData.lastName} a été créé pour l'entreprise "${selectedCompany.name}".`
      });

      // Reset form and close modal
      setAdminFormData({
        firstName: '',
        lastName: '',
        email: '',
        phone: '',
        password: '',
        confirmPassword: ''
      });
      setIsCreateAdminModalOpen(false);
      setSelectedCompany(null);

    } catch (error: any) {
      console.error('Error creating company admin:', error);
      toast({
        title: "Erreur de création",
        description: error.message || "Impossible de créer l'administrateur.",
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const openEditModal = (company: Company) => {
    setSelectedCompany(company);
    setFormData({
      name: company.name,
      email: company.email || '',
      phone: company.phone || '',
      address: company.address || '',
      description: company.description || '',
      currency: company.settings?.localization?.currency || 'MAD',
      timezone: company.settings?.localization?.timezone || 'Africa/Casablanca',
      dateFormat: company.settings?.localization?.dateFormat || 'DD/MM/YYYY',
      timeFormat: company.settings?.localization?.timeFormat || '24h',
      defaultLanguage: company.settings?.localization?.defaultLanguage || 'fr'
    });
    setIsEditModalOpen(true);
  };

  const resetForm = () => {
    setFormData({
      name: '',
      email: '',
      phone: '',
      address: '',
      description: '',
      currency: 'MAD',
      timezone: 'Africa/Casablanca',
      dateFormat: 'DD/MM/YYYY',
      timeFormat: '24h',
      defaultLanguage: 'fr'
    });
  };

  // Filter companies based on search term
  const filteredCompanies = companies.filter(company =>
    company.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (company.email && company.email.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  if (!isSuperAdmin()) {
    return (
      <Card className="border-amber-200 bg-amber-50">
        <CardContent className="pt-6">
          <div className="text-center">
            <Building2 className="h-12 w-12 mx-auto mb-4 text-amber-600" />
            <h3 className="font-medium text-amber-800 mb-2">Accès Restreint</h3>
            <p className="text-sm text-amber-700">
              La gestion des entreprises est réservée aux super-administrateurs.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Search and Create Button */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div className="flex items-center gap-2">
          <Search className="h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Rechercher une entreprise..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="max-w-sm"
          />
        </div>

        <Dialog open={isCreateModalOpen} onOpenChange={setIsCreateModalOpen}>
          <DialogTrigger asChild>
            <Button onClick={resetForm}>
              <Plus className="mr-2 h-4 w-4" />
              Créer une Entreprise
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle>Créer une Nouvelle Entreprise</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="name">Nom de l'entreprise *</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="Nom de l'entreprise"
                />
              </div>
              <div>
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                  placeholder="<EMAIL>"
                />
              </div>
              <div>
                <Label htmlFor="phone">Téléphone</Label>
                <Input
                  id="phone"
                  value={formData.phone}
                  onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
                  placeholder="+212 6XX XX XX XX"
                />
              </div>
              <div>
                <Label htmlFor="address">Adresse</Label>
                <Textarea
                  id="address"
                  value={formData.address}
                  onChange={(e) => setFormData(prev => ({ ...prev, address: e.target.value }))}
                  placeholder="Adresse complète"
                  rows={2}
                />
              </div>
              <div>
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="Description de l'entreprise"
                  rows={2}
                />
              </div>

              {/* Localization Settings */}
              <div className="border-t pt-4">
                <h4 className="font-medium text-sm mb-3">Paramètres de Localisation</h4>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="currency">Devise par défaut</Label>
                    <Select
                      value={formData.currency}
                      onValueChange={(value) => setFormData(prev => ({ ...prev, currency: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="MAD">MAD (Dirham marocain)</SelectItem>
                        <SelectItem value="EUR">EUR (Euro)</SelectItem>
                        <SelectItem value="USD">USD (Dollar américain)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="timezone">Fuseau horaire</Label>
                    <Select
                      value={formData.timezone}
                      onValueChange={(value) => setFormData(prev => ({ ...prev, timezone: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Africa/Casablanca">Africa/Casablanca</SelectItem>
                        <SelectItem value="Europe/Paris">Europe/Paris</SelectItem>
                        <SelectItem value="UTC">UTC</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4 mt-4">
                  <div>
                    <Label htmlFor="dateFormat">Format de date</Label>
                    <Select
                      value={formData.dateFormat}
                      onValueChange={(value) => setFormData(prev => ({ ...prev, dateFormat: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="DD/MM/YYYY">DD/MM/YYYY</SelectItem>
                        <SelectItem value="MM/DD/YYYY">MM/DD/YYYY</SelectItem>
                        <SelectItem value="YYYY-MM-DD">YYYY-MM-DD</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="timeFormat">Format d'heure</Label>
                    <Select
                      value={formData.timeFormat}
                      onValueChange={(value) => setFormData(prev => ({ ...prev, timeFormat: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="24h">24 heures</SelectItem>
                        <SelectItem value="12h">12 heures (AM/PM)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="mt-4">
                  <Label htmlFor="defaultLanguage">Langue par défaut</Label>
                  <Select
                    value={formData.defaultLanguage}
                    onValueChange={(value) => setFormData(prev => ({ ...prev, defaultLanguage: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="fr">Français</SelectItem>
                      <SelectItem value="ar">العربية</SelectItem>
                      <SelectItem value="en">English</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="flex gap-2 pt-4">
                <Button 
                  onClick={handleCreateCompany} 
                  disabled={isSubmitting}
                  className="flex-1"
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Création...
                    </>
                  ) : (
                    'Créer l\'Entreprise'
                  )}
                </Button>
                <Button 
                  variant="outline" 
                  onClick={() => setIsCreateModalOpen(false)}
                  disabled={isSubmitting}
                >
                  Annuler
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Companies Table */}
      <Card>
        <CardContent className="p-6">
          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin" />
              <span className="ml-2">Chargement des entreprises...</span>
            </div>
          ) : filteredCompanies.length === 0 ? (
            <div className="text-center py-8">
              <Building2 className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
              <h3 className="font-medium mb-2">Aucune entreprise trouvée</h3>
              <p className="text-sm text-muted-foreground mb-4">
                {searchTerm ? 'Aucune entreprise ne correspond à votre recherche.' : 'Commencez par créer votre première entreprise.'}
              </p>
              {!searchTerm && (
                <Button onClick={() => setIsCreateModalOpen(true)}>
                  <Plus className="mr-2 h-4 w-4" />
                  Créer une Entreprise
                </Button>
              )}
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Entreprise</TableHead>
                  <TableHead>Contact</TableHead>
                  <TableHead>Date de création</TableHead>
                  <TableHead>Statut</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredCompanies.map((company) => (
                  <TableRow key={company.id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">{company.name}</div>
                        {company.description && (
                          <div className="text-sm text-muted-foreground">{company.description}</div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        {company.email && <div>{company.email}</div>}
                        {company.phone && <div className="text-muted-foreground">{company.phone}</div>}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                        {new Date(company.createdAt).toLocaleDateString('fr-FR')}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge 
                        variant={company.isActive ? "default" : "secondary"}
                        className={company.isActive ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"}
                      >
                        {company.isActive ? (
                          <>
                            <CheckCircle className="mr-1 h-3 w-3" />
                            Active
                          </>
                        ) : (
                          <>
                            <XCircle className="mr-1 h-3 w-3" />
                            Inactive
                          </>
                        )}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon" className="h-8 w-8">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Actions</DropdownMenuLabel>
                          <DropdownMenuItem onClick={() => openEditModal(company)}>
                            <Edit className="mr-2 h-4 w-4" />
                            Modifier
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => {
                            setSelectedCompany(company);
                            setIsCreateAdminModalOpen(true);
                          }}>
                            <UserPlus className="mr-2 h-4 w-4" />
                            Créer Administrateur
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleToggleCompanyStatus(company)}>
                            {company.isActive ? (
                              <>
                                <XCircle className="mr-2 h-4 w-4" />
                                Désactiver
                              </>
                            ) : (
                              <>
                                <CheckCircle className="mr-2 h-4 w-4" />
                                Activer
                              </>
                            )}
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <AlertDialog>
                            <AlertDialogTrigger asChild>
                              <DropdownMenuItem
                                onSelect={(e) => e.preventDefault()}
                                className="text-red-600 hover:!text-red-600 focus:!text-red-600"
                              >
                                <Trash2 className="mr-2 h-4 w-4" />
                                Supprimer
                              </DropdownMenuItem>
                            </AlertDialogTrigger>
                            <AlertDialogContent>
                              <AlertDialogHeader>
                                <AlertDialogTitle>Êtes-vous sûr ?</AlertDialogTitle>
                                <AlertDialogDescription>
                                  Cette action supprimera définitivement l'entreprise "{company.name}" et toutes ses données associées.
                                  Cette action ne peut pas être annulée.
                                </AlertDialogDescription>
                              </AlertDialogHeader>
                              <AlertDialogFooter>
                                <AlertDialogCancel>Annuler</AlertDialogCancel>
                                <AlertDialogAction
                                  onClick={() => handleDeleteCompany(company)}
                                  className="bg-destructive hover:bg-destructive/90"
                                >
                                  Supprimer l'Entreprise
                                </AlertDialogAction>
                              </AlertDialogFooter>
                            </AlertDialogContent>
                          </AlertDialog>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* Edit Company Modal */}
      {selectedCompany && (
        <Dialog open={isEditModalOpen} onOpenChange={setIsEditModalOpen}>
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle>Modifier l'Entreprise</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="edit-name">Nom de l'entreprise *</Label>
                <Input
                  id="edit-name"
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="Nom de l'entreprise"
                />
              </div>
              <div>
                <Label htmlFor="edit-email">Email</Label>
                <Input
                  id="edit-email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                  placeholder="<EMAIL>"
                />
              </div>
              <div>
                <Label htmlFor="edit-phone">Téléphone</Label>
                <Input
                  id="edit-phone"
                  value={formData.phone}
                  onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
                  placeholder="+212 6XX XX XX XX"
                />
              </div>
              <div>
                <Label htmlFor="edit-address">Adresse</Label>
                <Textarea
                  id="edit-address"
                  value={formData.address}
                  onChange={(e) => setFormData(prev => ({ ...prev, address: e.target.value }))}
                  placeholder="Adresse complète"
                  rows={2}
                />
              </div>
              <div>
                <Label htmlFor="edit-description">Description</Label>
                <Textarea
                  id="edit-description"
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="Description de l'entreprise"
                  rows={2}
                />
              </div>

              {/* Localization Settings */}
              <div className="border-t pt-4">
                <h4 className="font-medium text-sm mb-3">Paramètres de Localisation</h4>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="edit-currency">Devise par défaut</Label>
                    <Select
                      value={formData.currency}
                      onValueChange={(value) => setFormData(prev => ({ ...prev, currency: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="MAD">MAD (Dirham marocain)</SelectItem>
                        <SelectItem value="EUR">EUR (Euro)</SelectItem>
                        <SelectItem value="USD">USD (Dollar américain)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="edit-timezone">Fuseau horaire</Label>
                    <Select
                      value={formData.timezone}
                      onValueChange={(value) => setFormData(prev => ({ ...prev, timezone: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Africa/Casablanca">Africa/Casablanca</SelectItem>
                        <SelectItem value="Europe/Paris">Europe/Paris</SelectItem>
                        <SelectItem value="UTC">UTC</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4 mt-4">
                  <div>
                    <Label htmlFor="edit-dateFormat">Format de date</Label>
                    <Select
                      value={formData.dateFormat}
                      onValueChange={(value) => setFormData(prev => ({ ...prev, dateFormat: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="DD/MM/YYYY">DD/MM/YYYY</SelectItem>
                        <SelectItem value="MM/DD/YYYY">MM/DD/YYYY</SelectItem>
                        <SelectItem value="YYYY-MM-DD">YYYY-MM-DD</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="edit-timeFormat">Format d'heure</Label>
                    <Select
                      value={formData.timeFormat}
                      onValueChange={(value) => setFormData(prev => ({ ...prev, timeFormat: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="24h">24 heures</SelectItem>
                        <SelectItem value="12h">12 heures (AM/PM)</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="mt-4">
                  <Label htmlFor="edit-defaultLanguage">Langue par défaut</Label>
                  <Select
                    value={formData.defaultLanguage}
                    onValueChange={(value) => setFormData(prev => ({ ...prev, defaultLanguage: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="fr">Français</SelectItem>
                      <SelectItem value="ar">العربية</SelectItem>
                      <SelectItem value="en">English</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="flex gap-2 pt-4">
                <Button 
                  onClick={handleEditCompany} 
                  disabled={isSubmitting}
                  className="flex-1"
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Modification...
                    </>
                  ) : (
                    'Modifier l\'Entreprise'
                  )}
                </Button>
                <Button 
                  variant="outline" 
                  onClick={() => setIsEditModalOpen(false)}
                  disabled={isSubmitting}
                >
                  Annuler
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )}

      {/* Create Company Admin Modal */}
      {selectedCompany && (
        <Dialog open={isCreateAdminModalOpen} onOpenChange={setIsCreateAdminModalOpen}>
          <DialogContent className="sm:max-w-md">
            <DialogHeader>
              <DialogTitle>Créer un Administrateur</DialogTitle>
              <p className="text-sm text-muted-foreground">
                Créer un administrateur pour l'entreprise "{selectedCompany.name}"
              </p>
            </DialogHeader>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="admin-firstName">Prénom *</Label>
                  <Input
                    id="admin-firstName"
                    value={adminFormData.firstName}
                    onChange={(e) => setAdminFormData(prev => ({ ...prev, firstName: e.target.value }))}
                    placeholder="Prénom"
                  />
                </div>
                <div>
                  <Label htmlFor="admin-lastName">Nom *</Label>
                  <Input
                    id="admin-lastName"
                    value={adminFormData.lastName}
                    onChange={(e) => setAdminFormData(prev => ({ ...prev, lastName: e.target.value }))}
                    placeholder="Nom"
                  />
                </div>
              </div>
              <div>
                <Label htmlFor="admin-email">Email *</Label>
                <Input
                  id="admin-email"
                  type="email"
                  value={adminFormData.email}
                  onChange={(e) => setAdminFormData(prev => ({ ...prev, email: e.target.value }))}
                  placeholder="<EMAIL>"
                />
              </div>
              <div>
                <Label htmlFor="admin-phone">Téléphone</Label>
                <Input
                  id="admin-phone"
                  value={adminFormData.phone}
                  onChange={(e) => setAdminFormData(prev => ({ ...prev, phone: e.target.value }))}
                  placeholder="+212 6XX XX XX XX"
                />
              </div>
              <div>
                <Label htmlFor="admin-password">Mot de passe *</Label>
                <Input
                  id="admin-password"
                  type="password"
                  value={adminFormData.password}
                  onChange={(e) => setAdminFormData(prev => ({ ...prev, password: e.target.value }))}
                  placeholder="Mot de passe (min. 6 caractères)"
                />
              </div>
              <div>
                <Label htmlFor="admin-confirmPassword">Confirmer le mot de passe *</Label>
                <Input
                  id="admin-confirmPassword"
                  type="password"
                  value={adminFormData.confirmPassword}
                  onChange={(e) => setAdminFormData(prev => ({ ...prev, confirmPassword: e.target.value }))}
                  placeholder="Confirmer le mot de passe"
                />
              </div>

              {/* Role Information */}
              <div className="bg-blue-50 p-3 rounded-lg">
                <div className="flex items-center gap-2 text-blue-800 mb-1">
                  <Shield className="h-4 w-4" />
                  <span className="font-medium text-sm">Rôle: Administrateur d'Entreprise</span>
                </div>
                <p className="text-xs text-blue-700">
                  Cet utilisateur aura les permissions complètes pour gérer les utilisateurs,
                  employés et données de l'entreprise "{selectedCompany.name}".
                </p>
              </div>

              <div className="flex gap-2 pt-4">
                <Button
                  onClick={handleCreateCompanyAdmin}
                  disabled={isSubmitting}
                  className="flex-1"
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Création...
                    </>
                  ) : (
                    <>
                      <UserPlus className="mr-2 h-4 w-4" />
                      Créer l'Administrateur
                    </>
                  )}
                </Button>
                <Button
                  variant="outline"
                  onClick={() => {
                    setIsCreateAdminModalOpen(false);
                    setSelectedCompany(null);
                    setAdminFormData({
                      firstName: '',
                      lastName: '',
                      email: '',
                      phone: '',
                      password: '',
                      confirmPassword: ''
                    });
                  }}
                  disabled={isSubmitting}
                >
                  Annuler
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}
