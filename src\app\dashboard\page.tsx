"use client";
import type { Metadata } from 'next';
import AppShell from '@/components/layout/AppShell';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { DollarSign, Users, Briefcase } from 'lucide-react';
import { useTranslation } from '@/context/i18nContext';
import { PageHeader } from '@/components/layout/PageHeader';
import { usePageConfig } from '@/hooks/usePageConfig';

// export const metadata: Metadata = {
//   title: 'Dashboard', // Static title
// };

export default function DashboardPage() {
  const { t } = useTranslation();
  const { title, description } = usePageConfig();

  return (
    <ProtectedRoute requireAuth={true}>
      <AppShell>
        <div className="w-full space-y-4">
          {/* Header de page avec configuration centralisée */}
          <PageHeader
            title={title}
            description={description}
          />

          <div className="grid gap-3 md:grid-cols-2 lg:grid-cols-3">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">{t('dashboard.totalEmployees', 'Total Employees')}</CardTitle>
                <Users className="h-5 w-5 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">150</div>
                <p className="text-xs text-muted-foreground">{t('dashboard.totalEmployees.caption', '+2 from last month')}</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">{t('dashboard.pendingPayroll', 'Pending Payroll')}</CardTitle>
                <DollarSign className="h-5 w-5 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">$45,231.89</div>
                <p className="text-xs text-muted-foreground">{t('dashboard.pendingPayroll.caption', 'Next run: July 30th')}</p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">{t('dashboard.openPositions', 'Open Positions')}</CardTitle>
                <Briefcase className="h-5 w-5 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">5</div>
                <p className="text-xs text-muted-foreground">{t('dashboard.openPositions.caption', '2 new this week')}</p>
              </CardContent>
            </Card>
          </div>

          <Card>
          <CardHeader>
            <CardTitle>{t('dashboard.recentActivity', 'Recent Activity')}</CardTitle>
            <CardDescription>{t('dashboard.recentActivity.description', 'Overview of recent actions in the system.')}</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground">{t('dashboard.recentActivity.empty', 'No recent activity to display.')}</p>
            {/* Placeholder for an activity feed */}
          </CardContent>
          </Card>
        </div>
      </AppShell>
    </ProtectedRoute>
  );
}
