"use client";

import React from 'react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { AlertTriangle, RefreshCw, Shield, Wifi } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface FirebaseError {
  code: string;
  message: string;
  details?: any;
}

interface FirebaseErrorHandlerProps {
  error: FirebaseError | Error | null;
  onRetry?: () => void;
  context?: string;
  showDetails?: boolean;
}

/**
 * Firebase Error Handler Component
 * 
 * Provides user-friendly error messages for common Firebase errors
 * with appropriate actions and troubleshooting guidance.
 */
export function FirebaseErrorHandler({ 
  error, 
  onRetry, 
  context = 'opération', 
  showDetails = false 
}: FirebaseErrorHandlerProps) {
  const { toast } = useToast();

  if (!error) return null;

  const getErrorInfo = (error: FirebaseError | Error) => {
    const code = 'code' in error ? error.code : 'unknown';
    const message = error.message;

    switch (code) {
      case 'permission-denied':
        return {
          title: 'Permissions insuffisantes',
          description: 'Vous n\'avez pas les droits nécessaires pour effectuer cette action. Veuillez vérifier vos permissions ou contacter votre administrateur.',
          icon: Shield,
          variant: 'destructive' as const,
          actionText: 'Actualiser les permissions',
          troubleshooting: [
            'Vérifiez que vous êtes connecté avec le bon compte',
            'Assurez-vous d\'avoir les permissions nécessaires',
            'Contactez votre administrateur si le problème persiste'
          ]
        };

      case 'unauthenticated':
        return {
          title: 'Authentification requise',
          description: 'Vous devez être connecté pour effectuer cette action.',
          icon: Shield,
          variant: 'destructive' as const,
          actionText: 'Se reconnecter',
          troubleshooting: [
            'Connectez-vous à votre compte',
            'Vérifiez que votre session n\'a pas expiré'
          ]
        };

      case 'unavailable':
        return {
          title: 'Service temporairement indisponible',
          description: 'Le service Firebase est temporairement indisponible. Veuillez réessayer dans quelques instants.',
          icon: Wifi,
          variant: 'default' as const,
          actionText: 'Réessayer',
          troubleshooting: [
            'Vérifiez votre connexion internet',
            'Attendez quelques minutes et réessayez',
            'Contactez le support si le problème persiste'
          ]
        };

      case 'not-found':
        return {
          title: 'Ressource non trouvée',
          description: 'La ressource demandée n\'existe pas ou a été supprimée.',
          icon: AlertTriangle,
          variant: 'default' as const,
          actionText: 'Actualiser',
          troubleshooting: [
            'Vérifiez que la ressource existe',
            'Actualisez la page',
            'Vérifiez vos permissions d\'accès'
          ]
        };

      case 'already-exists':
        return {
          title: 'Ressource déjà existante',
          description: 'Une ressource avec ces informations existe déjà.',
          icon: AlertTriangle,
          variant: 'default' as const,
          actionText: 'Modifier',
          troubleshooting: [
            'Vérifiez les données saisies',
            'Utilisez des identifiants uniques',
            'Modifiez la ressource existante si nécessaire'
          ]
        };

      case 'quota-exceeded':
        return {
          title: 'Quota dépassé',
          description: 'Les limites d\'utilisation ont été dépassées.',
          icon: AlertTriangle,
          variant: 'destructive' as const,
          actionText: 'Contacter l\'administrateur',
          troubleshooting: [
            'Contactez votre administrateur système',
            'Vérifiez les limites de votre plan',
            'Réessayez plus tard'
          ]
        };

      default:
        return {
          title: 'Erreur inattendue',
          description: `Une erreur inattendue s'est produite lors de ${context}. ${message}`,
          icon: AlertTriangle,
          variant: 'destructive' as const,
          actionText: 'Réessayer',
          troubleshooting: [
            'Actualisez la page',
            'Vérifiez votre connexion internet',
            'Contactez le support technique'
          ]
        };
    }
  };

  const errorInfo = getErrorInfo(error);
  const Icon = errorInfo.icon;

  const handleRetry = () => {
    if (onRetry) {
      onRetry();
    } else {
      window.location.reload();
    }
  };

  const copyErrorDetails = () => {
    const details = {
      code: 'code' in error ? error.code : 'unknown',
      message: error.message,
      context,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href
    };

    navigator.clipboard.writeText(JSON.stringify(details, null, 2));
    toast({
      title: 'Détails copiés',
      description: 'Les détails de l\'erreur ont été copiés dans le presse-papiers.'
    });
  };

  return (
    <Alert variant={errorInfo.variant} className="my-4">
      <Icon className="h-4 w-4" />
      <AlertTitle className="flex items-center justify-between">
        {errorInfo.title}
        <div className="flex gap-2">
          {onRetry && (
            <Button
              variant="outline"
              size="sm"
              onClick={handleRetry}
              className="h-7 text-xs"
            >
              <RefreshCw className="h-3 w-3 mr-1" />
              {errorInfo.actionText}
            </Button>
          )}
          {showDetails && (
            <Button
              variant="outline"
              size="sm"
              onClick={copyErrorDetails}
              className="h-7 text-xs"
            >
              Copier les détails
            </Button>
          )}
        </div>
      </AlertTitle>
      <AlertDescription className="mt-2">
        <p>{errorInfo.description}</p>
        
        {showDetails && (
          <details className="mt-3">
            <summary className="cursor-pointer text-sm font-medium">
              Détails techniques
            </summary>
            <div className="mt-2 p-2 bg-muted rounded text-xs font-mono">
              <p><strong>Code:</strong> {'code' in error ? error.code : 'unknown'}</p>
              <p><strong>Message:</strong> {error.message}</p>
              <p><strong>Contexte:</strong> {context}</p>
            </div>
          </details>
        )}

        <details className="mt-3">
          <summary className="cursor-pointer text-sm font-medium">
            Guide de dépannage
          </summary>
          <ul className="mt-2 space-y-1 text-sm">
            {errorInfo.troubleshooting.map((step, index) => (
              <li key={index} className="flex items-start gap-2">
                <span className="text-muted-foreground">•</span>
                <span>{step}</span>
              </li>
            ))}
          </ul>
        </details>
      </AlertDescription>
    </Alert>
  );
}

/**
 * Hook for handling Firebase errors with toast notifications
 */
export function useFirebaseErrorHandler() {
  const { toast } = useToast();

  const handleError = (error: FirebaseError | Error, context?: string) => {
    const code = 'code' in error ? error.code : 'unknown';
    
    let title = 'Erreur';
    let description = error.message;

    switch (code) {
      case 'permission-denied':
        title = 'Permissions insuffisantes';
        description = 'Vous n\'avez pas les droits nécessaires pour cette action.';
        break;
      case 'unauthenticated':
        title = 'Authentification requise';
        description = 'Veuillez vous connecter pour continuer.';
        break;
      case 'unavailable':
        title = 'Service indisponible';
        description = 'Le service est temporairement indisponible.';
        break;
    }

    toast({
      title,
      description: context ? `${description} (${context})` : description,
      variant: 'destructive'
    });
  };

  return { handleError };
}
