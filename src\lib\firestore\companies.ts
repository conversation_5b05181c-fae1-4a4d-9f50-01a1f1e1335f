import { doc, getDoc, setDoc, collection, getDocs, query, orderBy, Timestamp } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import type { Company } from './types';

/**
 * Company service for managing company data
 * Companies are special as they don't have a companyId (they ARE the company)
 */
export class CompanyService {
  private collectionName = 'companies';

  /**
   * Get company by ID
   */
  async getById(id: string): Promise<Company | null> {
    try {
      const docRef = doc(db, this.collectionName, id);
      const docSnap = await getDoc(docRef);

      if (!docSnap.exists()) {
        return null;
      }

      const data = docSnap.data();
      return {
        id: docSnap.id,
        ...data,
        createdAt: data.createdAt?.toDate() || new Date(),
        updatedAt: data.updatedAt?.toDate() || new Date(),
      } as Company;
    } catch (error) {
      console.error('Error getting company:', error);
      throw error;
    }
  }

  /**
   * Create new company
   */
  async create(
    companyData: Omit<Company, 'id' | 'createdAt' | 'updatedAt' | 'companyId'>,
    customId?: string
  ): Promise<Company> {
    try {
      const now = new Date();

      // Create document reference with custom ID or auto-generated ID
      const docRef = customId
        ? doc(db, this.collectionName, customId)
        : doc(collection(db, this.collectionName));

      const data = {
        ...companyData,
        companyId: docRef.id, // Company's companyId is its own ID
        createdAt: Timestamp.fromDate(now),
        updatedAt: Timestamp.fromDate(now),
      };

      await setDoc(docRef, data);

      return {
        id: docRef.id,
        ...data,
        createdAt: now,
        updatedAt: now,
      } as Company;
    } catch (error) {
      console.error('Error creating company:', error);
      throw error;
    }
  }

  /**
   * Get all companies (for superuser)
   */
  async getAll(): Promise<Company[]> {
    try {
      console.log('🏢 CompanyService: Getting all companies');
      const q = query(collection(db, this.collectionName), orderBy('name', 'asc'));
      const querySnapshot = await getDocs(q);

      const companies = querySnapshot.docs.map(doc => {
        const data = doc.data();
        return {
          id: doc.id,
          ...data,
          createdAt: data.createdAt?.toDate() || new Date(),
          updatedAt: data.updatedAt?.toDate() || new Date(),
        } as Company;
      });

      console.log('🏢 CompanyService: Found companies:', companies.length);
      return companies;
    } catch (error) {
      console.error('🏢 CompanyService: Error getting all companies:', error);
      throw error;
    }
  }

  /**
   * Create company with specific ID
   */
  async createWithId(
    id: string,
    companyData: Omit<Company, 'id' | 'createdAt' | 'updatedAt' | 'companyId'>
  ): Promise<Company> {
    return this.create(companyData, id);
  }

  /**
   * Update company
   */
  async update(id: string, updates: Partial<Company>): Promise<Company> {
    try {
      const docRef = doc(db, this.collectionName, id);
      
      // Remove fields that shouldn't be updated
      const { id: _, createdAt, companyId, ...updateData } = updates as any;
      
      const data = {
        ...updateData,
        updatedAt: Timestamp.fromDate(new Date()),
      };

      await setDoc(docRef, data, { merge: true });
      
      // Return updated document
      const updated = await this.getById(id);
      if (!updated) {
        throw new Error('Failed to retrieve updated company');
      }
      
      return updated;
    } catch (error) {
      console.error('Error updating company:', error);
      throw error;
    }
  }

  /**
   * Create default company settings
   */
  createDefaultSettings() {
    return {
      timezone: 'UTC',
      currency: 'USD',
      dateFormat: 'MM/DD/YYYY',
      workingDays: [1, 2, 3, 4, 5], // Monday to Friday
      workingHours: {
        start: '09:00',
        end: '17:00',
      },
    };
  }

  /**
   * Create default subscription
   */
  createDefaultSubscription() {
    return {
      plan: 'free' as const,
      status: 'active' as const,
    };
  }

  /**
   * Check if company exists
   */
  async exists(id: string): Promise<boolean> {
    try {
      const company = await this.getById(id);
      return company !== null;
    } catch (error) {
      return false;
    }
  }

  /**
   * Validate company access for a user
   */
  async validateAccess(companyId: string, userId: string): Promise<boolean> {
    try {
      // This would typically check if the user belongs to the company
      // For now, we'll implement a basic check
      const company = await this.getById(companyId);
      return company !== null;
    } catch (error) {
      return false;
    }
  }
}

// Export singleton instance
export const companyService = new CompanyService();
