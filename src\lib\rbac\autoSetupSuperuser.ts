/**
 * Auto-setup superuser utility
 * 
 * This utility automatically configures the first user as a superuser
 * if no superuser exists in the system.
 */

import { getAuth } from 'firebase-admin/auth';
import { getFirestore } from 'firebase-admin/firestore';
import { initializeApp, getApps, cert } from 'firebase-admin/app';

// Initialize Firebase Admin SDK if not already initialized
function initializeFirebaseAdmin() {
  if (!getApps().length) {
    const serviceAccount = {
      type: "service_account",
      project_id: process.env.FIREBASE_PROJECT_ID,
      private_key_id: process.env.FIREBASE_PRIVATE_KEY_ID,
      private_key: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
      client_email: process.env.FIREBASE_CLIENT_EMAIL,
      client_id: process.env.FIREBASE_CLIENT_ID,
      auth_uri: "https://accounts.google.com/o/oauth2/auth",
      token_uri: "https://oauth2.googleapis.com/token",
      auth_provider_x509_cert_url: "https://www.googleapis.com/oauth2/v1/certs",
      client_x509_cert_url: `https://www.googleapis.com/robot/v1/metadata/x509/${process.env.FIREBASE_CLIENT_EMAIL}`
    };

    initializeApp({
      credential: cert(serviceAccount as any),
      projectId: process.env.FIREBASE_PROJECT_ID,
    });
  }
}

export async function autoSetupSuperuser(userUid: string, userEmail: string): Promise<boolean> {
  try {
    initializeFirebaseAdmin();
    const auth = getAuth();
    const db = getFirestore();

    console.log('🔍 AutoSetup: Checking if superuser setup is needed for:', userEmail);

    // Check if any superuser already exists
    const existingSuperusers = await db.collection('users')
      .where('role', '==', 'super_admin')
      .where('isActive', '==', true)
      .get();

    if (!existingSuperusers.empty) {
      console.log('🔍 AutoSetup: Superuser already exists, skipping auto-setup');
      return false;
    }

    console.log('🔧 AutoSetup: No superuser found, setting up:', userEmail);

    // Create user document in Firestore
    const userData = {
      id: userUid,
      email: userEmail,
      firstName: 'Super',
      lastName: 'User',
      role: 'super_admin',
      companyId: null,
      isActive: true,
      phoneNumber: null,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    await db.collection('users').doc(userUid).set(userData);
    console.log('✅ AutoSetup: Created Firestore user document');

    // Set custom claims
    const customClaims = {
      role: 'super_admin',
      companyId: null,
      isAdmin: true,
      isSuperAdmin: true,
      isPayrollManager: true,
      isViewer: false
    };

    await auth.setCustomUserClaims(userUid, customClaims);
    console.log('✅ AutoSetup: Set custom claims');

    console.log('🎉 AutoSetup: Successfully configured superuser:', userEmail);
    return true;

  } catch (error) {
    console.error('❌ AutoSetup: Error setting up superuser:', error);
    return false;
  }
}
