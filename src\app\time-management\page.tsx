import type { <PERSON>ada<PERSON> } from 'next';
import AppShell from '@/components/layout/AppShell';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { AbsenceForm } from '@/components/time-management/AbsenceForm';
import { OvertimeForm } from '@/components/time-management/OvertimeForm';

import type { Employee } from '@/types'; // For mock data

export const metadata: Metadata = {
  title: 'Time Management',
};

// Mock data for employees to pass to forms - to be updated with new Employee structure
const mockEmployees: Employee[] = [
  { id: '1', firstName: 'Alice', lastName: 'Wonderland', email: '<EMAIL>', department: 'Engineering', service: 'Frontend', position: 'Software Developer', hourlyRate: 50, hoursPerWeek: 40, overtimeRate: 75, weeklySalary: 2000, status: 'Active', hireDate: '2022-01-15', phoneNumber: '555-0101', address: '123 Rabbit Hole Lane' },
  { id: '2', firstName: 'Bob', lastName: 'The Builder', email: '<EMAIL>', department: 'Operations', service: 'Construction', position: 'Manager', hourlyRate: 60, hoursPerWeek: 40, overtimeRate: 90, weeklySalary: 2400, status: 'Active', hireDate: '2021-03-10', phoneNumber: '555-0102', address: '456 Fixit Ave' },
  { id: '3', firstName: 'Charlie', lastName: 'Chaplin', email: '<EMAIL>', department: 'Marketing', service: 'Campaigns', position: 'Specialist', hourlyRate: 45, hoursPerWeek: 35, overtimeRate: 67.5, weeklySalary: 1575, status: 'Inactive', hireDate: '2023-05-20', phoneNumber: '555-0103', address: '789 Tramp St' },
];

export default function TimeManagementPage() {
  return (
    <ProtectedRoute requireAuth={true}>
      <AppShell pageTitle="Time Management">
      <div className="w-full space-y-4">
        {/* Full-width header to establish consistent page width */}
        <div className="w-full border-b border-border pb-4">
          <h1 className="text-2xl font-bold">Gestion du Temps</h1>
          <p className="text-muted-foreground">Enregistrement des absences et heures supplémentaires</p>
        </div>

        <Tabs defaultValue="absences" className="w-full">
        <TabsList className="grid w-full grid-cols-2 md:w-[360px]">
          <TabsTrigger value="absences">Log Absences</TabsTrigger>
          <TabsTrigger value="overtime">Log Overtime</TabsTrigger>
        </TabsList>
        <TabsContent value="absences">
          <Card>
            <CardHeader>
              <CardTitle className="font-headline">Log Employee Absences</CardTitle>
              <CardDescription>
                Record sick leave, annual leave, or other types of absences for employees.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-3"> {/* space-y-4 to space-y-3 */}
              <AbsenceForm employees={mockEmployees} />
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="overtime">
          <Card>
            <CardHeader>
              <CardTitle className="font-headline">Log Employee Overtime</CardTitle>
              <CardDescription>
                Record overtime hours worked by employees. Specify date, hours, and category.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-3"> {/* space-y-4 to space-y-3 */}
              <OvertimeForm employees={mockEmployees} />
            </CardContent>
          </Card>
        </TabsContent>
        </Tabs>

        <Card>
        <CardHeader>
            <CardTitle className="font-headline">Time Log Overview</CardTitle>
            <CardDescription>A summary of logged absences and overtime. (Placeholder)</CardDescription>
        </CardHeader>
        <CardContent>
            <p className="text-muted-foreground">Time log data will be displayed here. This could include a calendar view, a list of recent entries, or summary charts.</p>
            {/* Placeholder for displaying logged time data */}
        </CardContent>
        </Card>
        </div>
      </AppShell>
    </ProtectedRoute>
  );
}
