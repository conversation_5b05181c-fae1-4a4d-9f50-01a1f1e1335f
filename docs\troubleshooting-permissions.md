# Troubleshooting Firebase Permissions

This guide helps you resolve common Firebase permission issues in WePaie.

## Common Error: "Missing or insufficient permissions"

This error occurs when trying to access Firestore without proper authentication or when security rules block the operation.

### Understanding the Error

The error "Missing or insufficient permissions" means:
1. **No Authentication**: You're trying to access Firestore without being logged in
2. **Insufficient Permissions**: Your user doesn't have the required role/permissions
3. **Security Rules**: Firestore security rules are blocking the operation
4. **Wrong Company Context**: You're trying to access data from a different company

## Solutions

### Solution 1: Use the Quick Start (Recommended)

The easiest way to get started is using our Quick Start feature:

1. Go to: http://localhost:9002/test-firebase
2. Click on the "Quick Start" tab
3. Click "Create Demo Setup"
4. This will create a demo company and admin user that bypasses authentication requirements

### Solution 2: Understand the Security Model

WePaie uses a multi-tenant security model:

```
Company A Data ← Only Company A users can access
Company B Data ← Only Company B users can access
```

**Security Rules Hierarchy:**
1. **Admin**: Full access to their company's data
2. **Payroll Manager**: Limited access to employees, absences, overtime
3. **Unauthenticated**: No access (blocked by security rules)

### Solution 3: Development Testing Rules

We've added special rules for development testing that allow:
- Connection tests with `source: 'WePaie_Connection_Test'`
- Company creation with names containing "Test Company"

These rules are automatically applied when you use our testing components.

### Solution 4: Check Your Firebase Configuration

Verify your `.env.local` file has the correct values:

```env
NEXT_PUBLIC_FIREBASE_API_KEY=AIzaSyDc2twEKEd2x-FIKDLnup-p_uSd5Crar2o
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=wepaie.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=wepaie
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=wepaie.firebasestorage.app
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=578673015610
NEXT_PUBLIC_FIREBASE_APP_ID=1:578673015610:web:3951724700e67e578e2e03
```

## Debugging Steps

### Step 1: Check Firebase Console

1. Go to: https://console.firebase.google.com/project/wepaie
2. Navigate to Firestore Database
3. Check if data is being created
4. Look at the "Rules" tab to verify security rules are deployed

### Step 2: Check Browser Console

1. Open browser developer tools (F12)
2. Look for Firebase-related errors
3. Check for network errors or authentication issues

### Step 3: Verify Security Rules

Current security rules allow:
- **Development Tests**: Documents with specific test markers
- **Authenticated Users**: Access to their company's data only
- **Role-based Access**: Admins and payroll managers have different permissions

### Step 4: Test Connection

Use our connection test:
1. Go to: http://localhost:9002/test-firebase
2. Click "Connection Test" tab
3. Click "Run Connection Tests"
4. Review the results for specific error details

## Expected Behavior

### ✅ Normal Operation (After Setup)

1. **Quick Start Creates**: Demo company and admin user
2. **Connection Test Shows**: All tests passing
3. **Main App Works**: Employee management functions properly
4. **Data Persists**: Information is saved to Firebase

### ❌ Permission Errors (Before Setup)

1. **Raw Firestore Access**: Blocked by security rules
2. **Unauthenticated Operations**: Return permission-denied
3. **Cross-company Access**: Blocked by multi-tenant rules

## Advanced Troubleshooting

### Check Firestore Rules Deployment

```bash
firebase deploy --only firestore:rules
```

### View Current Rules

```bash
firebase firestore:rules
```

### Test Rules Locally

```bash
firebase emulators:start --only firestore
# Then set NEXT_PUBLIC_USE_FIREBASE_EMULATOR=true
```

### Manual Data Verification

1. Go to Firebase Console
2. Navigate to Firestore Database
3. Look for collections: `companies`, `users`, `employees`
4. Verify data structure matches expected format

## Security Rules Explanation

Our security rules implement:

```javascript
// Companies: Only admins can access
match /companies/{companyId} {
  allow read, write: if isAdmin(companyId) || isDevelopmentTest();
}

// Users: Can read own data, admins can manage
match /users/{userId} {
  allow read: if request.auth.uid == userId;
  allow write: if isAdmin(resource.data.companyId);
}

// Employees: Company members can read, managers can write
match /employees/{employeeId} {
  allow read: if isActiveUser(resource.data.companyId);
  allow write: if isPayrollManager(resource.data.companyId);
}
```

## Getting Help

### If Quick Start Fails

1. Check browser console for specific errors
2. Verify Firebase project is active
3. Ensure internet connection is stable
4. Try refreshing the page and running again

### If Connection Test Fails

1. Verify environment variables are correct
2. Check Firebase project permissions
3. Ensure Firestore is enabled in Firebase Console
4. Try switching to emulator mode for testing

### If Main App Shows Errors

1. Ensure you've completed Quick Start
2. Check that company and user context are set
3. Verify you have the required permissions
4. Look for specific error messages in console

## Contact Support

For persistent issues:
1. Check Firebase Console for service status
2. Review application logs for detailed error messages
3. Verify your Firebase project configuration
4. Consider using emulator mode for development

## Quick Commands

```bash
# Start with emulators (no permission issues)
npm run dev:emulators

# Start with production Firebase
npm run dev

# Deploy updated security rules
firebase deploy --only firestore:rules

# Check Firebase login status
firebase login:list
```
