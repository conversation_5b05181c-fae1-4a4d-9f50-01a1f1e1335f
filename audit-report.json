{"timestamp": "2025-06-12T07:34:36.551Z", "stats": {"totalFiles": 146, "filesWithIssues": 14, "totalIssues": 33, "issuesByType": {}}, "issues": [{"file": "src\\app\\login\\page.tsx", "line": 130, "column": 22, "text": "email", "context": "name=\"email\"", "frenchMatches": ["email"], "severity": "high", "suggestedKey": "page.email"}, {"file": "src\\app\\login\\page.tsx", "line": 133, "column": 55, "text": "Email", "context": "<FormLabel>{t('form.label.email', 'Email')}</FormLabel>", "frenchMatches": ["Email"], "severity": "high", "suggestedKey": "page.email"}, {"file": "src\\app\\login\\page.tsx", "line": 152, "column": 58, "text": "Mot de passe", "context": "<FormLabel>{t('form.label.password', 'Mot de passe')}</FormLabel>", "frenchMatches": ["de", "Mot de passe"], "severity": "high", "suggestedKey": "page.motdepasse"}, {"file": "src\\app\\signup\\page.tsx", "line": 37, "column": 30, "text": "email", "context": "<Label htmlFor=\"email\")})})})})})}>{t({t('page.email', 'Email')})})})})})})})}</Label>", "frenchMatches": ["email"], "severity": "high", "suggestedKey": "page.email"}, {"file": "src\\app\\signup\\page.tsx", "line": 37, "column": 70, "text": "Email", "context": "<Label htmlFor=\"email\")})})})})})}>{t({t('page.email', 'Email')})})})})})})})}</Label>", "frenchMatches": ["Email"], "severity": "high", "suggestedKey": "page.email"}, {"file": "src\\components\\admin\\CompanyManagement.tsx", "line": 477, "column": 32, "text": "email", "context": "<Label htmlFor=\"email\")})}>{t('form.label.email', 'Email')}}</Label>", "frenchMatches": ["email"], "severity": "high", "suggestedKey": "companymanagement.email"}, {"file": "src\\components\\admin\\CompanyManagement.tsx", "line": 477, "column": 67, "text": "Email", "context": "<Label htmlFor=\"email\")})}>{t('form.label.email', 'Email')}}</Label>", "frenchMatches": ["Email"], "severity": "high", "suggestedKey": "companymanagement.email"}, {"file": "src\\components\\admin\\CompanyManagement.tsx", "line": 506, "column": 32, "text": "description", "context": "<Label htmlFor=\"description\">{t('form.label.description', 'Description')}<//Label>", "frenchMatches": ["description"], "severity": "high", "suggestedKey": "companymanagement.description"}, {"file": "src\\components\\admin\\CompanyManagement.tsx", "line": 506, "column": 75, "text": "Description", "context": "<Label htmlFor=\"description\">{t('form.label.description', 'Description')}<//Label>", "frenchMatches": ["Description"], "severity": "high", "suggestedKey": "companymanagement.description"}, {"file": "src\\components\\admin\\CompanyManagement.tsx", "line": 808, "column": 32, "text": "email", "context": "<Label htmlFor=\"email\")})}>{t('form.label.email', 'Email')}}</Label>", "frenchMatches": ["email"], "severity": "high", "suggestedKey": "companymanagement.email"}, {"file": "src\\components\\admin\\CompanyManagement.tsx", "line": 808, "column": 67, "text": "Email", "context": "<Label htmlFor=\"email\")})}>{t('form.label.email', 'Email')}}</Label>", "frenchMatches": ["Email"], "severity": "high", "suggestedKey": "companymanagement.email"}, {"file": "src\\components\\admin\\CompanyManagement.tsx", "line": 837, "column": 125, "text": "Description", "context": "<Label htmlFor={t('companymanagement.editdescription', 'editdescription')})})}>{t('form.label.description', 'Description')}<//Label>", "frenchMatches": ["Description"], "severity": "high", "suggestedKey": "companymanagement.description"}, {"file": "src\\components\\admin\\CompanyManagement.tsx", "line": 995, "column": 32, "text": "email", "context": "<Label htmlFor=\"email\")})}>{t('form.label.email', 'Email')} *<//Label>", "frenchMatches": ["email"], "severity": "high", "suggestedKey": "companymanagement.email"}, {"file": "src\\components\\admin\\CompanyManagement.tsx", "line": 995, "column": 67, "text": "Email", "context": "<Label htmlFor=\"email\")})}>{t('form.label.email', 'Email')} *<//Label>", "frenchMatches": ["Email"], "severity": "high", "suggestedKey": "companymanagement.email"}, {"file": "src\\components\\admin\\CreateUserDialog.tsx", "line": 140, "column": 16, "text": "email", "context": "name=\"email\"))", "frenchMatches": ["email"], "severity": "high", "suggestedKey": "createuserdialog.email"}, {"file": "src\\components\\admin\\SystemSettings.tsx", "line": 357, "column": 30, "text": "email", "context": "<Label htmlFor=\"email\")})}>{t('settings.emailNotifications', 'Notifications par email')}<//Label>", "frenchMatches": ["email"], "severity": "high", "suggestedKey": "systemsettings.email"}, {"file": "src\\components\\settings\\CompanySettings.tsx", "line": 422, "column": 24, "text": "email", "context": "name=\"email\"))", "frenchMatches": ["email"], "severity": "high", "suggestedKey": "companysettings.email"}, {"file": "src\\app\\login\\page.tsx", "line": 157, "column": 71, "text": "Entrez votre mot de passe", "context": "placeholder={t('login.passwordPlaceholder', 'Entrez votre mot de passe')}}", "frenchMatches": ["de", "mot de passe"], "severity": "medium", "suggestedKey": "page.entrezvotremot"}, {"file": "src\\components\\admin\\SystemSettings.tsx", "line": 357, "column": 76, "text": "Notifications par email", "context": "<Label htmlFor=\"email\")})}>{t('settings.emailNotifications', 'Notifications par email')}<//Label>", "frenchMatches": ["par", "email"], "severity": "medium", "suggestedKey": "systemsettings.notificationsparemail"}, {"file": "src\\components\\auth\\RBACErrorBoundary.tsx", "line": 67, "column": 49, "text": "<PERSON><PERSON><PERSON> inconnue", "context": "{this.state.error?.message || 'Erreur inconnue'}", "frenchMatches": ["<PERSON><PERSON><PERSON>"], "severity": "medium", "suggestedKey": "rbacerrorboundary.erreurinconnue"}, {"file": "src\\components\\time-management\\OvertimeForm.tsx", "line": 106, "column": 46, "text": "common.date", "context": "<FormLabel>{t('common.date', 'common.date')})})})}</FormLabel>", "frenchMatches": ["date"], "severity": "medium", "suggestedKey": "overtimeform.commondate"}, {"file": "src\\components\\ui\\searchable-combobox.tsx", "line": 40, "column": 59, "text": "searchable-combobox.rechercher", "context": "searchPlaceholder = t('searchable-combobox.rechercher', 'searchable-combobox.rechercher'))),", "frenchMatches": ["rechercher"], "severity": "medium", "suggestedKey": "searchable-combobox.searchablecomboboxrechercher"}, {"file": "src\\lib\\firestore\\employees.ts", "line": 183, "column": 37, "text": "employees.email", "context": "{ field: t('employees.email', 'employees.email'))), operator: '==' as const, value: email },", "frenchMatches": ["email"], "severity": "medium", "suggestedKey": "employees.employeesemail"}, {"file": "src\\lib\\firestore\\employees.ts", "line": 201, "column": 37, "text": "employees.email", "context": "{ field: t('employees.email', 'employees.email'))), operator: '==' as const, value: email },", "frenchMatches": ["email"], "severity": "medium", "suggestedKey": "employees.employeesemail"}, {"file": "src\\lib\\firestore\\overtime.ts", "line": 145, "column": 35, "text": "overtime.date", "context": "{ field: t('overtime.date', 'overtime.date'))), operator: '>=' as const, value: startDate.toISOString() },", "frenchMatches": ["date"], "severity": "medium", "suggestedKey": "overtime.overtimedate"}, {"file": "src\\lib\\firestore\\overtime.ts", "line": 146, "column": 35, "text": "overtime.date", "context": "{ field: t('overtime.date', 'overtime.date'))), operator: '<=' as const, value: endDate.toISOString() },", "frenchMatches": ["date"], "severity": "medium", "suggestedKey": "overtime.overtimedate"}, {"file": "src\\lib\\firestore\\overtime.ts", "line": 226, "column": 35, "text": "overtime.date", "context": "{ field: t('overtime.date', 'overtime.date'))), operator: '>=' as const, value: startDate.toISOString() },", "frenchMatches": ["date"], "severity": "medium", "suggestedKey": "overtime.overtimedate"}, {"file": "src\\lib\\firestore\\overtime.ts", "line": 227, "column": 35, "text": "overtime.date", "context": "{ field: t('overtime.date', 'overtime.date'))), operator: '<=' as const, value: endDate.toISOString() },", "frenchMatches": ["date"], "severity": "medium", "suggestedKey": "overtime.overtimedate"}, {"file": "src\\lib\\firestore\\users.ts", "line": 60, "column": 33, "text": "users.email", "context": "{ field: t('users.email', 'users.email'))), operator: '==' as const, value: email },", "frenchMatches": ["email"], "severity": "medium", "suggestedKey": "users.usersemail"}, {"file": "src\\app\\login\\page.tsx", "line": 137, "column": 66, "text": "<EMAIL>", "context": "placeholder={t('login.emailPlaceholder', '<EMAIL>')}}", "frenchMatches": ["email"], "severity": "low", "suggestedKey": "page.votreemailexemplecom"}, {"file": "src\\app\\page.tsx", "line": 32, "column": 49, "text": "Vérification de votre statut...", "context": "submessage={t('loading.checkingStatus', 'Vérification de votre statut...')}", "frenchMatches": ["de", "é", "statut"], "severity": "low", "suggestedKey": "page.vrificationdevotre"}, {"file": "src\\components\\employees\\EmployeeForm.tsx", "line": 281, "column": 30, "text": "Impossible de ${isEditMode ? t('employeeform.mettrejour', 'employeeform.mettrejour'))) : t('employeeform.crer', 'employeeform.crer')))} l'employé. Veuillez réessayer.", "context": ": error.message || `Impossible de ${isEditMode ? t('employeeform.mettrejour', 'employeeform.mettrejour'))) : t('employeeform.crer', 'employeeform.crer')))} l'employé. Veuillez réessayer.`,", "frenchMatches": ["de", "é"], "severity": "low", "suggestedKey": "employeeform.impossibledeiseditmode"}, {"file": "src\\components\\time-management\\OvertimeForm.tsx", "line": 103, "column": 41, "text": "overtimeform.date", "context": "name=t('overtimeform.date', 'overtimeform.date')))", "frenchMatches": ["date"], "severity": "low", "suggestedKey": "overtimeform.overtimeformdate"}]}