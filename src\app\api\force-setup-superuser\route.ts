import { NextRequest, NextResponse } from 'next/server';
import { getAuth } from 'firebase-admin/auth';
import { getFirestore } from 'firebase-admin/firestore';
import { initializeApp, getApps, cert } from 'firebase-admin/app';

// Initialize Firebase Admin SDK
if (!getApps().length) {
  const serviceAccount = {
    type: "service_account",
    project_id: process.env.FIREBASE_PROJECT_ID,
    private_key_id: process.env.FIREBASE_PRIVATE_KEY_ID,
    private_key: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
    client_email: process.env.FIREBASE_CLIENT_EMAIL,
    client_id: process.env.FIREBASE_CLIENT_ID,
    auth_uri: "https://accounts.google.com/o/oauth2/auth",
    token_uri: "https://oauth2.googleapis.com/token",
    auth_provider_x509_cert_url: "https://www.googleapis.com/oauth2/v1/certs",
    client_x509_cert_url: `https://www.googleapis.com/robot/v1/metadata/x509/${process.env.FIREBASE_CLIENT_EMAIL}`
  };

  initializeApp({
    credential: cert(serviceAccount as any),
    projectId: process.env.FIREBASE_PROJECT_ID,
  });
}

const auth = getAuth();
const db = getFirestore();

export async function GET(request: NextRequest) {
  try {
    // Configuration forcée du superutilisateur
    const superuserUid = 'VxVTSylKjGRDkLZw7xlrCvZkKE42';
    const superuserEmail = '<EMAIL>';
    
    console.log('🔧 Force Setup: Configuring superuser:', superuserUid, superuserEmail);
    
    // Créer/mettre à jour le document utilisateur dans Firestore
    const userData = {
      id: superuserUid,
      email: superuserEmail,
      firstName: 'Super',
      lastName: 'User',
      role: 'super_admin',
      companyId: null, // Le superutilisateur n'appartient pas à une entreprise spécifique
      isActive: true,
      phoneNumber: null,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    await db.collection('users').doc(superuserUid).set(userData);
    console.log('✅ Force Setup: Created/updated Firestore user document');

    // Définir les custom claims
    const customClaims = {
      role: 'super_admin',
      companyId: null,
      isAdmin: true,
      isSuperAdmin: true,
      isPayrollManager: true,
      isViewer: false
    };

    await auth.setCustomUserClaims(superuserUid, customClaims);
    console.log('✅ Force Setup: Set custom claims');

    // Vérifier que tout est bien configuré
    const userDoc = await db.collection('users').doc(superuserUid).get();
    const userRecord = await auth.getUser(superuserUid);

    return NextResponse.json({
      success: true,
      message: 'Superuser force setup completed',
      userData,
      customClaims,
      verification: {
        firestoreDocExists: userDoc.exists,
        firestoreData: userDoc.data(),
        authUserExists: !!userRecord,
        authCustomClaims: userRecord.customClaims
      }
    });

  } catch (error) {
    console.error('❌ Force Setup: Error:', error);
    return NextResponse.json({ 
      error: 'Force setup failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
