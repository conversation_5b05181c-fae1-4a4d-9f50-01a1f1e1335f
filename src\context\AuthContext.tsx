"use client";

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { 
  User as FirebaseUser,
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  signOut as firebaseSignOut,
  onAuthStateChanged,
  sendPasswordResetEmail,
  updateProfile
} from 'firebase/auth';
import { auth } from '@/lib/firebase';
import { userService, companyService } from '@/lib/firestore';
import { DEMO_COMPANY_ID, DEMO_ADMIN_DATA } from '@/lib/demo-data';
import type { UserDocument, Company } from '@/lib/firestore/types';

interface AuthContextType {
  // Firebase Auth user
  firebaseUser: FirebaseUser | null;
  
  // WePaie user data
  user: UserDocument | null;
  company: Company | null;
  
  // Loading states
  isLoading: boolean;
  isInitialized: boolean;
  
  // Auth actions
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (email: string, password: string, userData: {
    firstName: string;
    lastName: string;
    companyName: string;
  }) => Promise<void>;
  signOut: () => Promise<void>;
  resetPassword: (email: string) => Promise<void>;
  
  // Data refresh
  refreshUserData: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [firebaseUser, setFirebaseUser] = useState<FirebaseUser | null>(null);
  const [user, setUser] = useState<UserDocument | null>(null);
  const [company, setCompany] = useState<Company | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isInitialized, setIsInitialized] = useState(false);

  // Listen to Firebase Auth state changes
  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (firebaseUser) => {
      console.log('🔐 Auth state changed:', firebaseUser?.email || 'No user');
      setIsLoading(true);
      setFirebaseUser(firebaseUser);

      try {
        if (firebaseUser) {
          await loadUserData(firebaseUser);
        } else {
          setUser(null);
          setCompany(null);
        }
      } catch (error) {
        console.error('❌ Error in auth state change:', error);
        setUser(null);
        setCompany(null);
      } finally {
        setIsLoading(false);
        setIsInitialized(true);
      }
    });

    return unsubscribe;
  }, []);

  /**
   * Load user data from Firestore based on Firebase Auth user
   */
  const loadUserData = async (firebaseUser: FirebaseUser) => {
    try {
      console.log('📊 Loading user data for:', firebaseUser.email);

      // Disabled: Special handling for demo admin user
      // Demo data creation is now disabled to prevent unauthorized data creation
      // if (firebaseUser.email === '<EMAIL>') {
      //   await handleDemoAdminUser(firebaseUser);
      //   return;
      // }

      // For other users, try to find their company and user data
      // This would be implemented based on your user onboarding flow
      console.log('⚠️ Non-demo user authentication not yet implemented');

      // For now, set empty state for non-demo users
      setUser(null);
      setCompany(null);

    } catch (error) {
      console.error('❌ Error loading user data:', error);
      setUser(null);
      setCompany(null);
    }
  };

  /**
   * Handle demo admin user authentication
   */
  const handleDemoAdminUser = async (firebaseUser: FirebaseUser) => {
    try {
      console.log('👤 Handling demo admin user...', {
        uid: firebaseUser.uid,
        email: firebaseUser.email,
        displayName: firebaseUser.displayName
      });

      // Load demo company
      let demoCompany: Company;
      try {
        console.log('🔍 Looking for existing demo company...');
        demoCompany = await companyService.getById(DEMO_COMPANY_ID);
        console.log('✅ Found existing demo company:', demoCompany.name);
      } catch (error: any) {
        console.log('📝 Demo company not found, creating...', error.code);

        // Create demo company directly using the service
        try {
          demoCompany = await companyService.create({
            name: 'WePaie Demo Company',
            email: '<EMAIL>',
            phone: '+****************',
            address: '123 Demo Street, Demo City, DC 12345',
            settings: companyService.createDefaultSettings(),
            subscription: companyService.createDefaultSubscription(),
          }, DEMO_COMPANY_ID);
          console.log('✅ Created demo company:', demoCompany.name);
        } catch (createError: any) {
          console.error('❌ Failed to create demo company:', createError);
          throw new Error(`Failed to create demo company: ${createError.message}`);
        }
      }

      // Load or create demo admin user
      let demoUser: UserDocument;
      try {
        console.log('🔍 Looking for existing demo user...');
        demoUser = await userService.getById(firebaseUser.uid, demoCompany.id);
        console.log('✅ Found existing demo user:', demoUser.email);
      } catch (error: any) {
        console.log('👨‍💼 Demo user not found, creating...', error.code);

        try {
          // Create demo user with Firebase Auth UID
          demoUser = await userService.createUser({
            ...DEMO_ADMIN_DATA,
            name: firebaseUser.displayName || DEMO_ADMIN_DATA.name,
            email: firebaseUser.email || DEMO_ADMIN_DATA.email,
          }, demoCompany.id, firebaseUser.uid);
          console.log('✅ Created demo user:', demoUser.email);
        } catch (createError: any) {
          console.error('❌ Failed to create demo user:', createError);
          throw new Error(`Failed to create demo user: ${createError.message}`);
        }
      }

      setCompany(demoCompany);
      setUser(demoUser);

      console.log('🎉 Demo admin user setup complete!', {
        company: demoCompany.name,
        user: demoUser.email,
        role: demoUser.role
      });

    } catch (error: any) {
      console.error('❌ Error setting up demo admin user:', error);
      // Don't throw here, let the user see the error but continue
      setUser(null);
      setCompany(null);
    }
  };

  /**
   * Sign in with email and password
   */
  const signIn = async (email: string, password: string) => {
    setIsLoading(true);
    try {
      console.log('🔑 Signing in user:', email);
      const result = await signInWithEmailAndPassword(auth, email, password);
      console.log('✅ Sign in successful');
      // User data will be loaded automatically by the auth state listener
    } catch (error: any) {
      console.error('❌ Sign in failed:', error);
      throw new Error(getAuthErrorMessage(error.code));
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Sign up with email and password
   */
  const signUp = async (
    email: string, 
    password: string, 
    userData: {
      firstName: string;
      lastName: string;
      companyName: string;
    }
  ) => {
    setIsLoading(true);
    try {
      console.log('📝 Creating new user:', email);
      
      // Create Firebase Auth user
      const result = await createUserWithEmailAndPassword(auth, email, password);
      
      // Update display name
      await updateProfile(result.user, {
        displayName: `${userData.firstName} ${userData.lastName}`
      });
      
      // Create company and user data in Firestore
      // This would be implemented based on your onboarding flow
      console.log('⚠️ Company creation for new users not yet implemented');
      
    } catch (error: any) {
      console.error('❌ Sign up failed:', error);
      throw new Error(getAuthErrorMessage(error.code));
    } finally {
      setIsLoading(false);
    }
  };

  /**
   * Sign out
   */
  const signOut = async () => {
    try {
      console.log('👋 AuthContext: Starting sign out process');

      // Clear local state first to prevent any Firestore operations
      setUser(null);
      setCompany(null);

      // Then sign out from Firebase Auth
      await firebaseSignOut(auth);
      console.log('✅ AuthContext: Sign out successful');
    } catch (error: any) {
      console.error('❌ AuthContext: Sign out failed:', error);

      // Even if Firebase signOut fails, clear local state
      setUser(null);
      setCompany(null);

      // Don't throw the error to prevent UI issues
      console.log('🔄 AuthContext: Cleared local state despite error');
    }
  };

  /**
   * Reset password
   */
  const resetPassword = async (email: string) => {
    try {
      console.log('🔄 Sending password reset email to:', email);
      await sendPasswordResetEmail(auth, email);
      console.log('✅ Password reset email sent');
    } catch (error: any) {
      console.error('❌ Password reset failed:', error);
      throw new Error(getAuthErrorMessage(error.code));
    }
  };

  /**
   * Refresh user data
   */
  const refreshUserData = async () => {
    if (firebaseUser) {
      await loadUserData(firebaseUser);
    }
  };

  const value: AuthContextType = {
    firebaseUser,
    user,
    company,
    isLoading,
    isInitialized,
    signIn,
    signUp,
    signOut,
    resetPassword,
    refreshUserData,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

/**
 * Convert Firebase Auth error codes to user-friendly messages
 */
function getAuthErrorMessage(errorCode: string): string {
  switch (errorCode) {
    case 'auth/user-not-found':
      return 'No account found with this email address.';
    case 'auth/wrong-password':
      return 'Incorrect password.';
    case 'auth/email-already-in-use':
      return 'An account with this email already exists.';
    case 'auth/weak-password':
      return 'Password should be at least 6 characters.';
    case 'auth/invalid-email':
      return 'Invalid email address.';
    case 'auth/too-many-requests':
      return 'Too many failed attempts. Please try again later.';
    case 'auth/network-request-failed':
      return 'Network error. Please check your connection.';
    default:
      return 'An error occurred. Please try again.';
  }
}
