// Centralisation de tous les thèmes
import { defaultLight } from './default/light';
import { defaultDark } from './default/dark';
import { natureLight } from './nature/light';
import { natureDark } from './nature/dark';
import { corporateLight } from './corporate/light';
import { corporateDark } from './corporate/dark';
import { warmLight } from './warm/light';
import { warmDark } from './warm/dark';

export const themes = {
  default: {
    light: defaultLight,
    dark: defaultDark,
  },
  nature: {
    light: natureLight,
    dark: natureDark,
  },
  corporate: {
    light: corporateLight,
    dark: corporateDark,
  },
  warm: {
    light: warmLight,
    dark: warmDark,
  },
};

// Types pour TypeScript (définis dans le fichier .ts)
export type ThemeName = keyof typeof themes;
export type ThemeMode = 'light' | 'dark';

// Fonction utilitaire pour obtenir un thème
export const getTheme = (themeName: ThemeName, mode: ThemeMode) => {
  return themes[themeName]?.[mode] || themes.default.light;
};

// Liste des noms de thèmes pour les sélecteurs
export const themeNames = Object.keys(themes) as ThemeName[];

// Métadonnées des thèmes pour l'interface utilisateur
export const themeMetadata = {
  default: {
    name: 'Défaut',
    description: 'Thème bleu professionnel',
    color: '#3498db',
  },
  nature: {
    name: 'Nature',
    description: 'Thème vert inspiré de la nature',
    color: '#6D9773',
  },
  corporate: {
    name: 'Corporate',
    description: 'Thème bleu-gris professionnel',
    color: '#114377',
  },
  warm: {
    name: 'Chaleureux',
    description: 'Thème brun et doré chaleureux',
    color: '#B46617',
  },
};
