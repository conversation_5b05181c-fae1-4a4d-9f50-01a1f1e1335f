export type Employee = {
  id: string;
  firstName: string;
  lastName: string;
  phoneNumber?: string;
  address?: string;
  service?: string;
  position: string;
  hourlyRate?: number; // Made optional as weeklySalary could be primary
  hoursPerWeek?: number;
  weeklySalary?: number;
  overtimeRate?: number;
  status: 'Active' | 'Inactive';
  hireDate: string; // ISO Date string
};

export type AbsenceCategory = 'Sick Leave' | 'Annual Leave' | 'Unpaid Leave' | 'Other';

export type Absence = {
  id: string;
  employeeId: string;
  category: AbsenceCategory;
  startDate: string; // ISO Date string
  endDate: string; // ISO Date string
  notes?: string;
};

export type OvertimeCategory = 'Regular Overtime' | 'Holiday Overtime' | 'Special Project';

export type Overtime = {
  id: string;
  employeeId: string;
  date: string; // ISO Date string
  hours: number;
  category: OvertimeCategory;
  reason?: string;
};

export type UserRole = 'admin' | 'payroll_manager';

export type User = {
  id: string;
  name: string;
  email: string;
  role: UserRole;
  companyId: string;
};
