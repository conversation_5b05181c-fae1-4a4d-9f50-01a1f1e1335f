import { BaseFirestoreService } from './base';
import type { Department, Service, Position, QueryOptions } from './types';

/**
 * Helper function to remove undefined values from an object
 */
function cleanUndefinedValues<T extends Record<string, any>>(obj: T): Partial<T> {
  const cleaned: Partial<T> = {};
  for (const [key, value] of Object.entries(obj)) {
    if (value !== undefined) {
      cleaned[key as keyof T] = value;
    }
  }
  return cleaned;
}

/**
 * Department service for managing company departments
 */
export class DepartmentService extends BaseFirestoreService<Department> {
  constructor() {
    super('departments');
  }

  /**
   * Create new department
   */
  async createDepartment(
    departmentData: Omit<Department, 'id' | 'createdAt' | 'updatedAt' | 'companyId'>,
    companyId: string
  ): Promise<Department> {
    const data = cleanUndefinedValues({
      ...departmentData,
      isActive: true,
      order: await this.getNextOrder(companyId),
    });

    return this.create(data as any, companyId);
  }

  /**
   * Get all active departments for a company
   */
  async getActiveDepartments(companyId: string): Promise<Department[]> {
    const whereClause = [
      { field: 'isActive', operator: '==' as const, value: true }
    ];

    return this.getAll(companyId, { 
      where: whereClause,
      orderBy: { field: 'order', direction: 'asc' }
    });
  }

  /**
   * Get next order number for departments
   */
  private async getNextOrder(companyId: string): Promise<number> {
    const departments = await this.getAll(companyId, {
      orderBy: { field: 'order', direction: 'desc' },
      limit: 1
    });

    return departments.length > 0 ? departments[0].order + 1 : 1;
  }

  /**
   * Update department order
   */
  async updateOrder(id: string, newOrder: number, companyId: string): Promise<Department> {
    return this.update(id, { order: newOrder } as any, companyId);
  }

  /**
   * Soft delete department (deactivate)
   */
  async deactivateDepartment(id: string, companyId: string): Promise<Department> {
    return this.update(id, { isActive: false } as any, companyId);
  }
}

/**
 * Service service for managing company services
 */
export class ServiceService extends BaseFirestoreService<Service> {
  constructor() {
    super('services');
  }

  /**
   * Create new service
   */
  async createService(
    serviceData: Omit<Service, 'id' | 'createdAt' | 'updatedAt' | 'companyId'>,
    companyId: string
  ): Promise<Service> {
    const data = cleanUndefinedValues({
      ...serviceData,
      isActive: true,
      order: await this.getNextOrder(companyId),
    });

    return this.create(data as any, companyId);
  }

  /**
   * Get all active services for a department
   */
  async getServicesByDepartment(companyId: string, departmentId: string): Promise<Service[]> {
    const whereClause = [
      { field: 'isActive', operator: '==' as const, value: true },
      { field: 'departmentId', operator: '==' as const, value: departmentId }
    ];

    return this.getAll(companyId, { 
      where: whereClause,
      orderBy: { field: 'order', direction: 'asc' }
    });
  }

  /**
   * Get all active services for a company
   */
  async getActiveServices(companyId: string): Promise<Service[]> {
    const whereClause = [
      { field: 'isActive', operator: '==' as const, value: true }
    ];

    return this.getAll(companyId, { 
      where: whereClause,
      orderBy: { field: 'order', direction: 'asc' }
    });
  }

  /**
   * Get next order number for services
   */
  private async getNextOrder(companyId: string, departmentId?: string): Promise<number> {
    const services = departmentId
      ? await this.getServicesByDepartment(companyId, departmentId)
      : await this.getActiveServices(companyId);
    return services.length > 0 ? Math.max(...services.map(s => s.order)) + 1 : 1;
  }

  /**
   * Soft delete service (deactivate)
   */
  async deactivateService(id: string, companyId: string): Promise<Service> {
    return this.update(id, { isActive: false } as any, companyId);
  }
}

/**
 * Position service for managing company positions
 */
export class PositionService extends BaseFirestoreService<Position> {
  constructor() {
    super('positions');
  }

  /**
   * Create new position
   */
  async createPosition(
    positionData: Omit<Position, 'id' | 'createdAt' | 'updatedAt' | 'companyId'>,
    companyId: string
  ): Promise<Position> {
    const data = cleanUndefinedValues({
      ...positionData,
      isActive: true,
      order: await this.getNextOrder(companyId, undefined, positionData.serviceId),
    });

    return this.create(data as any, companyId);
  }

  /**
   * Get all active positions for a service
   */
  async getPositionsByService(companyId: string, serviceId: string): Promise<Position[]> {
    const whereClause = [
      { field: 'isActive', operator: '==' as const, value: true },
      { field: 'serviceId', operator: '==' as const, value: serviceId }
    ];

    return this.getAll(companyId, { 
      where: whereClause,
      orderBy: { field: 'order', direction: 'asc' }
    });
  }

  /**
   * Get all active positions for a department
   */
  async getPositionsByDepartment(companyId: string, departmentId: string): Promise<Position[]> {
    const whereClause = [
      { field: 'isActive', operator: '==' as const, value: true },
      { field: 'departmentId', operator: '==' as const, value: departmentId }
    ];

    return this.getAll(companyId, { 
      where: whereClause,
      orderBy: { field: 'order', direction: 'asc' }
    });
  }

  /**
   * Get all active positions for a company
   */
  async getActivePositions(companyId: string): Promise<Position[]> {
    const whereClause = [
      { field: 'isActive', operator: '==' as const, value: true }
    ];

    return this.getAll(companyId, { 
      where: whereClause,
      orderBy: { field: 'order', direction: 'asc' }
    });
  }

  /**
   * Get next order number for positions
   */
  private async getNextOrder(companyId: string, departmentId?: string, serviceId?: string): Promise<number> {
    let positions: Position[];

    if (serviceId) {
      positions = await this.getPositionsByService(companyId, serviceId);
    } else if (departmentId) {
      positions = await this.getPositionsByDepartment(companyId, departmentId);
    } else {
      positions = await this.getActivePositions(companyId);
    }

    return positions.length > 0 ? Math.max(...positions.map(p => p.order)) + 1 : 1;
  }

  /**
   * Soft delete position (deactivate)
   */
  async deactivatePosition(id: string, companyId: string): Promise<Position> {
    return this.update(id, { isActive: false } as any, companyId);
  }
}

// Export service instances
export const departmentService = new DepartmentService();
export const serviceService = new ServiceService();
export const positionService = new PositionService();
