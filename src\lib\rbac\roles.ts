/**
 * Role-Based Access Control (RBAC) Role Definitions
 * 
 * Defines the complete role hierarchy, permissions, and access control
 * for the WePaie multi-tenant system.
 */

import type { RoleConfig, Permission, UserRole, PageAccess } from './types';

// Complete permission definitions for each role
export const ROLE_PERMISSIONS: Record<UserRole, Permission[]> = {
  super_admin: [
    // Full access to everything
    'users.create', 'users.read', 'users.update', 'users.delete', 'users.assign_roles',
    'companies.create', 'companies.read', 'companies.update', 'companies.delete', 'companies.manage_settings',
    'employees.create', 'employees.read', 'employees.update', 'employees.delete',
    'time.create', 'time.read', 'time.update', 'time.delete',
    'payroll.create', 'payroll.read', 'payroll.update', 'payroll.delete', 'payroll.calculate',
    'settings.read', 'settings.update', 'settings.system',
    'audit.read', 'audit.export',
    'analytics.read', 'analytics.export', 'analytics.global'
  ],
  
  company_admin: [
    // User management within company
    'users.create', 'users.read', 'users.update', 'users.delete', 'users.assign_roles',
    // Company management (own company only)
    'companies.read', 'companies.update', 'companies.manage_settings',
    // Full business data access
    'employees.create', 'employees.read', 'employees.update', 'employees.delete',
    'time.create', 'time.read', 'time.update', 'time.delete',
    'payroll.create', 'payroll.read', 'payroll.update', 'payroll.delete', 'payroll.calculate',
    // Settings and audit
    'settings.read', 'settings.update',
    'audit.read', 'audit.export',
    'analytics.read', 'analytics.export'
  ],
  
  editor: [
    // Business data management only
    'employees.create', 'employees.read', 'employees.update', 'employees.delete',
    'time.create', 'time.read', 'time.update', 'time.delete',
    'payroll.create', 'payroll.read', 'payroll.update', 'payroll.delete', 'payroll.calculate',
    // Limited settings access
    'settings.read',
    'analytics.read'
  ],
  
  viewer: [
    // Read-only access to business data
    'employees.read',
    'time.read',
    'payroll.read',
    'settings.read',
    'analytics.read'
  ]
};

// Role configuration with metadata
export const ROLE_CONFIGS: Record<UserRole, RoleConfig> = {
  super_admin: {
    role: 'super_admin',
    name: 'Super Administrator',
    nameFr: 'Super Administrateur',
    description: 'Full system access with global company management',
    descriptionFr: 'Accès complet au système avec gestion globale des entreprises',
    permissions: ROLE_PERMISSIONS.super_admin,
    canManageRoles: ['company_admin', 'editor', 'viewer'],
    dataScope: 'global'
  },
  
  company_admin: {
    role: 'company_admin',
    name: 'Company Administrator',
    nameFr: 'Administrateur d\'Entreprise',
    description: 'Full company management and user administration',
    descriptionFr: 'Gestion complète de l\'entreprise et administration des utilisateurs',
    permissions: ROLE_PERMISSIONS.company_admin,
    canManageRoles: ['editor', 'viewer'],
    dataScope: 'company'
  },
  
  editor: {
    role: 'editor',
    name: 'Editor (Payroll Manager)',
    nameFr: 'Éditeur (Gestionnaire de Paie)',
    description: 'Full access to business data and operations',
    descriptionFr: 'Accès complet aux données et opérations métier',
    permissions: ROLE_PERMISSIONS.editor,
    dataScope: 'company'
  },
  
  viewer: {
    role: 'viewer',
    name: 'Viewer (Read-Only)',
    nameFr: 'Observateur (Lecture Seule)',
    description: 'Read-only access to business data',
    descriptionFr: 'Accès en lecture seule aux données métier',
    permissions: ROLE_PERMISSIONS.viewer,
    dataScope: 'company'
  }
};

// Page access configuration
export const PAGE_ACCESS: PageAccess[] = [
  // Dashboard - accessible to all authenticated users
  {
    path: '/dashboard',
    allowedRoles: ['super_admin', 'company_admin', 'editor', 'viewer']
  },
  
  // Employees - role-based access
  {
    path: '/employees',
    allowedRoles: ['super_admin', 'company_admin', 'editor', 'viewer'],
    requiredPermissions: ['employees.read']
  },
  
  // Time Management - role-based access
  {
    path: '/time-management',
    allowedRoles: ['super_admin', 'company_admin', 'editor', 'viewer'],
    requiredPermissions: ['time.read']
  },
  
  // Admin - restricted to administrators
  {
    path: '/admin',
    allowedRoles: ['super_admin', 'company_admin'],
    requiredPermissions: ['users.read']
  },
  
  // Super Admin - super admin only
  {
    path: '/super-admin',
    requiredRole: 'super_admin',
    requiredPermissions: ['companies.create']
  },
  
  // Settings - personal settings only
  {
    path: '/settings',
    allowedRoles: ['super_admin', 'company_admin', 'editor', 'viewer'],
    requiredPermissions: ['settings.read']
  }
];

// Navigation items with role requirements
export const RBAC_NAV_ITEMS = [
  {
    href: '/dashboard',
    labelKey: 'nav.dashboard',
    icon: 'LayoutDashboard',
    matchExact: true,
    allowedRoles: ['super_admin', 'company_admin', 'editor', 'viewer']
  },
  {
    href: '/employees',
    labelKey: 'nav.employees',
    icon: 'Users',
    allowedRoles: ['super_admin', 'company_admin', 'editor', 'viewer'],
    requiredPermissions: ['employees.read']
  },
  {
    href: '/time-management',
    labelKey: 'nav.timeManagement',
    icon: 'Clock3',
    allowedRoles: ['super_admin', 'company_admin', 'editor', 'viewer'],
    requiredPermissions: ['time.read']
  },
  {
    href: '/admin',
    labelKey: 'nav.admin',
    icon: 'Shield',
    allowedRoles: ['super_admin', 'company_admin'],
    requiredPermissions: ['users.read']
  },
  {
    href: '/super-admin',
    labelKey: 'nav.superAdmin',
    icon: 'Crown',
    requiredRole: 'super_admin',
    requiredPermissions: ['companies.create']
  },
  {
    href: '/settings',
    labelKey: 'nav.settings',
    icon: 'Settings',
    allowedRoles: ['super_admin', 'company_admin', 'editor', 'viewer']
  }
];

// Helper functions for role management
export function getRoleConfig(role: UserRole): RoleConfig {
  return ROLE_CONFIGS[role];
}

export function getRolePermissions(role: UserRole): Permission[] {
  return ROLE_PERMISSIONS[role];
}

export function canManageRole(managerRole: UserRole, targetRole: UserRole): boolean {
  const config = ROLE_CONFIGS[managerRole];
  return config.canManageRoles?.includes(targetRole) ?? false;
}

export function isHigherRole(role1: UserRole, role2: UserRole): boolean {
  const hierarchy: UserRole[] = ['viewer', 'editor', 'company_admin', 'super_admin'];
  return hierarchy.indexOf(role1) > hierarchy.indexOf(role2);
}

export function getAvailableRoles(currentUserRole: UserRole): UserRole[] {
  const config = ROLE_CONFIGS[currentUserRole];
  return config.canManageRoles || [];
}

// French role names for UI
export const ROLE_NAMES_FR: Record<UserRole, string> = {
  super_admin: 'Super Administrateur',
  company_admin: 'Administrateur d\'Entreprise',
  editor: 'Éditeur',
  viewer: 'Observateur'
};

// Role descriptions in French
export const ROLE_DESCRIPTIONS_FR: Record<UserRole, string> = {
  super_admin: 'Accès complet au système avec gestion globale des entreprises',
  company_admin: 'Gestion complète de l\'entreprise et administration des utilisateurs',
  editor: 'Accès complet aux données et opérations métier',
  viewer: 'Accès en lecture seule aux données métier'
};

// Permission names in French for UI
export const PERMISSION_NAMES_FR: Record<string, string> = {
  'users.create': 'Créer des utilisateurs',
  'users.read': 'Voir les utilisateurs',
  'users.update': 'Modifier les utilisateurs',
  'users.delete': 'Supprimer les utilisateurs',
  'users.assign_roles': 'Assigner des rôles',

  'companies.create': 'Créer des entreprises',
  'companies.read': 'Voir les entreprises',
  'companies.update': 'Modifier les entreprises',
  'companies.delete': 'Supprimer les entreprises',
  'companies.manage_settings': 'Gérer les paramètres d\'entreprise',

  'employees.create': 'Créer des employés',
  'employees.read': 'Voir les employés',
  'employees.update': 'Modifier les employés',
  'employees.delete': 'Supprimer les employés',

  'time.create': 'Créer des données de temps',
  'time.read': 'Voir les données de temps',
  'time.update': 'Modifier les données de temps',
  'time.delete': 'Supprimer les données de temps',

  'payroll.create': 'Créer des données de paie',
  'payroll.read': 'Voir les données de paie',
  'payroll.update': 'Modifier les données de paie',
  'payroll.delete': 'Supprimer les données de paie',
  'payroll.calculate': 'Calculer la paie',

  'settings.read': 'Voir les paramètres',
  'settings.update': 'Modifier les paramètres',
  'settings.system': 'Paramètres système',

  'audit.read': 'Voir les journaux d\'audit',
  'audit.export': 'Exporter les journaux d\'audit',

  'analytics.read': 'Voir les analyses',
  'analytics.export': 'Exporter les analyses',
  'analytics.global': 'Analyses globales'
};
