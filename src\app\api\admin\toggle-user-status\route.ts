import { NextRequest, NextResponse } from 'next/server';
import { getAuth } from 'firebase-admin/auth';
import { getFirestore } from 'firebase-admin/firestore';
import { initializeApp, getApps, cert } from 'firebase-admin/app';

// Initialize Firebase Admin SDK
if (!getApps().length) {
  const serviceAccount = {
    type: 'service_account',
    project_id: process.env.FIREBASE_PROJECT_ID,
    private_key_id: process.env.FIREBASE_PRIVATE_KEY_ID,
    private_key: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
    client_email: process.env.FIREBASE_CLIENT_EMAIL,
    client_id: process.env.FIREBASE_CLIENT_ID,
    auth_uri: 'https://accounts.google.com/o/oauth2/auth',
    token_uri: 'https://oauth2.googleapis.com/token',
    auth_provider_x509_cert_url: 'https://www.googleapis.com/oauth2/v1/certs',
    client_x509_cert_url: `https://www.googleapis.com/robot/v1/metadata/x509/${process.env.FIREBASE_CLIENT_EMAIL}`,
  };

  initializeApp({
    credential: cert(serviceAccount as any),
    projectId: process.env.FIREBASE_PROJECT_ID,
  });
}

const auth = getAuth();
const db = getFirestore();

export interface ToggleUserStatusRequest {
  userId: string;
  companyId: string;
  isActive: boolean;
}

export async function PATCH(request: NextRequest) {
  try {
    // Verify the request is from an authenticated admin user
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const idToken = authHeader.split('Bearer ')[1];
    
    // Verify the ID token
    const decodedToken = await auth.verifyIdToken(idToken);

    console.log('🔍 Toggle User Status API: Decoded token claims:', {
      uid: decodedToken.uid,
      email: decodedToken.email,
      isAdmin: decodedToken.isAdmin,
      isSuperAdmin: decodedToken.isSuperAdmin,
      role: decodedToken.role
    });

    // Check if user has admin permissions
    const hasAdminPermission = decodedToken.isAdmin === true || 
                              decodedToken.isSuperAdmin === true ||
                              decodedToken.role === 'super_admin' ||
                              decodedToken.role === 'company_admin';

    // Double-check with Firestore document if custom claims are not set
    if (!hasAdminPermission) {
      try {
        const userDoc = await db.collection('users').doc(decodedToken.uid).get();
        if (userDoc.exists) {
          const userData = userDoc.data();
          const isFirestoreAdmin = userData?.role === 'super_admin' || 
                                  userData?.role === 'company_admin';
          if (!isFirestoreAdmin) {
            console.log('❌ Toggle User Status API: User lacks admin privileges');
            return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
          }
        } else {
          console.log('❌ Toggle User Status API: User document not found');
          return NextResponse.json({ error: 'User not found' }, { status: 403 });
        }
      } catch (error) {
        console.error('❌ Toggle User Status API: Error checking user permissions:', error);
        return NextResponse.json({ error: 'Permission check failed' }, { status: 500 });
      }
    }

    const body: ToggleUserStatusRequest = await request.json();
    const { userId, companyId, isActive } = body;

    // Validate required fields
    if (!userId || !companyId || typeof isActive !== 'boolean') {
      return NextResponse.json({ error: 'Missing required fields: userId, companyId, and isActive' }, { status: 400 });
    }

    // Prevent deactivation of the current user
    if (userId === decodedToken.uid && !isActive) {
      return NextResponse.json({ error: 'Cannot deactivate your own account' }, { status: 400 });
    }

    // Get user data for validation
    const userDoc = await db.collection('users').doc(userId).get();
    if (!userDoc.exists) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    const userData = userDoc.data();
    
    // Prevent deactivation of superuser accounts (unless current user is superuser)
    if (userData?.role === 'super_admin' && decodedToken.role !== 'super_admin' && !isActive) {
      return NextResponse.json({ error: 'Only superuser can deactivate superuser accounts' }, { status: 403 });
    }

    // Company admin can only manage users from their own company
    if (decodedToken.role === 'company_admin' && userData?.companyId !== decodedToken.companyId) {
      return NextResponse.json({ error: 'Cannot manage users from other companies' }, { status: 403 });
    }

    console.log('🔄 Toggle User Status API: Updating user status:', userId, isActive ? 'activate' : 'deactivate');

    // Update user status in Firestore
    await db.collection('users').doc(userId).update({
      isActive: isActive,
      updatedAt: new Date()
    });

    console.log('✅ Toggle User Status API: Updated user status successfully');

    // If deactivating user, also disable their Firebase Auth account
    if (!isActive) {
      try {
        await auth.updateUser(userId, {
          disabled: true
        });
        console.log('✅ Toggle User Status API: Disabled Firebase Auth account');
      } catch (authError: any) {
        // If user doesn't exist in Auth, continue
        if (authError.code !== 'auth/user-not-found') {
          console.error('❌ Toggle User Status API: Error disabling Firebase Auth:', authError);
          // Don't fail the request, just log the error
        }
      }
    } else {
      // If activating user, enable their Firebase Auth account
      try {
        await auth.updateUser(userId, {
          disabled: false
        });
        console.log('✅ Toggle User Status API: Enabled Firebase Auth account');
      } catch (authError: any) {
        // If user doesn't exist in Auth, continue
        if (authError.code !== 'auth/user-not-found') {
          console.error('❌ Toggle User Status API: Error enabling Firebase Auth:', authError);
          // Don't fail the request, just log the error
        }
      }
    }

    return NextResponse.json({
      success: true,
      message: `User ${isActive ? 'activated' : 'deactivated'} successfully`,
      user: {
        id: userId,
        email: userData?.email,
        firstName: userData?.firstName,
        lastName: userData?.lastName,
        isActive: isActive
      }
    });

  } catch (error) {
    console.error('❌ Toggle User Status API: Error:', error);
    
    return NextResponse.json({ 
      error: 'Erreur lors de la modification du statut utilisateur',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
