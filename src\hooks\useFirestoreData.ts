"use client";

import { useState, useEffect, useCallback } from 'react';
import { useCompanyId } from '@/context/FirestoreContext';
import type { QueryOptions, PaginatedResponse } from '@/lib/firestore/types';

interface UseFirestoreDataOptions<T> {
  service: {
    getAll: (companyId: string, options?: QueryOptions) => Promise<T[]>;
    getPaginated?: (companyId: string, options?: QueryOptions) => Promise<PaginatedResponse<T>>;
  };
  queryOptions?: QueryOptions;
  enabled?: boolean;
  refetchOnMount?: boolean;
  paginated?: boolean;
}

interface UseFirestoreDataResult<T> {
  data: T[];
  isLoading: boolean;
  error: Error | null;
  refetch: () => Promise<void>;
  hasMore?: boolean;
  loadMore?: () => Promise<void>;
  total?: number;
}

/**
 * Custom hook for fetching Firestore data with React state management
 */
export function useFirestoreData<T>({
  service,
  queryOptions = {},
  enabled = true,
  refetchOnMount = true,
  paginated = false,
}: UseFirestoreDataOptions<T>): UseFirestoreDataResult<T> {
  const companyId = useCompanyId();
  const [data, setData] = useState<T[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [hasMore, setHasMore] = useState(false);
  const [lastDoc, setLastDoc] = useState<any>(null);
  const [total, setTotal] = useState<number | undefined>(undefined);

  const fetchData = useCallback(async (reset = true) => {
    if (!companyId || !enabled) return;

    setIsLoading(true);
    setError(null);

    try {
      const options = reset ? queryOptions : { ...queryOptions, startAfter: lastDoc };

      if (paginated && service.getPaginated) {
        const result = await service.getPaginated(companyId, options);
        
        if (reset) {
          setData(result.data);
        } else {
          setData(prev => [...prev, ...result.data]);
        }
        
        setHasMore(result.hasMore);
        setLastDoc(result.lastDoc);
        setTotal(result.total);
      } else {
        const result = await service.getAll(companyId, options);
        
        if (reset) {
          setData(result);
        } else {
          setData(prev => [...prev, ...result]);
        }
        
        setHasMore(false);
        setTotal(result.length);
      }
    } catch (err) {
      setError(err as Error);
      console.error('Error fetching Firestore data:', err);
    } finally {
      setIsLoading(false);
    }
  }, [companyId, enabled, service, queryOptions, paginated, lastDoc]);

  const refetch = useCallback(() => fetchData(true), [fetchData]);

  const loadMore = useCallback(async () => {
    if (!hasMore || isLoading) return;
    await fetchData(false);
  }, [hasMore, isLoading, fetchData]);

  // Initial fetch
  useEffect(() => {
    if (refetchOnMount) {
      fetchData(true);
    }
  }, [fetchData, refetchOnMount]);

  return {
    data,
    isLoading,
    error,
    refetch,
    hasMore: paginated ? hasMore : undefined,
    loadMore: paginated ? loadMore : undefined,
    total,
  };
}

/**
 * Hook for fetching a single document by ID
 */
export function useFirestoreDocument<T>(
  service: {
    getById: (id: string, companyId: string) => Promise<T | null>;
  },
  id: string | null,
  enabled = true
) {
  const companyId = useCompanyId();
  const [data, setData] = useState<T | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const fetchDocument = useCallback(async () => {
    if (!companyId || !id || !enabled) return;

    setIsLoading(true);
    setError(null);

    try {
      const result = await service.getById(id, companyId);
      setData(result);
    } catch (err) {
      setError(err as Error);
      console.error('Error fetching Firestore document:', err);
    } finally {
      setIsLoading(false);
    }
  }, [companyId, id, enabled, service]);

  const refetch = useCallback(() => fetchDocument(), [fetchDocument]);

  useEffect(() => {
    fetchDocument();
  }, [fetchDocument]);

  return {
    data,
    isLoading,
    error,
    refetch,
  };
}

/**
 * Hook for creating/updating documents with optimistic updates
 */
export function useFirestoreMutation<T, TInput = Partial<T>>(
  service: {
    create?: (data: TInput, companyId: string) => Promise<T>;
    update?: (id: string, data: TInput, companyId: string) => Promise<T>;
    delete?: (id: string, companyId: string) => Promise<void>;
  }
) {
  const companyId = useCompanyId();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const create = useCallback(async (data: TInput): Promise<T | null> => {
    if (!companyId || !service.create) return null;

    setIsLoading(true);
    setError(null);

    try {
      const result = await service.create(data, companyId);
      return result;
    } catch (err) {
      setError(err as Error);
      console.error('Error creating document:', err);
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [companyId, service]);

  const update = useCallback(async (id: string, data: TInput): Promise<T | null> => {
    if (!companyId || !service.update) return null;

    setIsLoading(true);
    setError(null);

    try {
      const result = await service.update(id, data, companyId);
      return result;
    } catch (err) {
      setError(err as Error);
      console.error('Error updating document:', err);
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [companyId, service]);

  const remove = useCallback(async (id: string): Promise<boolean> => {
    if (!companyId || !service.delete) return false;

    setIsLoading(true);
    setError(null);

    try {
      await service.delete(id, companyId);
      return true;
    } catch (err) {
      setError(err as Error);
      console.error('Error deleting document:', err);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [companyId, service]);

  return {
    create,
    update,
    delete: remove,
    isLoading,
    error,
  };
}
