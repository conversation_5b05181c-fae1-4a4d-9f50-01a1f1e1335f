"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import type { AbsenceFormData } from "@/lib/schemas";
import { absenceSchema } from "@/lib/schemas";
import type { Employee } from "@/types";
import { useTranslation } from "@/context/i18nContext";


import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { Textarea } from "@/components/ui/textarea";
import { CalendarIcon } from "lucide-react"; // Removed Users icon as it's not used
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import { useToast } from "@/hooks/use-toast";

// Mock data for employees to pass to forms - updated with new Employee structure
const mockEmployeesData: Employee[] = [
  { id: '1', firstName: 'Alice', lastName: 'Wonderland', email: '<EMAIL>', department: 'Engineering', service: 'Frontend', position: 'Software Developer', hourlyRate: 50, hoursPerWeek: 40, overtimeRate: 75, weeklySalary: 2000, status: 'Active', hireDate: '2022-01-15', phoneNumber: '555-0101', address: '123 Rabbit Hole Lane' },
  { id: '2', firstName: 'Bob', lastName: 'The Builder', email: '<EMAIL>', department: 'Operations', service: 'Construction', position: 'Manager', hourlyRate: 60, hoursPerWeek: 40, overtimeRate: 90, weeklySalary: 2400, status: 'Active', hireDate: '2021-03-10', phoneNumber: '555-0102', address: '456 Fixit Ave' },
];


interface AbsenceFormProps {
  employees: Employee[]; 
  onSuccess?: () => void;
}

export function AbsenceForm({ employees = mockEmployeesData, onSuccess }: AbsenceFormProps) {
  const { toast } = useToast();
  const { t } = useTranslation();
  const form = useForm<AbsenceFormData>({
    resolver: zodResolver(absenceSchema),
    defaultValues: {
      employeeIds: [],
      category: "Sick Leave",
      startDate: new Date(),
      endDate: new Date(),
      notes: "",
    },
  });

  const employeeOptions = employees.map(emp => ({
    value: emp.id,
    label: `${emp.firstName} ${emp.lastName}`
  }));

  async function onSubmit(data: AbsenceFormData) {
    console.log("Absence data:", data);
    toast({ title: t('absenceForm.toast.loggedTitle', "Absence Logged"), description: t('absenceForm.toast.loggedDescription', `Absence for selected employee(s) has been logged. (Simulated)`) });
    form.reset();
    if (onSuccess) onSuccess();
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4"> {/* Reduced space-y-6 to space-y-4 */}
        <FormField
          control={form.control}
          name="employeeIds"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t('absenceForm.label.employees', 'Employee(s)')}</FormLabel>
              <Select onValueChange={(value) => field.onChange([value])} defaultValue={field.value?.[0]}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder={t('absenceForm.placeholder.selectEmployee', 'Select employee(s)')} />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {employeeOptions.map(emp => (
                    <SelectItem key={emp.value} value={emp.value}>{emp.label}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormDescription>{t('absenceForm.description.selectEmployee', 'Select one or more employees.')}</FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="category"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t('absenceForm.label.category', 'Absence Category')}</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder={t('absenceForm.placeholder.selectCategory', 'Select absence category')} />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="Sick Leave">{t('absenceForm.category.sickLeave', 'Sick Leave')}</SelectItem>
                  <SelectItem value="Annual Leave">{t('absenceForm.category.annualLeave', 'Annual Leave')}</SelectItem>
                  <SelectItem value="Unpaid Leave">{t('absenceForm.category.unpaidLeave', 'Unpaid Leave')}</SelectItem>
                  <SelectItem value="Other">{t('absenceForm.category.other', 'Other')}</SelectItem>
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4"> {/* Reduced gap-6 to gap-4 */}
          <FormField
            control={form.control}
            name="startDate"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>{t('absenceForm.label.startDate', 'Start Date')}</FormLabel>
                <Popover>
                  <PopoverTrigger asChild>
                    <FormControl>
                      <Button
                        variant={"outline"}
                        className={cn("pl-3 text-left font-normal", !field.value && "text-muted-foreground")}
                      >
                        {field.value ? format(field.value, "PPP") : <span>{t('form.placeholder.pickDate', 'Pick a date')}</span>}
                        <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                      </Button>
                    </FormControl>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar mode="single" selected={field.value} onSelect={field.onChange} initialFocus />
                  </PopoverContent>
                </Popover>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="endDate"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>{t('absenceForm.label.endDate', 'End Date')}</FormLabel>
                <Popover>
                  <PopoverTrigger asChild>
                    <FormControl>
                      <Button
                        variant={"outline"}
                        className={cn("pl-3 text-left font-normal", !field.value && "text-muted-foreground")}
                      >
                        {field.value ? format(field.value, "PPP") : <span>{t('form.placeholder.pickDate', 'Pick a date')}</span>}
                        <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                      </Button>
                    </FormControl>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar mode="single" selected={field.value} onSelect={field.onChange} initialFocus />
                  </PopoverContent>
                </Popover>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="notes"
          render={({ field }) => (
            <FormItem>
              <FormLabel>{t('absenceForm.label.notes', 'Notes (Optional)')}</FormLabel>
              <FormControl>
                <Textarea placeholder={t('absenceForm.placeholder.notes', "Reason for absence, doctor's note reference, etc.")} {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <Button type="submit" className="w-full md:w-auto">{t('absenceForm.button.logAbsence', 'Log Absence')}</Button>
      </form>
    </Form>
  );
}
