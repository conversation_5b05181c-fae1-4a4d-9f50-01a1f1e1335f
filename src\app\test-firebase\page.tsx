import { FirebaseConnectionTest } from '@/components/test/FirebaseConnectionTest';
import { ComprehensiveTest } from '@/components/test/ComprehensiveTest';
import { SecurityTest } from '@/components/test/SecurityTest';
import { QuickStart } from '@/components/setup/QuickStart';
import { Ta<PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';

export default function TestFirebasePage() {
  return (
    <div className="min-h-screen bg-background p-4">
      <div className="container mx-auto py-8">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold mb-2">WePaie Setup & Testing</h1>
          <p className="text-muted-foreground">
            Test your Firebase connection and get started with WePaie
          </p>
        </div>

        <Tabs defaultValue="security" className="w-full max-w-4xl mx-auto">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="security">Security Test</TabsTrigger>
            <TabsTrigger value="quickstart">Quick Start</TabsTrigger>
            <TabsTrigger value="comprehensive">Full Test</TabsTrigger>
            <TabsTrigger value="test">Connection Test</TabsTrigger>
          </TabsList>

          <TabsContent value="security" className="mt-6">
            <div className="text-center mb-6">
              <h2 className="text-xl font-semibold mb-2">Security Vulnerability Test</h2>
              <p className="text-muted-foreground">
                Critical security test - verify that unauthorized access is properly blocked
              </p>
            </div>
            <SecurityTest />
          </TabsContent>

          <TabsContent value="quickstart" className="mt-6">
            <div className="text-center mb-6">
              <h2 className="text-xl font-semibold mb-2">Get Started Quickly</h2>
              <p className="text-muted-foreground">
                Create a demo company and start exploring WePaie features immediately
              </p>
            </div>
            <QuickStart />
          </TabsContent>

          <TabsContent value="comprehensive" className="mt-6">
            <div className="text-center mb-6">
              <h2 className="text-xl font-semibold mb-2">Full Functionality Test</h2>
              <p className="text-muted-foreground">
                Complete test including security rules, document references, and company operations
              </p>
            </div>
            <ComprehensiveTest />
          </TabsContent>

          <TabsContent value="test" className="mt-6">
            <div className="text-center mb-6">
              <h2 className="text-xl font-semibold mb-2">Technical Testing</h2>
              <p className="text-muted-foreground">
                Verify that all Firebase services are properly configured and working
              </p>
            </div>
            <FirebaseConnectionTest />
          </TabsContent>
        </Tabs>

        <div className="mt-8 text-center">
          <p className="text-sm text-muted-foreground">
            Having issues? Check the{' '}
            <a href="https://console.firebase.google.com/project/wepaie" target="_blank" rel="noopener noreferrer" className="text-blue-500 hover:underline">
              Firebase Console
            </a>{' '}
            for more details.
          </p>
        </div>
      </div>
    </div>
  );
}
