"use client";

import React, { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { cn } from '@/lib/utils';
import { ImmediateFeedbackSkeleton } from './skeleton-loaders';

interface NavigationLinkProps {
  href: string;
  children: React.ReactNode;
  className?: string;
  showFeedback?: boolean;
  feedbackDuration?: number;
}

/**
 * Navigation link with immediate visual feedback
 * Shows skeleton loading immediately when clicked
 */
export function NavigationLink({ 
  href, 
  children, 
  className, 
  showFeedback = true,
  feedbackDuration = 300 
}: NavigationLinkProps) {
  const [isNavigating, setIsNavigating] = useState(false);
  const router = useRouter();

  const handleClick = (e: React.MouseEvent) => {
    if (showFeedback) {
      setIsNavigating(true);
      
      // Reset after duration
      setTimeout(() => {
        setIsNavigating(false);
      }, feedbackDuration);
    }
  };

  if (isNavigating) {
    return (
      <div className={cn("animate-pulse", className)}>
        <div className="h-6 w-full bg-muted rounded" />
      </div>
    );
  }

  return (
    <Link 
      href={href} 
      className={className}
      onClick={handleClick}
    >
      {children}
    </Link>
  );
}

/**
 * Button with immediate feedback for actions
 */
interface FeedbackButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  children: React.ReactNode;
  isLoading?: boolean;
  loadingText?: string;
  showSkeleton?: boolean;
}

export function FeedbackButton({ 
  children, 
  isLoading = false, 
  loadingText = 'Processing...', 
  showSkeleton = false,
  className,
  disabled,
  ...props 
}: FeedbackButtonProps) {
  if (isLoading && showSkeleton) {
    return (
      <div className={cn("h-10 bg-muted animate-pulse rounded-md", className)} />
    );
  }

  return (
    <button 
      className={cn(
        "inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors",
        "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",
        "disabled:opacity-50 disabled:pointer-events-none",
        "bg-primary text-primary-foreground hover:bg-primary/90",
        "h-10 px-4 py-2",
        className
      )}
      disabled={disabled || isLoading}
      {...props}
    >
      {isLoading ? (
        <>
          <div className="mr-2 h-4 w-4 animate-pulse bg-current rounded" />
          {loadingText}
        </>
      ) : (
        children
      )}
    </button>
  );
}
