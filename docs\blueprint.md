# **App Name**: WePaie Simplified

## Core Features:

- Simplified Employee Management: Streamlined UI for adding and managing employees. Focus on essential details: name, professional information (department, position), and compensation (hourly rate).
- Simplified time management: Simplified absence and overtime input. Minimal categories of absences. Fast input for bulk actions.
- Multi-tenancy: Multi-tenant support allowing multiple companies to use the same application, with data isolation.
- User Roles: User roles to manage access and permissions within the application (admin, payroll_manager).

## Style Guidelines:

- Primary color: #3498db (a vibrant blue for trust and stability)
- Secondary color: #ecf0f1 (a clean white for readability)
- Accent color: #34d9eb (a bright sky blue for interactive elements)
- Body: 'Roboto', sans-serif, a legible and modern font.
- Headlines: 'Montserrat', sans-serif, for a clean and bold look.
- Lucide React icons for a consistent and modern look.
- Clean and structured layout with Tailwind CSS, optimized for responsive design.