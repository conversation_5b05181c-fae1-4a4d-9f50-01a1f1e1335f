import { initializeApp, getApps } from 'firebase/app';
import { getFirestore, connectFirestoreEmulator } from 'firebase/firestore';
import { getAuth, connectAuthEmulator } from 'firebase/auth';

const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,
  measurementId: process.env.NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID,
};

// Initialize Firebase
const app = getApps().length === 0 ? initializeApp(firebaseConfig) : getApps()[0];

// Initialize Firestore
export const db = getFirestore(app);

// Initialize Auth
export const auth = getAuth(app);

// Connect to emulators in development (only if explicitly enabled)
const useEmulator = process.env.NEXT_PUBLIC_USE_FIREBASE_EMULATOR === 'true';

if (process.env.NODE_ENV === 'development' && useEmulator && typeof window !== 'undefined') {
  // Check if we're running in the browser and in development mode
  const isEmulatorConnected = (globalThis as any).__FIREBASE_EMULATOR_CONNECTED__;

  if (!isEmulatorConnected) {
    try {
      // Connect to Firestore emulator
      connectFirestoreEmulator(db, 'localhost', 8080);

      // Connect to Auth emulator
      connectAuthEmulator(auth, 'http://localhost:9099', { disableWarnings: true });

      // Mark as connected to avoid reconnection attempts
      (globalThis as any).__FIREBASE_EMULATOR_CONNECTED__ = true;

      console.log('🔧 Connected to Firebase emulators');
    } catch (error) {
      // Emulators might already be connected or not running
      console.log('Firebase emulators connection info:', error);
    }
  }
} else if (process.env.NODE_ENV === 'development') {
  console.log('🔥 Connected to production Firebase project:', firebaseConfig.projectId);
}

export default app;
