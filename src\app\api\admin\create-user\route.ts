import { NextRequest, NextResponse } from 'next/server';
import { getAuth } from 'firebase-admin/auth';
import { getFirestore } from 'firebase-admin/firestore';
import { initializeApp, getApps, cert } from 'firebase-admin/app';

// Initialize Firebase Admin SDK
if (!getApps().length) {
  const serviceAccount = {
    type: "service_account",
    project_id: process.env.FIREBASE_PROJECT_ID,
    private_key_id: process.env.FIREBASE_PRIVATE_KEY_ID,
    private_key: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
    client_email: process.env.FIREBASE_CLIENT_EMAIL,
    client_id: process.env.FIREBASE_CLIENT_ID,
    auth_uri: "https://accounts.google.com/o/oauth2/auth",
    token_uri: "https://oauth2.googleapis.com/token",
    auth_provider_x509_cert_url: "https://www.googleapis.com/oauth2/v1/certs",
    client_x509_cert_url: `https://www.googleapis.com/robot/v1/metadata/x509/${process.env.FIREBASE_CLIENT_EMAIL}`
  };

  initializeApp({
    credential: cert(serviceAccount as any),
    projectId: process.env.FIREBASE_PROJECT_ID,
  });
}

const auth = getAuth();
const db = getFirestore();

export interface CreateUserRequest {
  email: string;
  firstName: string;
  lastName: string;
  role: string;
  companyId: string;
  phoneNumber?: string;
  sendWelcomeEmail?: boolean;
}

export async function POST(request: NextRequest) {
  try {
    // Verify the request is from an authenticated admin user
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const idToken = authHeader.split('Bearer ')[1];
    
    // Verify the ID token
    const decodedToken = await auth.verifyIdToken(idToken);

    console.log('🔍 Admin API: Decoded token claims:', {
      uid: decodedToken.uid,
      email: decodedToken.email,
      customClaims: decodedToken,
      isAdmin: decodedToken.isAdmin,
      isSuperAdmin: decodedToken.isSuperAdmin,
      role: decodedToken.role
    });

    // Check if user has admin privileges - check both custom claims and Firestore document
    let hasAdminPermission = decodedToken.isAdmin || decodedToken.isSuperAdmin;

    // If custom claims don't indicate admin, check Firestore document
    if (!hasAdminPermission) {
      try {
        console.log('🔍 Admin API: Checking Firestore document for user:', decodedToken.uid);
        const userDoc = await db.collection('users').doc(decodedToken.uid).get();
        console.log('🔍 Admin API: User document exists:', userDoc.exists);

        if (userDoc.exists) {
          const userData = userDoc.data();
          const userRole = userData?.role;
          hasAdminPermission = ['super_admin', 'company_admin'].includes(userRole) && userData?.isActive === true;
          console.log('🔍 Admin API: Checked Firestore for permissions:', {
            userData: userData,
            userRole,
            isActive: userData?.isActive,
            hasAdminPermission,
            allowedRoles: ['super_admin', 'company_admin'],
            roleCheck: ['super_admin', 'company_admin'].includes(userRole)
          });
        } else {
          console.log('❌ Admin API: User document does not exist in Firestore');
        }
      } catch (error) {
        console.error('❌ Admin API: Error checking user document:', error);
      }
    }

    if (!hasAdminPermission) {
      console.log('❌ Admin API: User lacks admin privileges:', {
        customClaimsAdmin: decodedToken.isAdmin,
        customClaimsSuperAdmin: decodedToken.isSuperAdmin,
        hasAdminPermission
      });
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const body: CreateUserRequest = await request.json();
    const { email, firstName, lastName, role, companyId, phoneNumber, sendWelcomeEmail = true } = body;

    // Validate required fields
    if (!email || !firstName || !lastName || !role || !companyId) {
      return NextResponse.json({ error: 'Missing required fields' }, { status: 400 });
    }

    // Prevent creation of additional superuser accounts
    let isSuperAdmin = decodedToken.isSuperAdmin;
    if (!isSuperAdmin) {
      try {
        const userDoc = await db.collection('users').doc(decodedToken.uid).get();
        if (userDoc.exists) {
          const userData = userDoc.data();
          isSuperAdmin = userData?.role === 'super_admin' && userData?.isActive === true;
        }
      } catch (error) {
        console.error('❌ Admin API: Error checking superuser status:', error);
      }
    }

    if (role === 'super_admin' && !isSuperAdmin) {
      return NextResponse.json({ error: 'Only superuser can create superuser accounts' }, { status: 403 });
    }

    // Create the user with Firebase Admin SDK
    const userRecord = await auth.createUser({
      email,
      displayName: `${firstName} ${lastName}`,
      password: 'TempPassword123!', // Temporary password
      emailVerified: false,
    });

    console.log('✅ Admin API: Created Firebase Auth user:', userRecord.uid);

    // Set custom claims
    const customClaims = {
      role,
      companyId,
      isAdmin: ['super_admin', 'company_admin'].includes(role),
      isSuperAdmin: role === 'super_admin',
      isPayrollManager: ['super_admin', 'company_admin', 'editor'].includes(role),
      isViewer: role === 'viewer'
    };

    await auth.setCustomUserClaims(userRecord.uid, customClaims);
    console.log('✅ Admin API: Set custom claims for user:', userRecord.uid);

    // Create user document in Firestore
    const userData = {
      id: userRecord.uid,
      email,
      firstName,
      lastName,
      role,
      companyId,
      isActive: true,
      phoneNumber: phoneNumber || null,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    await db.collection('users').doc(userRecord.uid).set(userData);
    console.log('✅ Admin API: Created Firestore user document');

    // Send password reset email if requested
    if (sendWelcomeEmail) {
      await auth.generatePasswordResetLink(email);
      console.log('✅ Admin API: Sent welcome email to:', email);
    }

    return NextResponse.json({
      success: true,
      user: {
        id: userRecord.uid,
        email,
        firstName,
        lastName,
        role,
        companyId,
        isActive: true,
        phoneNumber,
        createdAt: userData.createdAt.toISOString(),
        updatedAt: userData.updatedAt.toISOString()
      }
    });

  } catch (error) {
    console.error('❌ Admin API: Error creating user:', error);
    
    if (error instanceof Error) {
      if (error.message.includes('email-already-exists')) {
        return NextResponse.json({ error: 'Un utilisateur avec cet email existe déjà' }, { status: 400 });
      }
      if (error.message.includes('invalid-email')) {
        return NextResponse.json({ error: 'Adresse email invalide' }, { status: 400 });
      }
    }

    return NextResponse.json({ 
      error: 'Erreur lors de la création de l\'utilisateur',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
