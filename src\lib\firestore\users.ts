import { BaseFirestoreService } from './base';
import type { UserDocument, QueryOptions } from './types';
import type { User, UserRole } from '@/types';

/**
 * User service for managing user data
 */
export class UserService extends BaseFirestoreService<UserDocument> {
  constructor() {
    super('users');
  }

  /**
   * Create new user
   */
  async createUser(
    userData: Omit<User, 'id'> & {
      firstName: string;
      lastName: string;
      phone?: string;
      avatar?: string;
    },
    companyId: string,
    customId?: string
  ): Promise<UserDocument> {
    const data = {
      ...userData,
      isActive: true,
      emailVerified: false,
      preferences: {
        language: 'en' as const,
        timezone: 'UTC',
        notifications: {
          email: true,
          push: true,
        },
      },
    };

    return this.create(data as any, companyId, customId);
  }

  /**
   * Update user
   */
  async updateUser(
    id: string, 
    updates: Partial<UserDocument>, 
    companyId: string
  ): Promise<UserDocument> {
    return this.update(id, updates, companyId);
  }

  /**
   * Get user by email
   */
  async getUserByEmail(email: string, companyId: string): Promise<UserDocument | null> {
    const whereClause = [
      { field: 'email', operator: '==' as const, value: email },
    ];

    const users = await this.getAll(companyId, { where: whereClause, limit: 1 });
    return users.length > 0 ? users[0] : null;
  }

  /**
   * Get active users only
   */
  async getActiveUsers(companyId: string, options: QueryOptions = {}): Promise<UserDocument[]> {
    const whereClause = [
      { field: 'isActive', operator: '==' as const, value: true },
      ...(options.where || [])
    ];

    return this.getAll(companyId, { ...options, where: whereClause });
  }

  /**
   * Get users by role
   */
  async getUsersByRole(
    companyId: string, 
    role: UserRole, 
    options: QueryOptions = {}
  ): Promise<UserDocument[]> {
    const whereClause = [
      { field: 'isActive', operator: '==' as const, value: true },
      { field: 'role', operator: '==' as const, value: role },
      ...(options.where || [])
    ];

    return this.getAll(companyId, { ...options, where: whereClause });
  }

  /**
   * Deactivate user (soft delete)
   */
  async deactivateUser(id: string, companyId: string): Promise<void> {
    await this.update(id, { isActive: false } as any, companyId);
  }

  /**
   * Activate user
   */
  async activateUser(id: string, companyId: string): Promise<void> {
    await this.update(id, { isActive: true } as any, companyId);
  }

  /**
   * Update user preferences
   */
  async updatePreferences(
    id: string, 
    preferences: Partial<UserDocument['preferences']>, 
    companyId: string
  ): Promise<UserDocument> {
    const user = await this.getById(id, companyId);
    if (!user) {
      throw new Error('User not found');
    }

    const updatedPreferences = {
      ...user.preferences,
      ...preferences,
    };

    return this.update(id, { preferences: updatedPreferences } as any, companyId);
  }

  /**
   * Update last login time
   */
  async updateLastLogin(id: string, companyId: string): Promise<void> {
    await this.update(id, { lastLoginAt: new Date() } as any, companyId);
  }

  /**
   * Verify user email
   */
  async verifyEmail(id: string, companyId: string): Promise<void> {
    await this.update(id, { emailVerified: true } as any, companyId);
  }

  /**
   * Check if email is already taken
   */
  async isEmailTaken(email: string, companyId: string, excludeId?: string): Promise<boolean> {
    const user = await this.getUserByEmail(email, companyId);
    
    if (!user) {
      return false;
    }

    if (excludeId && user.id === excludeId) {
      return false;
    }

    return true;
  }

  /**
   * Get user statistics
   */
  async getUserStats(companyId: string): Promise<{
    total: number;
    active: number;
    inactive: number;
    byRole: Record<UserRole, number>;
    emailVerified: number;
  }> {
    const users = await this.getAll(companyId);
    
    const stats = {
      total: users.length,
      active: users.filter(u => u.isActive).length,
      inactive: users.filter(u => !u.isActive).length,
      byRole: {
        admin: 0,
        payroll_manager: 0,
      } as Record<UserRole, number>,
      emailVerified: users.filter(u => u.emailVerified).length,
    };

    // Count by role
    users.forEach(user => {
      stats.byRole[user.role] = (stats.byRole[user.role] || 0) + 1;
    });

    return stats;
  }

  /**
   * Convert UserDocument to User (for compatibility with existing code)
   */
  toUser(userDoc: UserDocument): User {
    const { 
      firstName, 
      lastName, 
      phone, 
      avatar, 
      isActive, 
      lastLoginAt, 
      emailVerified, 
      preferences, 
      createdAt, 
      updatedAt, 
      ...user 
    } = userDoc;
    
    return user as User;
  }

  /**
   * Convert multiple UserDocuments to Users
   */
  toUsers(userDocs: UserDocument[]): User[] {
    return userDocs.map(doc => this.toUser(doc));
  }

  /**
   * Get full user name
   */
  getFullName(user: UserDocument): string {
    return `${user.firstName} ${user.lastName}`.trim();
  }
}

// Export singleton instance
export const userService = new UserService();
