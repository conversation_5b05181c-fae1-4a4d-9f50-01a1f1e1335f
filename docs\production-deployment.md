# Production Deployment Guide

This guide walks you through deploying WePaie to production with your Firebase project.

## Prerequisites

- ✅ Firebase project created (`wepaie`)
- ✅ Firebase CLI installed and authenticated
- ✅ Environment variables configured
- ✅ Firestore rules and indexes deployed

## Current Configuration

Your WePaie application is now configured with:

```
Project ID: wepaie
Auth Domain: wepaie.firebaseapp.com
Storage Bucket: wepaie.firebasestorage.app
```

## Testing Your Setup

1. **Run Connection Tests**
   ```bash
   npm run dev
   ```
   Visit: http://localhost:9002/test-firebase

2. **Test Company Setup**
   Visit: http://localhost:9002 and use the CompanySetup component

## Deployment Options

### Option 1: Firebase Hosting (Recommended)

1. **Enable Firebase Hosting**
   ```bash
   firebase init hosting
   ```
   
   Select:
   - Use existing project: `wepaie`
   - Public directory: `out` (for static export) or `dist`
   - Single-page app: `Yes`
   - Overwrite index.html: `No`

2. **Configure Next.js for Static Export** (if needed)
   
   Add to `next.config.js`:
   ```javascript
   /** @type {import('next').NextConfig} */
   const nextConfig = {
     output: 'export',
     trailingSlash: true,
     images: {
       unoptimized: true
     }
   };
   
   module.exports = nextConfig;
   ```

3. **Build and Deploy**
   ```bash
   npm run build
   firebase deploy --only hosting
   ```

### Option 2: Vercel (Alternative)

1. **Install Vercel CLI**
   ```bash
   npm install -g vercel
   ```

2. **Deploy**
   ```bash
   vercel
   ```

3. **Add Environment Variables in Vercel Dashboard**
   - Go to your project settings
   - Add all `NEXT_PUBLIC_FIREBASE_*` variables

### Option 3: Other Platforms

For other platforms (Netlify, AWS, etc.), ensure you:
1. Set all environment variables
2. Build the project: `npm run build`
3. Deploy the `dist` or `out` folder

## Environment Variables for Production

Ensure these are set in your production environment:

```env
NEXT_PUBLIC_FIREBASE_API_KEY=AIzaSyDc2twEKEd2x-FIKDLnup-p_uSd5Crar2o
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=wepaie.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=wepaie
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=wepaie.firebasestorage.app
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=578673015610
NEXT_PUBLIC_FIREBASE_APP_ID=1:578673015610:web:3951724700e67e578e2e03
NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID=G-XS0EN9TJHW
```

## Security Checklist

### Firestore Security Rules ✅
- [x] Multi-tenant data isolation
- [x] Role-based access control
- [x] Authenticated user requirements

### Firebase Project Settings
- [ ] Enable App Check (recommended for production)
- [ ] Configure CORS settings if needed
- [ ] Set up monitoring and alerts

### Application Security
- [ ] Implement proper authentication
- [ ] Add input validation
- [ ] Enable HTTPS only
- [ ] Configure CSP headers

## Performance Optimization

### Firestore Indexes ✅
- [x] Composite indexes for complex queries
- [x] Single-field indexes for sorting

### Application Optimization
- [ ] Enable Next.js Image Optimization
- [ ] Implement code splitting
- [ ] Add service worker for offline support
- [ ] Configure CDN for static assets

## Monitoring and Analytics

### Firebase Analytics ✅
- [x] Google Analytics configured
- [x] Measurement ID set up

### Additional Monitoring
- [ ] Set up Firebase Performance Monitoring
- [ ] Configure error tracking (Sentry, etc.)
- [ ] Set up uptime monitoring

## Backup and Recovery

### Firestore Backup
```bash
# Export Firestore data
gcloud firestore export gs://wepaie-backup/$(date +%Y%m%d)
```

### Automated Backups
- [ ] Set up scheduled Firestore exports
- [ ] Configure backup retention policy
- [ ] Test restore procedures

## Development vs Production

### Switch Between Environments

**Use Emulators (Development):**
```env
NEXT_PUBLIC_USE_FIREBASE_EMULATOR=true
```

**Use Production (Default):**
```env
# NEXT_PUBLIC_USE_FIREBASE_EMULATOR=false (or omit)
```

### Commands

```bash
# Development with emulators
npm run dev:emulators

# Development with production Firebase
npm run dev

# Production build
npm run build
npm run start
```

## Troubleshooting

### Common Issues

1. **"Permission denied" errors**
   - Check Firestore security rules
   - Verify user authentication
   - Ensure proper company context

2. **"Project not found" errors**
   - Verify `.firebaserc` project ID
   - Check environment variables
   - Confirm Firebase CLI authentication

3. **Build errors**
   - Check for client-side only code in server components
   - Verify all environment variables are set
   - Test build locally first

### Debug Mode

Enable debug logging:
```javascript
// In firebase.ts
if (process.env.NODE_ENV === 'development') {
  console.log('Firebase config:', firebaseConfig);
}
```

## Next Steps

After successful deployment:

1. **Set up Authentication**
   - Enable Firebase Auth providers
   - Implement login/logout flows
   - Update security rules for authenticated users

2. **Add Real Users**
   - Create company accounts
   - Set up user roles and permissions
   - Import existing employee data

3. **Configure Monitoring**
   - Set up alerts for errors
   - Monitor performance metrics
   - Track user engagement

4. **Scale Considerations**
   - Monitor Firestore usage
   - Optimize queries for large datasets
   - Consider Firestore pricing tiers

## Support

For issues with:
- **Firebase**: Check Firebase Console and documentation
- **Next.js**: Refer to Next.js documentation
- **WePaie**: Check application logs and error messages

## Useful Commands

```bash
# Check Firebase project status
firebase projects:list

# View Firestore data
firebase firestore:data

# Monitor Firestore usage
firebase firestore:usage

# Deploy specific services
firebase deploy --only firestore:rules
firebase deploy --only firestore:indexes
firebase deploy --only hosting
```
