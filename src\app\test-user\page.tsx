"use client";

import { useState } from 'react';
import { useAuth } from '@/context/AuthContext';
import { Button } from '@/components/ui/button';

export default function TestUserPage() {
  const { user } = useAuth();
  const [result, setResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  const testUserStatus = async () => {
    if (!user) {
      alert('Please log in first');
      return;
    }

    setLoading(true);
    try {
      const idToken = await user.getIdToken();

      const response = await fetch('/api/test-user-status', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${idToken}`
        }
      });

      const data = await response.json();
      setResult(data);
      console.log('Test result:', data);
    } catch (error) {
      console.error('Test error:', error);
      setResult({ error: error instanceof Error ? error.message : 'Unknown error' });
    } finally {
      setLoading(false);
    }
  };

  const setupSuperuser = async () => {
    if (!user) {
      alert('Please log in first');
      return;
    }

    setLoading(true);
    try {
      const idToken = await user.getIdToken();

      const response = await fetch('/api/setup-superuser', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${idToken}`
        }
      });

      const data = await response.json();
      setResult(data);
      console.log('Setup result:', data);

      if (data.success) {
        alert('Superuser setup completed! Please refresh the page and try again.');
      }
    } catch (error) {
      console.error('Setup error:', error);
      setResult({ error: error instanceof Error ? error.message : 'Unknown error' });
    } finally {
      setLoading(false);
    }
  };

  const refreshToken = async () => {
    if (!user) {
      alert('Please log in first');
      return;
    }

    setLoading(true);
    try {
      // Force refresh the ID token to get updated custom claims
      await user.getIdToken(true);

      // Reload the page to reinitialize contexts with new token
      window.location.reload();
    } catch (error) {
      console.error('Token refresh error:', error);
      setResult({ error: error instanceof Error ? error.message : 'Unknown error' });
    } finally {
      setLoading(false);
    }
  };

  if (!user) {
    return (
      <div className="p-8">
        <h1 className="text-2xl font-bold mb-4">User Status Test</h1>
        <p>Please log in to test user status.</p>
      </div>
    );
  }

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">User Status Test</h1>
      <p className="mb-4">Current user: {user.email}</p>
      
      <div className="space-x-4">
        <Button onClick={testUserStatus} disabled={loading}>
          {loading ? 'Testing...' : 'Test User Status'}
        </Button>

        <Button onClick={setupSuperuser} disabled={loading} variant="outline">
          {loading ? 'Setting up...' : 'Setup Superuser'}
        </Button>

        <Button onClick={refreshToken} disabled={loading} variant="secondary">
          {loading ? 'Refreshing...' : 'Refresh Token & Reload'}
        </Button>
      </div>

      {result && (
        <div className="mt-6">
          <h2 className="text-lg font-semibold mb-2">Result:</h2>
          <pre className="bg-gray-100 p-4 rounded overflow-auto">
            {JSON.stringify(result, null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
}
