/**
 * RBAC Context for WePaie
 * 
 * Provides role-based access control context and hooks
 * for managing user permissions throughout the application.
 */

'use client';

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { useAuth } from './AuthContext';
import { userService } from '@/lib/firestore';
import type { 
  RBACUser, 
  Permission, 
  UserRole, 
  PermissionContext, 
  PermissionResult,
  CustomClaims 
} from '@/lib/rbac/types';
import { 
  hasPermission, 
  hasAnyPermission, 
  hasAllPermissions,
  canAccessCompany,
  canManageUser,
  checkPermission,
  canPerformAction,
  hasRoleOrHigher,
  getPermissionsFromClaims,
  canCreateUserWithRole
} from '@/lib/rbac/permissions';

interface RBACContextType {
  // User data
  user: RBACUser | null;
  isLoading: boolean;
  isInitialized: boolean;
  
  // Permission checking
  hasPermission: (permission: Permission, context?: PermissionContext) => boolean;
  hasAnyPermission: (permissions: Permission[], context?: PermissionContext) => boolean;
  hasAllPermissions: (permissions: Permission[], context?: PermissionContext) => boolean;
  checkPermission: (permission: Permission, context?: PermissionContext) => PermissionResult;
  canPerformAction: (action: 'create' | 'read' | 'update' | 'delete', resource: string, context?: PermissionContext) => boolean;
  
  // Role checking
  hasRole: (role: UserRole) => boolean;
  hasRoleOrHigher: (minRole: UserRole) => boolean;
  isRole: (role: UserRole) => boolean;
  
  // Company access
  canAccessCompany: (companyId: string) => boolean;
  isSameCompany: (companyId: string) => boolean;
  
  // User management
  canManageUser: (targetUser: RBACUser) => boolean;
  canCreateUserWithRole: (targetRole: UserRole, targetCompanyId?: string) => boolean;
  
  // Convenience role checks
  isSuperAdmin: () => boolean;
  isCompanyAdmin: () => boolean;
  isEditor: () => boolean;
  isViewer: () => boolean;
  
  // Legacy compatibility
  isAdmin: () => boolean;
  isPayrollManager: () => boolean;
  
  // Refresh user data
  refreshUser: () => Promise<void>;
}

const RBACContext = createContext<RBACContextType | undefined>(undefined);

interface RBACProviderProps {
  children: ReactNode;
}

export function RBACProvider({ children }: RBACProviderProps) {
  const { firebaseUser, isLoading: authLoading, isInitialized: authInitialized } = useAuth();
  const [user, setUser] = useState<RBACUser | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isInitialized, setIsInitialized] = useState(false);

  // Load user data when Firebase user changes
  useEffect(() => {
    async function loadUser() {
      if (!authInitialized) return;

      console.log('🔐 RBAC: Auth state changed, loading user data...', {
        firebaseUser: firebaseUser?.email || 'None',
        currentUser: user?.email || 'None',
        authInitialized
      });

      // Always set loading when auth state changes
      setIsLoading(true);

      try {
        if (firebaseUser) {
          console.log('🔐 RBAC: Loading user data for:', firebaseUser.email);

          // Get custom claims for role information with retry logic
          let claims: CustomClaims = { role: 'viewer' };
          try {
            // Force refresh token to get latest custom claims
            const tokenResult = await firebaseUser.getIdTokenResult(true);
            claims = tokenResult.claims as CustomClaims;
            console.log('🔐 RBAC: Custom claims loaded:', {
              role: claims.role,
              companyId: claims.companyId,
              isAdmin: claims.isAdmin,
              isSuperAdmin: claims.isSuperAdmin
            });
          } catch (error) {
            console.warn('🔐 RBAC: Could not load custom claims, using fallback:', error);

            // Retry without force refresh
            try {
              const tokenResult = await firebaseUser.getIdTokenResult(false);
              claims = tokenResult.claims as CustomClaims;
              console.log('🔐 RBAC: Custom claims loaded on retry:', claims);
            } catch (retryError) {
              console.error('🔐 RBAC: Failed to load custom claims on retry:', retryError);
            }
          }

          // Determine user role with fallback logic
          let userRole: UserRole = 'viewer';
          let companyId: string | undefined;

          // Check for custom claims first
          if (claims.role) {
            userRole = claims.role;
            companyId = claims.companyId;
          }
          // Fallback for manually created superuser
          else if (firebaseUser.email === '<EMAIL>') {
            userRole = 'super_admin';
            companyId = undefined; // Super admin doesn't belong to a specific company
            console.log('🔐 RBAC: Detected manual superuser, assigning super_admin role');
          }
          // Fallback for demo admin
          else if (firebaseUser.email === '<EMAIL>') {
            userRole = 'company_admin';
            companyId = 'demo-company';
            console.log('🔐 RBAC: Detected demo admin, assigning company_admin role');
          }

          // Try to load user from Firestore if we have a company
          let userData: RBACUser | null = null;
          if (companyId) {
            try {
              userData = await userService.getById(firebaseUser.uid, companyId) as RBACUser;
              console.log('🔐 RBAC: Loaded user from Firestore:', userData);
            } catch (error) {
              console.warn('🔐 RBAC: Could not load user from Firestore:', error);
            }
          }

          // If no Firestore data, create user from Firebase user and determined role
          if (!userData) {
            const displayName = firebaseUser.displayName || firebaseUser.email?.split('@')[0] || 'User';
            const nameParts = displayName.split(' ');

            userData = {
              id: firebaseUser.uid,
              email: firebaseUser.email || '',
              firstName: nameParts[0] || 'User',
              lastName: nameParts.slice(1).join(' ') || '',
              role: userRole,
              companyId: companyId,
              isActive: true,
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
              lastLoginAt: new Date().toISOString()
            };
            console.log('🔐 RBAC: Created user from Firebase data:', userData);
          }

          setUser(userData);
        } else {
          console.log('🔐 RBAC: No Firebase user, clearing RBAC user');
          setUser(null);
        }
      } catch (error) {
        console.error('🔐 RBAC: Error loading user data:', error);
        setUser(null);
      } finally {
        setIsLoading(false);
        setIsInitialized(true);
      }
    }

    loadUser();
  }, [firebaseUser, authInitialized]);

  // Refresh user data
  const refreshUser = async () => {
    if (firebaseUser && user?.companyId) {
      try {
        const userData = await userService.getById(firebaseUser.uid, user.companyId) as RBACUser;
        setUser(userData);
      } catch (error) {
        console.error('🔐 RBAC: Error refreshing user data:', error);
      }
    }
  };

  // Handle logout cleanup and state reset
  useEffect(() => {
    if (!firebaseUser && user) {
      console.log('🔐 RBAC: Firebase user logged out, clearing RBAC state');
      setUser(null);
      setIsLoading(false);
      setIsInitialized(true);
    } else if (!firebaseUser && !user && authInitialized) {
      // Ensure we're in a clean state when no user is present
      console.log('🔐 RBAC: No user present, ensuring clean state');
      setIsLoading(false);
      setIsInitialized(true);
    }
  }, [firebaseUser, user, authInitialized]);

  // Permission checking functions
  const contextValue: RBACContextType = {
    user,
    isLoading: isLoading || authLoading,
    isInitialized: isInitialized && authInitialized,
    
    // Permission checking
    hasPermission: (permission: Permission, context?: PermissionContext) => 
      hasPermission(user, permission, context),
    hasAnyPermission: (permissions: Permission[], context?: PermissionContext) => 
      hasAnyPermission(user, permissions, context),
    hasAllPermissions: (permissions: Permission[], context?: PermissionContext) => 
      hasAllPermissions(user, permissions, context),
    checkPermission: (permission: Permission, context?: PermissionContext) => 
      checkPermission(user, permission, context),
    canPerformAction: (action: 'create' | 'read' | 'update' | 'delete', resource: string, context?: PermissionContext) => 
      canPerformAction(user, action, resource, context),
    
    // Role checking
    hasRole: (role: UserRole) => user?.role === role && user?.isActive === true,
    hasRoleOrHigher: (minRole: UserRole) => hasRoleOrHigher(user, minRole),
    isRole: (role: UserRole) => user?.role === role,
    
    // Company access
    canAccessCompany: (companyId: string) => canAccessCompany(user, companyId),
    isSameCompany: (companyId: string) => user?.companyId === companyId,
    
    // User management
    canManageUser: (targetUser: RBACUser) => canManageUser(user, targetUser),
    canCreateUserWithRole: (targetRole: UserRole, targetCompanyId?: string) => 
      canCreateUserWithRole(user, targetRole, targetCompanyId),
    
    // Convenience role checks
    isSuperAdmin: () => user?.role === 'super_admin' && user?.isActive === true,
    isCompanyAdmin: () => user?.role === 'company_admin' && user?.isActive === true,
    isEditor: () => user?.role === 'editor' && user?.isActive === true,
    isViewer: () => user?.role === 'viewer' && user?.isActive === true,
    
    // Legacy compatibility
    isAdmin: () => ['super_admin', 'company_admin'].includes(user?.role || '') && user?.isActive === true,
    isPayrollManager: () => ['super_admin', 'company_admin', 'editor'].includes(user?.role || '') && user?.isActive === true,
    
    refreshUser
  };

  return (
    <RBACContext.Provider value={contextValue}>
      {children}
    </RBACContext.Provider>
  );
}

// Hook to use RBAC context
export function useRBAC(): RBACContextType {
  const context = useContext(RBACContext);
  if (context === undefined) {
    throw new Error('useRBAC must be used within an RBACProvider');
  }
  return context;
}

// Convenience hooks for specific checks
export function usePermission(permission: Permission, context?: PermissionContext): boolean {
  const { hasPermission } = useRBAC();
  return hasPermission(permission, context);
}

export function useRole(role: UserRole): boolean {
  const { hasRole } = useRBAC();
  return hasRole(role);
}

export function useCanManage(targetUser: RBACUser): boolean {
  const { canManageUser } = useRBAC();
  return canManageUser(targetUser);
}

export function useCompanyAccess(companyId: string): boolean {
  const { canAccessCompany } = useRBAC();
  return canAccessCompany(companyId);
}

// Legacy compatibility hooks
export function usePermissions() {
  const rbac = useRBAC();
  return {
    isAdmin: rbac.isAdmin,
    isPayrollManager: rbac.isPayrollManager,
    isSuperAdmin: rbac.isSuperAdmin,
    isCompanyAdmin: rbac.isCompanyAdmin,
    isEditor: rbac.isEditor,
    isViewer: rbac.isViewer,
    hasPermission: rbac.hasPermission,
    canManageUser: rbac.canManageUser
  };
}

export function useUserId(): string | null {
  const { user } = useRBAC();
  return user?.id || null;
}

export function useCompanyId(): string | null {
  const { user } = useRBAC();
  return user?.companyId || null;
}
