"use client";

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useAuth } from '@/context/AuthContext';
import { useRBAC } from '@/context/RBACContext';
import { companyService } from '@/lib/firestore';
import type { Company } from '@/lib/firestore/types';

interface CompanyContextType {
  // Current company state
  selectedCompany: Company | null;
  availableCompanies: Company[];
  
  // Loading states
  isLoading: boolean;
  isInitialized: boolean;
  
  // Actions
  selectCompany: (company: Company) => void;
  clearCompany: () => void;
  refreshCompanies: () => Promise<void>;
  refreshAvailableCompanies: () => Promise<void>;
  
  // Validation
  requiresCompanySelection: () => boolean;
  canAccessCompany: (companyId: string) => boolean;
}

const CompanyContext = createContext<CompanyContextType | undefined>(undefined);

interface CompanyProviderProps {
  children: ReactNode;
}

export function CompanyProvider({ children }: CompanyProviderProps) {
  const [selectedCompany, setSelectedCompany] = useState<Company | null>(null);
  const [availableCompanies, setAvailableCompanies] = useState<Company[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isInitialized, setIsInitialized] = useState(false);

  const { firebaseUser } = useAuth();
  const { user: rbacUser, isSuperAdmin, isInitialized: rbacInitialized } = useRBAC();

  // Load available companies when user is authenticated
  useEffect(() => {
    console.log('🏢 CompanyContext: Auth state changed:', {
      firebaseUser: firebaseUser?.email || 'None',
      rbacInitialized,
      rbacUser: rbacUser?.email || 'None'
    });

    if (firebaseUser && rbacInitialized && rbacUser) {
      console.log('🏢 CompanyContext: User authenticated and RBAC ready, loading companies');
      loadAvailableCompanies();
    } else if (!firebaseUser) {
      // Clear state when user logs out
      console.log('🏢 CompanyContext: User logged out, clearing state');
      setSelectedCompany(null);
      setAvailableCompanies([]);
      localStorage.removeItem('selectedCompanyId');
      localStorage.removeItem('autoSelectedCompany');
      setIsLoading(false);
      setIsInitialized(true);
    }
  }, [firebaseUser, rbacInitialized, rbacUser]);

  // Auto-select company for non-superuser accounts
  useEffect(() => {
    if (rbacUser && !isSuperAdmin() && rbacUser.companyId && availableCompanies.length > 0) {
      const userCompany = availableCompanies.find(company => company.id === rbacUser.companyId);
      if (userCompany && !selectedCompany) {
        console.log('🏢 CompanyContext: Auto-selecting company for user:', userCompany.name);
        setSelectedCompany(userCompany);

        // Store in localStorage for session persistence
        localStorage.setItem('selectedCompanyId', userCompany.id);
        localStorage.setItem('autoSelectedCompany', 'true');
      }
    }
  }, [rbacUser, availableCompanies, selectedCompany, isSuperAdmin]);

  const loadAvailableCompanies = async () => {
    try {
      setIsLoading(true);
      console.log('🏢 CompanyContext: Loading available companies');

      let companies: Company[] = [];

      if (isSuperAdmin()) {
        // Super admin can see all companies
        console.log('🏢 CompanyContext: Loading all companies for super admin');

        try {
          companies = await companyService.getAll();
          console.log('🏢 CompanyContext: Superuser loaded companies:', companies.map(c => ({ id: c.id, name: c.name })));
        } catch (error) {
          console.error('🏢 CompanyContext: Error loading all companies:', error);
        }
      } else if (rbacUser?.companyId) {
        // Regular users can only see their company
        console.log('🏢 CompanyContext: Loading company for user:', rbacUser.companyId);
        
        try {
          const userCompany = await companyService.getById(rbacUser.companyId);
          if (userCompany) {
            companies = [userCompany];
          }
        } catch (error) {
          console.error('🏢 CompanyContext: Error loading user company:', error);
        }
      }

      setAvailableCompanies(companies);
      console.log('🏢 CompanyContext: Loaded companies:', companies.length);

    } catch (error) {
      console.error('🏢 CompanyContext: Error loading companies:', error);
      setAvailableCompanies([]);
    } finally {
      setIsLoading(false);
      setIsInitialized(true);
    }
  };

  const selectCompany = (company: Company) => {
    console.log('🏢 CompanyContext: Selecting company:', company.name);
    setSelectedCompany(company);
    
    // Store selection in localStorage for persistence
    localStorage.setItem('selectedCompanyId', company.id);
  };

  const clearCompany = () => {
    console.log('🏢 CompanyContext: Clearing company selection');
    setSelectedCompany(null);
    localStorage.removeItem('selectedCompanyId');
  };

  const refreshCompanies = async () => {
    await loadAvailableCompanies();
  };

  const refreshAvailableCompanies = async () => {
    console.log('🏢 CompanyContext: Refreshing available companies');
    await loadAvailableCompanies();
  };

  const requiresCompanySelection = (): boolean => {
    // Super admin doesn't require company selection for global operations
    if (isSuperAdmin()) {
      return false;
    }
    
    // Regular users need a company selected
    return !selectedCompany;
  };

  const canAccessCompany = (companyId: string): boolean => {
    // Super admin can access any company
    if (isSuperAdmin()) {
      return true;
    }
    
    // Regular users can only access their own company
    return rbacUser?.companyId === companyId;
  };

  // Restore company selection from localStorage
  useEffect(() => {
    if (isInitialized && availableCompanies.length > 0 && !selectedCompany) {
      const savedCompanyId = localStorage.getItem('selectedCompanyId');
      if (savedCompanyId) {
        const savedCompany = availableCompanies.find(company => company.id === savedCompanyId);
        if (savedCompany && canAccessCompany(savedCompany.id)) {
          setSelectedCompany(savedCompany);
        } else {
          // Remove invalid selection
          localStorage.removeItem('selectedCompanyId');
        }
      }
    }
  }, [isInitialized, availableCompanies, selectedCompany]);

  const value: CompanyContextType = {
    selectedCompany,
    availableCompanies,
    isLoading,
    isInitialized,
    selectCompany,
    clearCompany,
    refreshCompanies,
    refreshAvailableCompanies,
    requiresCompanySelection,
    canAccessCompany,
  };

  return (
    <CompanyContext.Provider value={value}>
      {children}
    </CompanyContext.Provider>
  );
}

export function useCompany() {
  const context = useContext(CompanyContext);
  if (context === undefined) {
    throw new Error('useCompany must be used within a CompanyProvider');
  }
  return context;
}

/**
 * Hook to get the current company ID with validation
 * Returns null if no company is selected or user doesn't have access
 */
export function useCompanyId(): string | null {
  const { selectedCompany, canAccessCompany } = useCompany();
  
  if (!selectedCompany) {
    return null;
  }
  
  if (!canAccessCompany(selectedCompany.id)) {
    return null;
  }
  
  return selectedCompany.id;
}

/**
 * Hook to enforce company selection before data operations
 * Throws an error if no valid company is selected
 */
export function useRequiredCompany(): Company {
  const { selectedCompany, requiresCompanySelection, canAccessCompany } = useCompany();
  
  if (requiresCompanySelection()) {
    throw new Error('Company selection is required for this operation');
  }
  
  if (!selectedCompany) {
    throw new Error('No company selected');
  }
  
  if (!canAccessCompany(selectedCompany.id)) {
    throw new Error('Access denied to selected company');
  }
  
  return selectedCompany;
}

/**
 * Hook for data integrity validation
 * Ensures all data operations respect company boundaries
 */
export function useDataIntegrity() {
  const { selectedCompany, canAccessCompany, requiresCompanySelection } = useCompany();
  const { isSuperAdmin } = useRBAC();
  
  const validateCompanyAccess = (companyId: string): boolean => {
    return canAccessCompany(companyId);
  };
  
  const validateDataOperation = (operation: string, companyId?: string): void => {
    // Super admin can perform global operations
    if (isSuperAdmin() && !companyId) {
      return;
    }
    
    // For company-specific operations, validate company access
    if (companyId && !canAccessCompany(companyId)) {
      throw new Error(`Access denied: Cannot perform ${operation} on company ${companyId}`);
    }
    
    // For operations requiring company context, ensure company is selected
    if (requiresCompanySelection() && !companyId) {
      throw new Error(`Company selection required for ${operation}`);
    }
  };
  
  const getEffectiveCompanyId = (): string | null => {
    return selectedCompany?.id || null;
  };
  
  return {
    validateCompanyAccess,
    validateDataOperation,
    getEffectiveCompanyId,
    requiresCompanySelection: requiresCompanySelection(),
    canPerformGlobalOperations: isSuperAdmin(),
  };
}
