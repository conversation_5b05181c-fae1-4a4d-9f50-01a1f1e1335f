"use client";

import React, { useState } from 'react';
import { initializeDemoSetup, demoCompanyExists, demoAdminExists } from '@/lib/demo-data';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { 
  Loader2, 
  CheckCircle, 
  XCircle, 
  AlertCircle, 
  Rocket,
  RefreshCw,
  Database
} from 'lucide-react';

/**
 * Demo Data Test Page
 * 
 * This page allows testing the demo data initialization functionality
 * and provides detailed feedback about the process.
 */
export default function TestDemoPage() {
  const [isInitializing, setIsInitializing] = useState(false);
  const [isChecking, setIsChecking] = useState(false);
  const [result, setResult] = useState<{
    success: boolean;
    message: string;
    details?: any;
  } | null>(null);
  const [existenceCheck, setExistenceCheck] = useState<{
    company: boolean | null;
    admin: boolean | null;
  }>({ company: null, admin: null });

  const handleInitializeDemo = async () => {
    setIsInitializing(true);
    setResult(null);

    try {
      console.log('🚀 Starting demo data initialization...');
      const { company, admin } = await initializeDemoSetup();
      
      setResult({
        success: true,
        message: 'Demo data initialized successfully!',
        details: {
          company: {
            id: company.id,
            name: company.name,
            email: company.email
          },
          admin: {
            id: admin.id,
            name: admin.name,
            email: admin.email,
            role: admin.role
          }
        }
      });

      // Refresh existence check
      await checkExistence();
      
    } catch (error: any) {
      console.error('❌ Demo initialization failed:', error);
      setResult({
        success: false,
        message: error.message || 'Failed to initialize demo data',
        details: error
      });
    } finally {
      setIsInitializing(false);
    }
  };

  const checkExistence = async () => {
    setIsChecking(true);
    try {
      const [companyExists, adminExists] = await Promise.all([
        demoCompanyExists(),
        demoAdminExists()
      ]);
      
      setExistenceCheck({
        company: companyExists,
        admin: adminExists
      });
    } catch (error) {
      console.error('Error checking existence:', error);
      setExistenceCheck({
        company: null,
        admin: null
      });
    } finally {
      setIsChecking(false);
    }
  };

  // Check existence on component mount
  React.useEffect(() => {
    checkExistence();
  }, []);

  const getStatusIcon = (status: boolean | null) => {
    if (status === null) return <Loader2 className="h-4 w-4 animate-spin" />;
    return status ? <CheckCircle className="h-4 w-4 text-green-500" /> : <XCircle className="h-4 w-4 text-red-500" />;
  };

  const getStatusText = (status: boolean | null) => {
    if (status === null) return 'Checking...';
    return status ? 'Exists' : 'Not Found';
  };

  const getStatusVariant = (status: boolean | null): "default" | "secondary" | "destructive" => {
    if (status === null) return 'secondary';
    return status ? 'default' : 'destructive';
  };

  return (
    <ProtectedRoute requireAuth={true} requireAdmin={true}>
      <div className="max-w-4xl mx-auto p-6 space-y-6">
      <div className="text-center space-y-2">
        <Database className="h-12 w-12 mx-auto text-primary" />
        <h1 className="text-3xl font-bold">Demo Data Test</h1>
        <p className="text-muted-foreground">
          Test and verify demo data initialization for WePaie
        </p>
      </div>

      {/* Current Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            Current Status
            <Button 
              variant="outline" 
              size="sm" 
              onClick={checkExistence}
              disabled={isChecking}
            >
              {isChecking ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <RefreshCw className="h-4 w-4" />
              )}
            </Button>
          </CardTitle>
          <CardDescription>
            Check if demo company and admin user already exist
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div>
                <h3 className="font-medium">Demo Company</h3>
                <p className="text-sm text-muted-foreground">WePaie Demo Company</p>
              </div>
              <div className="flex items-center gap-2">
                {getStatusIcon(existenceCheck.company)}
                <Badge variant={getStatusVariant(existenceCheck.company)}>
                  {getStatusText(existenceCheck.company)}
                </Badge>
              </div>
            </div>

            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div>
                <h3 className="font-medium">Demo Admin</h3>
                <p className="text-sm text-muted-foreground"><EMAIL></p>
              </div>
              <div className="flex items-center gap-2">
                {getStatusIcon(existenceCheck.admin)}
                <Badge variant={getStatusVariant(existenceCheck.admin)}>
                  {getStatusText(existenceCheck.admin)}
                </Badge>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Initialize Demo Data */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Rocket className="h-5 w-5" />
            Initialize Demo Data
          </CardTitle>
          <CardDescription>
            Create demo company and admin user for testing
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Button 
            onClick={handleInitializeDemo}
            disabled={isInitializing}
            className="w-full"
            size="lg"
          >
            {isInitializing ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Initializing Demo Data...
              </>
            ) : (
              <>
                <Rocket className="mr-2 h-4 w-4" />
                Initialize Demo Data
              </>
            )}
          </Button>

          {result && (
            <Alert variant={result.success ? "default" : "destructive"}>
              {result.success ? (
                <CheckCircle className="h-4 w-4" />
              ) : (
                <AlertCircle className="h-4 w-4" />
              )}
              <AlertDescription>
                <div className="space-y-2">
                  <p className="font-medium">{result.message}</p>
                  {result.details && result.success && (
                    <div className="text-sm space-y-1">
                      <p><strong>Company:</strong> {result.details.company.name} ({result.details.company.id})</p>
                      <p><strong>Admin:</strong> {result.details.admin.name} ({result.details.admin.email})</p>
                    </div>
                  )}
                  {result.details && !result.success && (
                    <div className="text-sm">
                      <p><strong>Error Details:</strong> {JSON.stringify(result.details, null, 2)}</p>
                    </div>
                  )}
                </div>
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Next Steps */}
      {result?.success && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-500" />
              Success! Next Steps
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <p>Demo data has been successfully initialized. You can now:</p>
            <ul className="list-disc list-inside space-y-1 text-sm">
              <li>Navigate to <a href="/employees" className="text-blue-600 hover:underline">/employees</a> to test employee management</li>
              <li>The demo company and admin user are now available in the context</li>
              <li>All employee management features should work properly</li>
              <li>Data will persist in Firebase Firestore</li>
            </ul>
          </CardContent>
        </Card>
      )}

      {/* Development Info */}
      <Alert>
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          <strong>Development Mode:</strong> This test page is only available in development. 
          It helps verify that Firebase permissions are correctly configured for demo data creation.
        </AlertDescription>
      </Alert>
      </div>
    </ProtectedRoute>
  );
}
