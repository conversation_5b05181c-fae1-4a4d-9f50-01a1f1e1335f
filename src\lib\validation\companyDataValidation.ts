/**
 * Company Data Validation Service
 * 
 * Provides validation functions to ensure all data operations
 * respect company boundaries and maintain data integrity.
 */

import type { Company } from '@/lib/firestore/types';

export interface DataValidationContext {
  companyId: string;
  userId: string;
  userRole: string;
  isSuperAdmin: boolean;
}

export interface ValidationResult {
  isValid: boolean;
  error?: string;
  code?: string;
}

/**
 * Validates that a company ID is provided and valid
 */
export function validateCompanyId(companyId: string | null | undefined): ValidationResult {
  if (!companyId || companyId.trim() === '') {
    return {
      isValid: false,
      error: 'ID d\'entreprise requis pour cette opération',
      code: 'MISSING_COMPANY_ID'
    };
  }

  if (companyId.length < 3) {
    return {
      isValid: false,
      error: 'ID d\'entreprise invalide',
      code: 'INVALID_COMPANY_ID'
    };
  }

  return { isValid: true };
}

/**
 * Validates that user has access to the specified company
 */
export function validateCompanyAccess(
  context: DataValidationContext,
  targetCompanyId: string
): ValidationResult {
  // Super admin can access any company
  if (context.isSuperAdmin) {
    return { isValid: true };
  }

  // Regular users can only access their own company
  if (context.companyId !== targetCompanyId) {
    return {
      isValid: false,
      error: 'Accès refusé à cette entreprise',
      code: 'COMPANY_ACCESS_DENIED'
    };
  }

  return { isValid: true };
}

/**
 * Validates data creation within company context
 */
export function validateDataCreation(
  context: DataValidationContext,
  data: { companyId?: string; [key: string]: any }
): ValidationResult {
  // Ensure company ID is provided
  const companyValidation = validateCompanyId(data.companyId);
  if (!companyValidation.isValid) {
    return companyValidation;
  }

  // Validate company access
  const accessValidation = validateCompanyAccess(context, data.companyId!);
  if (!accessValidation.isValid) {
    return accessValidation;
  }

  // For non-super admins, ensure they're creating data in their own company
  if (!context.isSuperAdmin && data.companyId !== context.companyId) {
    return {
      isValid: false,
      error: 'Vous ne pouvez créer des données que pour votre entreprise',
      code: 'CROSS_COMPANY_CREATION_DENIED'
    };
  }

  return { isValid: true };
}

/**
 * Validates data modification within company context
 */
export function validateDataModification(
  context: DataValidationContext,
  existingData: { companyId: string; [key: string]: any },
  newData: { companyId?: string; [key: string]: any }
): ValidationResult {
  // Validate access to existing data
  const accessValidation = validateCompanyAccess(context, existingData.companyId);
  if (!accessValidation.isValid) {
    return accessValidation;
  }

  // If company ID is being changed, validate the new company
  if (newData.companyId && newData.companyId !== existingData.companyId) {
    // Only super admin can move data between companies
    if (!context.isSuperAdmin) {
      return {
        isValid: false,
        error: 'Vous ne pouvez pas déplacer des données entre entreprises',
        code: 'CROSS_COMPANY_MOVE_DENIED'
      };
    }

    // Validate access to target company
    const targetAccessValidation = validateCompanyAccess(context, newData.companyId);
    if (!targetAccessValidation.isValid) {
      return targetAccessValidation;
    }
  }

  return { isValid: true };
}

/**
 * Validates data deletion within company context
 */
export function validateDataDeletion(
  context: DataValidationContext,
  data: { companyId: string; [key: string]: any }
): ValidationResult {
  return validateCompanyAccess(context, data.companyId);
}

/**
 * Validates bulk operations within company context
 */
export function validateBulkOperation(
  context: DataValidationContext,
  items: Array<{ companyId: string; [key: string]: any }>
): ValidationResult {
  // Check if all items belong to accessible companies
  for (const item of items) {
    const validation = validateCompanyAccess(context, item.companyId);
    if (!validation.isValid) {
      return {
        isValid: false,
        error: `Accès refusé à l'entreprise ${item.companyId}`,
        code: 'BULK_OPERATION_ACCESS_DENIED'
      };
    }
  }

  // For non-super admins, ensure all items belong to the same company
  if (!context.isSuperAdmin) {
    const companyIds = new Set(items.map(item => item.companyId));
    if (companyIds.size > 1) {
      return {
        isValid: false,
        error: 'Les opérations en lot ne peuvent pas s\'étendre sur plusieurs entreprises',
        code: 'CROSS_COMPANY_BULK_DENIED'
      };
    }

    // Ensure the company is the user's company
    const firstCompanyId = items[0]?.companyId;
    if (firstCompanyId && firstCompanyId !== context.companyId) {
      return {
        isValid: false,
        error: 'Vous ne pouvez effectuer des opérations en lot que sur votre entreprise',
        code: 'BULK_OPERATION_COMPANY_MISMATCH'
      };
    }
  }

  return { isValid: true };
}

/**
 * Ensures data has proper company association
 */
export function ensureCompanyAssociation<T extends Record<string, any>>(
  data: T,
  companyId: string
): T & { companyId: string } {
  return {
    ...data,
    companyId
  };
}

/**
 * Filters data by company access
 */
export function filterByCompanyAccess<T extends { companyId: string }>(
  items: T[],
  context: DataValidationContext
): T[] {
  if (context.isSuperAdmin) {
    return items; // Super admin can see all
  }

  return items.filter(item => item.companyId === context.companyId);
}

/**
 * Creates a validation context from user and company information
 */
export function createValidationContext(
  userId: string,
  companyId: string,
  userRole: string,
  isSuperAdmin: boolean
): DataValidationContext {
  return {
    companyId,
    userId,
    userRole,
    isSuperAdmin
  };
}

/**
 * Audit log entry for data operations
 */
export interface AuditLogEntry {
  userId: string;
  userRole: string;
  companyId: string;
  operation: string;
  resourceType: string;
  resourceId: string;
  timestamp: Date;
  details?: Record<string, any>;
}

/**
 * Creates an audit log entry for data operations
 */
export function createAuditLogEntry(
  context: DataValidationContext,
  operation: string,
  resourceType: string,
  resourceId: string,
  details?: Record<string, any>
): AuditLogEntry {
  return {
    userId: context.userId,
    userRole: context.userRole,
    companyId: context.companyId,
    operation,
    resourceType,
    resourceId,
    timestamp: new Date(),
    details
  };
}

/**
 * Validation error class for company data operations
 */
export class CompanyDataValidationError extends Error {
  constructor(
    public code: string,
    message: string,
    public context?: DataValidationContext
  ) {
    super(message);
    this.name = 'CompanyDataValidationError';
  }
}

/**
 * Throws a validation error if validation fails
 */
export function assertValidation(
  validation: ValidationResult,
  context?: DataValidationContext
): void {
  if (!validation.isValid) {
    throw new CompanyDataValidationError(
      validation.code || 'VALIDATION_ERROR',
      validation.error || 'Validation failed',
      context
    );
  }
}
