"use client";

import React from 'react';
import { useRouter } from 'next/router';
import { HeaderProps, RouteConfig } from '@/types/layout';
import { CompanySelector } from '@/components/company/CompanySelector';
import { UserNav } from '@/components/layout/UserNav';
import { useRBAC } from '@/context/RBACContext';
import { useTranslation } from '@/context/i18nContext';

// Configuration des routes et leurs titres
const routeConfig: RouteConfig = {
  '/': {
    title: 'dashboard.title',
    description: 'dashboard.description',
    requireAuth: true,
  },
  '/employees': {
    title: 'employees.title',
    description: 'employees.description',
    requireAuth: true,
    requireCompany: true,
  },
  '/admin': {
    title: 'admin.title',
    description: 'admin.description',
    requireAuth: true,
    showCompanySelector: true,
  },
  '/settings': {
    title: 'settings.title',
    description: 'settings.description',
    requireAuth: true,
  },
  '/time-management': {
    title: 'timeManagement.title',
    description: 'timeManagement.description',
    requireAuth: true,
    requireCompany: true,
  },
};

const Header: React.FC<HeaderProps> = ({ 
  pageTitle, 
  showCompanySelector = false, 
  actions 
}) => {
  const router = useRouter();
  const { isSuperAdmin } = useRBAC();
  const { t } = useTranslation();

  // Fonction pour déduire le titre depuis la route
  const getPageTitleFromPath = (path: string): string => {
    const config = routeConfig[path];
    if (config) {
      return t(config.title, config.title.split('.').pop() || 'Page');
    }

    // Gestion des routes dynamiques
    const pathParts = path.split('/').filter(Boolean);
    if (pathParts.length > 0) {
      const lastPart = pathParts[pathParts.length - 1];
      return lastPart.charAt(0).toUpperCase() + lastPart.slice(1).replace(/-/g, ' ');
    }
    
    return t('common.unknownPage', 'Page Inconnue');
  };

  // Déterminer si on doit afficher le sélecteur de compagnie
  const shouldShowCompanySelector = showCompanySelector || 
    (isSuperAdmin() && routeConfig[router.pathname]?.showCompanySelector);

  const currentTitle = pageTitle || getPageTitleFromPath(router.pathname);

  return (
    <header className="sticky top-0 z-50 w-full border-b border-border/40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="w-full px-2 sm:px-4 lg:px-6">
        <div className="flex h-14 items-center justify-between">
          {/* Titre de la page */}
          <div className="flex items-center space-x-4">
            <h1 className="text-lg font-semibold">{currentTitle}</h1>
            {shouldShowCompanySelector && (
              <div className="hidden sm:block">
                <CompanySelector />
              </div>
            )}
          </div>

          {/* Actions et navigation utilisateur */}
          <div className="flex items-center space-x-4">
            {/* Actions personnalisées */}
            {actions && (
              <div className="flex items-center space-x-2">
                {actions}
              </div>
            )}

            {/* Sélecteur de compagnie mobile */}
            {shouldShowCompanySelector && (
              <div className="sm:hidden">
                <CompanySelector />
              </div>
            )}

            {/* Navigation utilisateur */}
            <UserNav />
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
