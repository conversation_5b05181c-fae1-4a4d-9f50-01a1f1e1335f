import { NextRequest, NextResponse } from 'next/server';
import { getAuth } from 'firebase-admin/auth';
import { getFirestore } from 'firebase-admin/firestore';
import { initializeApp, getApps, cert } from 'firebase-admin/app';

// Initialize Firebase Admin SDK
if (!getApps().length) {
  const serviceAccount = {
    type: "service_account",
    project_id: process.env.FIREBASE_PROJECT_ID,
    private_key_id: process.env.FIREBASE_PRIVATE_KEY_ID,
    private_key: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
    client_email: process.env.FIREBASE_CLIENT_EMAIL,
    client_id: process.env.FIREBASE_CLIENT_ID,
    auth_uri: "https://accounts.google.com/o/oauth2/auth",
    token_uri: "https://oauth2.googleapis.com/token",
    auth_provider_x509_cert_url: "https://www.googleapis.com/oauth2/v1/certs",
    client_x509_cert_url: `https://www.googleapis.com/robot/v1/metadata/x509/${process.env.FIREBASE_CLIENT_EMAIL}`
  };

  initializeApp({
    credential: cert(serviceAccount as any),
    projectId: process.env.FIREBASE_PROJECT_ID,
  });
}

const auth = getAuth();
const db = getFirestore();

export async function GET(request: NextRequest) {
  try {
    console.log('🏢 Setup Test Company: Creating test company for development');
    
    // Check if test company already exists
    const existingCompanies = await db.collection('companies')
      .where('name', '==', 'WePaie Test Company')
      .get();

    if (!existingCompanies.empty) {
      const existingCompany = existingCompanies.docs[0];
      console.log('🏢 Setup Test Company: Test company already exists:', existingCompany.id);
      return NextResponse.json({
        success: true,
        message: 'Test company already exists',
        company: {
          id: existingCompany.id,
          ...existingCompany.data()
        }
      });
    }

    // Create test company
    const companyData = {
      name: 'WePaie Test Company',
      description: 'Entreprise de test pour le développement WePaie',
      address: '123 Rue de Test, Casablanca, Maroc',
      phoneNumber: '+212 5 22 00 00 00',
      email: '<EMAIL>',
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
      settings: {
        currency: 'MAD',
        timezone: 'Africa/Casablanca',
        language: 'fr'
      }
    };

    const companyRef = await db.collection('companies').add(companyData);
    console.log('✅ Setup Test Company: Created test company:', companyRef.id);

    // Create some test services
    const services = [
      {
        name: 'Développement',
        description: 'Équipe de développement logiciel',
        order: 1,
        isActive: true,
        companyId: companyRef.id,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        name: 'Marketing',
        description: 'Équipe marketing et communication',
        order: 2,
        isActive: true,
        companyId: companyRef.id,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        name: 'Ressources Humaines',
        description: 'Gestion des ressources humaines',
        order: 3,
        isActive: true,
        companyId: companyRef.id,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ];

    const servicePromises = services.map(service => 
      db.collection('services').add(service)
    );
    const serviceRefs = await Promise.all(servicePromises);
    console.log('✅ Setup Test Company: Created test services:', serviceRefs.length);

    // Create some test positions
    const positions = [
      {
        name: 'Développeur Frontend',
        description: 'Développement d\'interfaces utilisateur',
        serviceId: serviceRefs[0].id,
        order: 1,
        isActive: true,
        companyId: companyRef.id,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        name: 'Développeur Backend',
        description: 'Développement d\'APIs et services',
        serviceId: serviceRefs[0].id,
        order: 2,
        isActive: true,
        companyId: companyRef.id,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        name: 'Chef de Projet Marketing',
        description: 'Gestion des projets marketing',
        serviceId: serviceRefs[1].id,
        order: 1,
        isActive: true,
        companyId: companyRef.id,
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        name: 'Responsable RH',
        description: 'Gestion des ressources humaines',
        serviceId: serviceRefs[2].id,
        order: 1,
        isActive: true,
        companyId: companyRef.id,
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ];

    const positionPromises = positions.map(position => 
      db.collection('positions').add(position)
    );
    const positionRefs = await Promise.all(positionPromises);
    console.log('✅ Setup Test Company: Created test positions:', positionRefs.length);

    return NextResponse.json({
      success: true,
      message: 'Test company and organizational data created successfully',
      company: {
        id: companyRef.id,
        ...companyData
      },
      stats: {
        services: serviceRefs.length,
        positions: positionRefs.length
      }
    });

  } catch (error) {
    console.error('❌ Setup Test Company: Error:', error);
    return NextResponse.json({ 
      error: 'Setup failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
