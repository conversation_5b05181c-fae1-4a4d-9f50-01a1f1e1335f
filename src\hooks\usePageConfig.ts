"use client";

import { useEffect, useState } from 'react';
import { getPageConfig, getTitleFromPath, type PageConfig } from '@/lib/page-config';
import { useTranslation } from '@/context/i18nContext';

/**
 * Hook pour obtenir la configuration de la page actuelle
 * Inspiré de votre approche de gestion centralisée des pages
 */
export function usePageConfig(overridePath?: string) {
  const { t } = useTranslation();
  const [currentPath, setCurrentPath] = useState('/');
  const [config, setConfig] = useState<PageConfig>({ title: 'common.loading' });

  useEffect(() => {
    if (typeof window !== 'undefined') {
      const path = overridePath || window.location.pathname;
      setCurrentPath(path);
      setConfig(getPageConfig(path));
    }
  }, [overridePath]);

  // Fonctions utilitaires
  const getTitle = (fallback?: string) => {
    const titleKey = config.title;
    const fallbackTitle = fallback || getTitleFromPath(currentPath);
    return titleKey.includes('.') ? t(titleKey, fallbackTitle) : titleKey;
  };

  const getDescription = () => {
    if (!config.description) return undefined;
    return config.description.includes('.') 
      ? t(config.description, config.description) 
      : config.description;
  };

  return {
    config,
    currentPath,
    title: getTitle(),
    description: getDescription(),
    requireAuth: config.requireAuth ?? true,
    requireCompany: config.requireCompany ?? false,
    showCompanySelector: config.showCompanySelector ?? false,
  };
}
