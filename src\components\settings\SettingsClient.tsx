"use client";

import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { useTranslation, type Locale } from '@/context/i18nContext';
import { useTheme, type BaseTheme } from '@/context/ThemeContext';
import { themeMetadata } from '@/themes';
import { useFonts } from '@/context/FontContext';
import { useToast } from '@/hooks/use-toast';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form';
import {
  Globe,
  Lock,
  Type,
  Palette,
  Eye,
  EyeOff,
  Save,
  Loader2
} from 'lucide-react';

// Password change schema - will be created with translations inside component
const createPasswordSchema = (t: (key: string, fallback?: string) => string) => z.object({
  currentPassword: z.string().min(1, t('settings.validation.currentPasswordRequired', 'Le mot de passe actuel est requis')),
  newPassword: z.string()
    .min(8, t('settings.validation.newPasswordMinLength', 'Le nouveau mot de passe doit contenir au moins 8 caractères'))
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, t('settings.validation.newPasswordComplexity', 'Le mot de passe doit contenir au moins une minuscule, une majuscule et un chiffre')),
  confirmPassword: z.string()
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: t('settings.validation.passwordsNoMatch', "Les mots de passe ne correspondent pas"),
  path: ["confirmPassword"],
});

type PasswordFormData = {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
};

// Font options - labels will be translated in the component

// Text sizes will be translated in the component

/**
 * Settings Client Component
 * 
 * Direct settings page with two main sections:
 * 1. Application Preferences (language, text size, fonts, theme)
 * 2. Password Management (change password functionality)
 * 
 * Features:
 * - Real-time language switching
 * - Font family selection for Latin and Arabic scripts
 * - Text size adjustment
 * - Light/Dark mode toggle
 * - Secure password change with validation
 * - Form validation using React Hook Form and Zod
 * - Toast notifications for user feedback
 */
export function SettingsClient() {
  const { t, language, setLanguage } = useTranslation();
  const { currentThemeName, currentMode, setTheme, setMode, isDarkMode } = useTheme();
  // Compatibilité avec l'ancien code
  const baseTheme = currentThemeName;
  const mode = currentMode;
  const setBaseTheme = setTheme;
  const { applyFonts } = useFonts();
  const { toast } = useToast();
  const [isChangingPassword, setIsChangingPassword] = useState(false);
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  // Check if this is a first-time login
  const [isFirstLogin, setIsFirstLogin] = useState(false);

  useEffect(() => {
    // Check URL parameters for first login
    const urlParams = new URLSearchParams(window.location.search);
    const firstLogin = urlParams.get('firstLogin') === 'true';
    setIsFirstLogin(firstLogin);

    if (firstLogin) {
      toast({
        title: t('settings.password.welcome', 'Bienvenue!'),
        description: t('settings.password.welcomeDescription', 'Veuillez changer votre mot de passe initial pour sécuriser votre compte.'),
        duration: 5000
      });
    }
  }, [toast]);

  // Local state for preferences with immediate persistence
  const [textSize, setTextSizeState] = useState('medium');
  const [latinFont, setLatinFontState] = useState('roboto');
  const [arabicFont, setArabicFontState] = useState('noto-arabic');
  const [fontStyle, setFontStyleState] = useState('normal');

  // Load preferences from localStorage on mount
  useEffect(() => {
    const storedTextSize = localStorage.getItem('wepaie_text_size') || 'medium';
    const storedLatinFont = localStorage.getItem('wepaie_latin_font') || 'roboto';
    const storedArabicFont = localStorage.getItem('wepaie_arabic_font') || 'noto-arabic';
    const storedFontStyle = localStorage.getItem('wepaie_font_style') || 'normal';

    setTextSizeState(storedTextSize);
    setLatinFontState(storedLatinFont);
    setArabicFontState(storedArabicFont);
    setFontStyleState(storedFontStyle);
  }, []);

  // Immediate setters with persistence
  const setTextSize = (size: string) => {
    setTextSizeState(size);
    localStorage.setItem('wepaie_text_size', size);
    // Apply text size immediately using FontProvider
    applyFonts();
  };

  const setLatinFont = (font: string) => {
    setLatinFontState(font);
    localStorage.setItem('wepaie_latin_font', font);
    // Apply font immediately using FontProvider
    applyFonts();
  };

  const setArabicFont = (font: string) => {
    setArabicFontState(font);
    localStorage.setItem('wepaie_arabic_font', font);
    // Apply font immediately using FontProvider
    applyFonts();
  };

  const setFontStyle = (style: string) => {
    setFontStyleState(style);
    localStorage.setItem('wepaie_font_style', style);
    // Apply font style immediately using FontProvider
    applyFonts();
  };

  // Apply settings on mount using FontProvider
  useEffect(() => {
    applyFonts();
  }, [textSize, latinFont, arabicFont, applyFonts]);

  // Password form with dynamic schema
  const passwordSchema = createPasswordSchema(t);
  const passwordForm = useForm<PasswordFormData>({
    resolver: zodResolver(passwordSchema),
    defaultValues: {
      currentPassword: '',
      newPassword: '',
      confirmPassword: ''
    }
  });

  // Auto-focus on current password field for first-time login
  useEffect(() => {
    if (isFirstLogin) {
      // Focus on the current password field after a short delay
      setTimeout(() => {
        const currentPasswordInput = document.querySelector('input[name="currentPassword"]') as HTMLInputElement;
        if (currentPasswordInput) {
          currentPasswordInput.focus();
        }
      }, 500);
    }
  }, [isFirstLogin]);

  const handlePasswordChange = async (data: PasswordFormData) => {
    setIsChangingPassword(true);
    try {
      // Import Firebase auth functions
      const { updatePassword, reauthenticateWithCredential, EmailAuthProvider, signOut } = await import('firebase/auth');
      const { auth } = await import('@/lib/firebase');

      const currentUser = auth.currentUser;
      if (!currentUser) {
        throw new Error(t('settings.password.errorUserNotFound', 'Utilisateur non connecté'));
      }

      console.log('🔐 Password change: Starting for user:', currentUser.email);
      console.log('🔐 Password change: User metadata:', {
        creationTime: currentUser.metadata.creationTime,
        lastSignInTime: currentUser.metadata.lastSignInTime,
        emailVerified: currentUser.emailVerified,
        uid: currentUser.uid,
        providerId: currentUser.providerId,
        providerData: currentUser.providerData.map(p => ({ providerId: p.providerId, uid: p.uid }))
      });
      console.log('🔐 Password change: Current password length:', data.currentPassword.length);
      console.log('🔐 Password change: New password length:', data.newPassword.length);

      // Check if this is a recently created user (within last 5 minutes)
      const creationTime = new Date(currentUser.metadata.creationTime!);
      const now = new Date();
      const timeSinceCreation = now.getTime() - creationTime.getTime();
      const isRecentlyCreated = timeSinceCreation < 5 * 60 * 1000; // 5 minutes

      console.log('🔐 Password change: User creation time:', creationTime);
      console.log('🔐 Password change: Time since creation (ms):', timeSinceCreation);
      console.log('🔐 Password change: Is recently created:', isRecentlyCreated);

      if (!isRecentlyCreated) {
        // Re-authenticate user with current password for older accounts
        console.log('🔐 Password change: Creating credential for re-authentication');
        const credential = EmailAuthProvider.credential(currentUser.email!, data.currentPassword);

        console.log('🔐 Password change: Attempting re-authentication');
        await reauthenticateWithCredential(currentUser, credential);
        console.log('✅ Password change: Re-authentication successful');
      } else {
        console.log('🔐 Password change: Skipping re-authentication for recently created user');
      }

      // Update password
      console.log('🔐 Password change: Updating password');
      await updatePassword(currentUser, data.newPassword);
      console.log('✅ Password change: Password updated successfully');

      toast({
        title: t('settings.password.successTitle', 'Succès'),
        description: t('settings.password.successDescription', 'Votre mot de passe a été modifié avec succès. Vous allez être déconnecté.')
      });

      passwordForm.reset();

      // Sign out user after password change
      setTimeout(async () => {
        await signOut(auth);
        window.location.href = '/login';
      }, 2000);

    } catch (error: any) {
      console.error('❌ Password change error:', error);
      console.error('❌ Error code:', error.code);
      console.error('❌ Error message:', error.message);

      let errorMessage = t('settings.password.errorDefault', 'Impossible de modifier le mot de passe. Veuillez réessayer.');

      if (error.code === 'auth/wrong-password' || error.code === 'auth/invalid-credential') {
        errorMessage = t('settings.password.errorWrongPassword', 'Le mot de passe actuel est incorrect. Vérifiez que vous utilisez le bon mot de passe.');
      } else if (error.code === 'auth/weak-password') {
        errorMessage = t('settings.password.errorWeakPassword', 'Le nouveau mot de passe est trop faible.');
      } else if (error.code === 'auth/requires-recent-login') {
        errorMessage = t('settings.password.errorRecentLogin', 'Veuillez vous reconnecter avant de changer votre mot de passe.');
      } else if (error.code === 'auth/user-mismatch') {
        errorMessage = t('settings.password.errorUserMismatch', 'Erreur d\'authentification. Veuillez vous reconnecter.');
      } else if (error.code === 'auth/user-not-found') {
        errorMessage = t('settings.password.errorUserNotFound', 'Utilisateur non trouvé. Veuillez vous reconnecter.');
      } else if (error.code === 'auth/invalid-email') {
        errorMessage = t('settings.password.errorInvalidEmail', 'Adresse email invalide.');
      }

      toast({
        title: t('settings.password.errorTitle', 'Erreur'),
        description: errorMessage,
        variant: 'destructive'
      });
    } finally {
      setIsChangingPassword(false);
    }
  };

  return (
    <>
      {/* Page header */}
      <div className="border-b border-border pb-4">
        <h1 className="text-2xl font-bold">Paramètres</h1>
        <p className="text-muted-foreground">Personnalisez votre expérience et gérez votre compte</p>
      </div>

      {/* Section 1: Language & Typography Preferences */}
      <Card>
        <CardHeader className="p-4 pb-3">
          <CardTitle className="flex items-center gap-2 text-lg">
            <Globe className="h-5 w-5" />
            Langue et Typographie
          </CardTitle>
        </CardHeader>
        <CardContent className="p-4 pt-0">
          {/* Typography Controls - Single Row */}
          <div className="grid gap-4 grid-cols-1 md:grid-cols-2 lg:grid-cols-4">
            {/* Language Selection */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">{t('settings.language')}</Label>
              <Select value={language} onValueChange={(value: Locale) => setLanguage(value)}>
                <SelectTrigger className="h-9">
                  <SelectValue placeholder={t('common.select')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="fr">
                    <div className="flex items-center gap-2">
                      <span>🇫🇷</span>
                      <span>Français</span>
                    </div>
                  </SelectItem>
                  <SelectItem value="en">
                    <div className="flex items-center gap-2">
                      <span>🇺🇸</span>
                      <span>English</span>
                    </div>
                  </SelectItem>
                  <SelectItem value="ar">
                    <div className="flex items-center gap-2">
                      <span>🇸🇦</span>
                      <span>العربية</span>
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Font Family Selection */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">{t('settings.fontFamily')}</Label>
              <Select
                value={language === 'ar' ? arabicFont : latinFont}
                onValueChange={language === 'ar' ? setArabicFont : setLatinFont}
              >
                <SelectTrigger className="h-9">
                  <SelectValue placeholder={t('common.select')} />
                </SelectTrigger>
                <SelectContent>
                  {language === 'ar' ? (
                    <>
                      <SelectItem value="noto-arabic">
                        <span style={{ fontFamily: 'Noto Sans Arabic, sans-serif' }}>Noto Sans Arabic</span>
                      </SelectItem>
                      <SelectItem value="cairo">
                        <span style={{ fontFamily: 'Cairo, sans-serif' }}>Cairo</span>
                      </SelectItem>
                      <SelectItem value="amiri">
                        <span style={{ fontFamily: 'Amiri, serif' }}>Amiri</span>
                      </SelectItem>
                    </>
                  ) : (
                    <>
                      <SelectItem value="roboto">
                        <span style={{ fontFamily: 'Roboto, sans-serif' }}>Roboto</span>
                      </SelectItem>
                      <SelectItem value="inter">
                        <span style={{ fontFamily: 'Inter, sans-serif' }}>Inter</span>
                      </SelectItem>
                      <SelectItem value="opensans">
                        <span style={{ fontFamily: 'Open Sans, sans-serif' }}>Open Sans</span>
                      </SelectItem>
                    </>
                  )}
                </SelectContent>
              </Select>
            </div>

            {/* Text Size */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">{t('settings.textSize')}</Label>
              <Select value={textSize} onValueChange={setTextSize}>
                <SelectTrigger className="h-9">
                  <SelectValue placeholder={t('common.select')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="small">
                    <span className="text-sm">{t('size.small')}</span>
                  </SelectItem>
                  <SelectItem value="medium">
                    <span className="text-base">{t('size.medium')}</span>
                  </SelectItem>
                  <SelectItem value="large">
                    <span className="text-lg">{t('size.large')}</span>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Font Style */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">{t('settings.fontStyle')}</Label>
              <Select value={fontStyle} onValueChange={setFontStyle}>
                <SelectTrigger className="h-9">
                  <SelectValue placeholder={t('common.select')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="normal">
                    <span className="font-normal">{t('style.normal')}</span>
                  </SelectItem>
                  <SelectItem value="medium">
                    <span className="font-medium">{t('style.medium')}</span>
                  </SelectItem>
                  <SelectItem value="bold">
                    <span className="font-bold">{t('style.bold')}</span>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Section 2: Theme & Display Mode */}
      <Card>
        <CardHeader className="p-4 pb-3">
          <CardTitle className="flex items-center gap-2 text-lg">
            <Palette className="h-5 w-5" />
            Thème et Affichage
          </CardTitle>
        </CardHeader>
        <CardContent className="p-4 pt-0 space-y-4">
          {/* Theme Selection */}
          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <Label className="text-sm font-medium">Thème de base</Label>
              <Select value={baseTheme} onValueChange={(value) => setBaseTheme(value as any)}>
                <SelectTrigger className="h-9">
                  <SelectValue placeholder="Sélectionner un thème" />
                </SelectTrigger>
                <SelectContent>
                  {Object.entries(themeMetadata).map(([key, meta]) => (
                    <SelectItem key={key} value={key}>
                      <div className="flex items-center gap-2">
                        <div
                          className="w-3 h-3 rounded-sm border"
                          style={{ backgroundColor: meta.color }}
                        />
                        <span>{meta.name}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Light/Dark Mode Toggle */}
            <div className="space-y-2">
              <Label className="text-sm font-medium">Mode d'affichage</Label>
              <div className="flex items-center justify-between h-9 px-3 border rounded-md">
                <div className="flex items-center gap-2">
                  {isDarkMode ? (
                    <div className="w-4 h-4 bg-slate-800 rounded border flex items-center justify-center">
                      <div className="w-1.5 h-1.5 bg-yellow-400 rounded-full" />
                    </div>
                  ) : (
                    <div className="w-4 h-4 bg-yellow-100 rounded border border-yellow-300 flex items-center justify-center">
                      <div className="w-2 h-2 bg-yellow-400 rounded-full" />
                    </div>
                  )}
                  <span className="text-sm">
                    {isDarkMode ? 'Mode sombre' : 'Mode clair'}
                  </span>
                </div>
                <Switch
                  checked={isDarkMode}
                  onCheckedChange={(checked) => setMode(checked ? 'dark' : 'light')}
                />
              </div>
            </div>
          </div>

          <p className="text-xs text-muted-foreground">
            Les modifications sont appliquées immédiatement et sauvegardées automatiquement.
          </p>
        </CardContent>
      </Card>

      {/* Section 3: Password Management */}
      <Card className={isFirstLogin ? 'ring-2 ring-primary ring-offset-2' : ''}>
        <CardHeader className="p-4 pb-3">
          <CardTitle className="flex items-center gap-2 text-lg">
            <Lock className="h-5 w-5" />
            Gestion du Mot de Passe
            {isFirstLogin && (
              <span className="text-xs bg-primary text-primary-foreground px-2 py-1 rounded-full">
                Requis
              </span>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent className="p-4 pt-0">
          <Form {...passwordForm}>
            <form onSubmit={passwordForm.handleSubmit(handlePasswordChange)} className="space-y-4">
              <FormField
                control={passwordForm.control}
                name="currentPassword"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sm font-medium">Mot de passe actuel</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Input
                          type={showCurrentPassword ? "text" : "password"}
                          placeholder="Entrez votre mot de passe actuel"
                          className="h-9 pr-10"
                          {...field}
                        />
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          className="absolute right-0 top-0 h-9 w-9 p-0 hover:bg-transparent"
                          onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                        >
                          {showCurrentPassword ? (
                            <EyeOff className="h-4 w-4" />
                          ) : (
                            <Eye className="h-4 w-4" />
                          )}
                        </Button>
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={passwordForm.control}
                name="newPassword"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sm font-medium">Nouveau mot de passe</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Input
                          type={showNewPassword ? "text" : "password"}
                          placeholder="Entrez votre nouveau mot de passe"
                          className="h-9 pr-10"
                          {...field}
                        />
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          className="absolute right-0 top-0 h-9 w-9 p-0 hover:bg-transparent"
                          onClick={() => setShowNewPassword(!showNewPassword)}
                        >
                          {showNewPassword ? (
                            <EyeOff className="h-4 w-4" />
                          ) : (
                            <Eye className="h-4 w-4" />
                          )}
                        </Button>
                      </div>
                    </FormControl>
                    <FormDescription className="text-xs">
                      Au moins 8 caractères avec une minuscule, une majuscule et un chiffre
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={passwordForm.control}
                name="confirmPassword"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sm font-medium">Confirmer le nouveau mot de passe</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Input
                          type={showConfirmPassword ? "text" : "password"}
                          placeholder="Confirmez votre nouveau mot de passe"
                          className="h-9 pr-10"
                          {...field}
                        />
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          className="absolute right-0 top-0 h-9 w-9 p-0 hover:bg-transparent"
                          onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                        >
                          {showConfirmPassword ? (
                            <EyeOff className="h-4 w-4" />
                          ) : (
                            <Eye className="h-4 w-4" />
                          )}
                        </Button>
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="flex justify-end pt-3">
                <Button
                  type="submit"
                  disabled={isChangingPassword}
                  className="min-w-[160px]"
                >
                  {isChangingPassword ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Modification...
                    </>
                  ) : (
                    <>
                      <Lock className="mr-2 h-4 w-4" />
                      Changer le mot de passe
                    </>
                  )}
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </>
  );
}
