import type { Metadata } from 'next';
import AppShell from '@/components/layout/AppShell';
import { OrganizationalManagement } from '@/components/admin/OrganizationalManagement';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';

export const metadata: Metadata = {
  title: 'Administration',
};

export default function AdminPage() {
  return (
    <ProtectedRoute requireAuth={true} requireAdmin={true}>
      <AppShell pageTitle="Administration">
        <OrganizationalManagement />
      </AppShell>
    </ProtectedRoute>
  );
}
