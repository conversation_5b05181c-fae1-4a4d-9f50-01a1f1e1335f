// Thème Warm - Mode clair
export const warmLight = {
  '--background': '35 25% 97%', // Warm cream background
  '--foreground': '25 20% 15%', // Dark brown text
  
  '--card': '0 0% 100%', // White cards
  '--card-foreground': '25 20% 15%',
  
  '--popover': '0 0% 100%',
  '--popover-foreground': '25 20% 15%',
  
  '--primary': '25 70% 55%', // Warm brown #B46617
  '--primary-foreground': '0 0% 98%',
  
  '--secondary': '45 40% 90%', // Light warm beige
  '--secondary-foreground': '25 20% 25%',
  
  '--muted': '45 40% 90%',
  '--muted-foreground': '25 15% 45%',
  
  '--accent': '45 90% 65%', // Golden yellow #FFBA00
  '--accent-foreground': '25 20% 15%',
  
  '--destructive': '0 84.2% 60.2%',
  '--destructive-foreground': '0 0% 98%',
  
  '--border': '35 30% 85%',
  '--input': '35 30% 85%',
  '--ring': '45 90% 65%',
  
  '--radius': '0.375rem',
  
  '--chart-1': '25 70% 55%',
  '--chart-2': '45 90% 65%',
  '--chart-3': '35 30% 60%',
  '--chart-4': '45 50% 70%',
  '--chart-5': '35 60% 65%',
  
  // Sidebar colors for warm theme
  '--sidebar-background': '35 30% 94%',
  '--sidebar-foreground': '25 20% 25%',
  '--sidebar-primary': '25 70% 55%',
  '--sidebar-primary-foreground': '0 0% 98%',
  '--sidebar-accent': '35 25% 88%',
  '--sidebar-accent-foreground': '25 70% 45%',
  '--sidebar-border': '35 30% 85%',
  '--sidebar-ring': '45 90% 65%',
};
