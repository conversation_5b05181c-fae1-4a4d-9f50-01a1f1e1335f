/**
 * RBAC Module Index
 * 
 * Exports all RBAC-related types, functions, and services
 * for easy importing throughout the application.
 */

// Types
export type {
  UserRole,
  Permission,
  PermissionCategory,
  CustomClaims,
  RBACUser,
  Company,
  RoleConfig,
  PermissionContext,
  PermissionResult,
  CreateUserRequest,
  UpdateUserRequest,
  CreateCompanyRequest,
  UpdateCompanyRequest,
  AuditLogEntry,
  RBACNavItem,
  PageAccess
} from './types';

// Role definitions and configurations
export {
  ROLE_PERMISSIONS,
  ROLE_CONFIGS,
  PAGE_ACCESS,
  RBAC_NAV_ITEMS,
  getRoleConfig,
  getRolePermissions,
  canManageRole,
  isHigherRole,
  getAvailableRoles,
  ROLE_NAMES_FR,
  ROLE_DESCRIPTIONS_FR,
  PERMISSION_NAMES_FR
} from './roles';

// Permission checking functions
export {
  hasPermission,
  hasAnyPermission,
  hasAllPermissions,
  canAccessCompany,
  canManageUser,
  checkPermission,
  canPerformAction,
  filterByPermission,
  hasRoleOrHigher,
  getPermissionsFromClaims,
  isSameCompanyOrSuperAdmin,
  canCreateUserWithRole,
  getAccessibleCompanies
} from './permissions';

// User management service
export {
  createUser,
  updateUser,
  deleteUser,
  getUserById,
  getCompanyUsers,
  getUsersByRole,
  searchUsers,
  toggleUserStatus,
  sendUserPasswordReset,
  getUserStats
} from './userService';

// Company management service
export {
  createCompany,
  updateCompany,
  deleteCompany,
  getCompanyById,
  getAllCompanies,
  getActiveCompanies,
  searchCompanies,
  toggleCompanyStatus,
  getCompanyStats,
  getCompanyUserCount,
  canAddUser,
  updateSubscriptionStatus,
  getCompaniesBySubscription,
  initializeDemoCompany
} from './companyService';

// Context and hooks are exported from their respective files
// Import them directly from @/context/RBACContext

// Constants for easy access
export const SUPER_ADMIN_EMAIL = '<EMAIL>';
export const DEMO_ADMIN_EMAIL = '<EMAIL>';

// Helper functions
export function isValidRole(role: string): role is UserRole {
  return ['super_admin', 'company_admin', 'editor', 'viewer'].includes(role);
}

export function isValidPermission(permission: string): permission is Permission {
  const validPermissions = [
    'users.create', 'users.read', 'users.update', 'users.delete', 'users.assign_roles',
    'companies.create', 'companies.read', 'companies.update', 'companies.delete', 'companies.manage_settings',
    'employees.create', 'employees.read', 'employees.update', 'employees.delete',
    'time.create', 'time.read', 'time.update', 'time.delete',
    'payroll.create', 'payroll.read', 'payroll.update', 'payroll.delete', 'payroll.calculate',
    'settings.read', 'settings.update', 'settings.system',
    'audit.read', 'audit.export',
    'analytics.read', 'analytics.export', 'analytics.global'
  ];
  return validPermissions.includes(permission);
}

export function getRoleHierarchyLevel(role: UserRole): number {
  const hierarchy = {
    viewer: 1,
    editor: 2,
    company_admin: 3,
    super_admin: 4
  };
  return hierarchy[role] || 0;
}

export function canRoleManageRole(managerRole: UserRole, targetRole: UserRole): boolean {
  const managerLevel = getRoleHierarchyLevel(managerRole);
  const targetLevel = getRoleHierarchyLevel(targetRole);
  
  // Super admin can manage all roles
  if (managerRole === 'super_admin') return true;
  
  // Company admin can manage editor and viewer
  if (managerRole === 'company_admin') {
    return ['editor', 'viewer'].includes(targetRole);
  }
  
  // Others cannot manage roles
  return false;
}

export function getDefaultRedirectForRole(role: UserRole): string {
  switch (role) {
    case 'super_admin':
      return '/super-admin';
    case 'company_admin':
      return '/admin';
    case 'editor':
      return '/employees';
    case 'viewer':
      return '/dashboard';
    default:
      return '/dashboard';
  }
}

export function formatRoleName(role: UserRole, language: 'en' | 'fr' = 'fr'): string {
  if (language === 'fr') {
    return ROLE_NAMES_FR[role] || role;
  }
  
  const englishNames = {
    super_admin: 'Super Administrator',
    company_admin: 'Company Administrator',
    editor: 'Editor',
    viewer: 'Viewer'
  };
  
  return englishNames[role] || role;
}

export function formatPermissionName(permission: Permission, language: 'en' | 'fr' = 'fr'): string {
  if (language === 'fr') {
    return PERMISSION_NAMES_FR[permission] || permission;
  }
  
  // Convert permission to readable English
  const [resource, action] = permission.split('.');
  const resourceName = resource.charAt(0).toUpperCase() + resource.slice(1);
  const actionName = action.charAt(0).toUpperCase() + action.slice(1);
  
  return `${actionName} ${resourceName}`;
}

// Validation helpers
export function validateUserData(userData: Partial<RBACUser>): string[] {
  const errors: string[] = [];
  
  if (!userData.email || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(userData.email)) {
    errors.push('Email address is invalid');
  }
  
  if (!userData.firstName || userData.firstName.length < 2) {
    errors.push('First name must be at least 2 characters');
  }
  
  if (!userData.lastName || userData.lastName.length < 2) {
    errors.push('Last name must be at least 2 characters');
  }
  
  if (!userData.role || !isValidRole(userData.role)) {
    errors.push('Role is required and must be valid');
  }
  
  return errors;
}

export function validateCompanyData(companyData: Partial<Company>): string[] {
  const errors: string[] = [];
  
  if (!companyData.name || companyData.name.length < 2) {
    errors.push('Company name must be at least 2 characters');
  }
  
  if (!companyData.email || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(companyData.email)) {
    errors.push('Company email address is invalid');
  }
  
  return errors;
}

// Migration helpers for transitioning from demo system
export function migrateFromDemoSystem() {
  // This would contain logic to migrate from the old demo admin system
  // to the new RBAC system
  console.log('Migration from demo system would be implemented here');
}

export function createSuperAdminUser() {
  // This would contain logic to create the initial super admin user
  // This should be done via Firebase Console or Cloud Functions
  console.log('Super admin user creation would be implemented here');
}
