"use client";

import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, XCircle, Loader2, Database, Shield, Settings } from 'lucide-react';
import { db } from '@/lib/firebase';
import { collection, doc, setDoc, getDoc, deleteDoc } from 'firebase/firestore';
import { companyService, initializeCompany } from '@/lib/firestore';

interface TestResult {
  name: string;
  status: 'pending' | 'success' | 'error';
  message: string;
  details?: string;
}

export function FirebaseConnectionTest() {
  const [tests, setTests] = useState<TestResult[]>([
    { name: 'Firebase Configuration', status: 'pending', message: 'Checking configuration...' },
    { name: 'Firestore Connection', status: 'pending', message: 'Testing connection...' },
    { name: 'Security Rules', status: 'pending', message: 'Testing security rules...' },
    { name: 'Company Service', status: 'pending', message: 'Testing company operations...' },
  ]);
  
  const [isRunning, setIsRunning] = useState(false);
  const [testCompanyId, setTestCompanyId] = useState<string | null>(null);

  const updateTest = (index: number, updates: Partial<TestResult>) => {
    setTests(prev => prev.map((test, i) => i === index ? { ...test, ...updates } : test));
  };

  const runTests = async () => {
    setIsRunning(true);
    
    try {
      // Test 1: Firebase Configuration
      updateTest(0, { status: 'pending', message: 'Checking Firebase configuration...' });
      
      const config = {
        apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
        projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
        authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
      };
      
      if (!config.apiKey || !config.projectId || !config.authDomain) {
        updateTest(0, { 
          status: 'error', 
          message: 'Missing Firebase configuration',
          details: 'Check your .env.local file'
        });
        return;
      }
      
      updateTest(0, { 
        status: 'success', 
        message: `Connected to project: ${config.projectId}`,
        details: `Auth domain: ${config.authDomain}`
      });

      // Test 2: Firestore Connection
      updateTest(1, { status: 'pending', message: 'Testing Firestore connection...' });

      try {
        // Test Firestore connection with a development test document
        const testDocRef = doc(collection(db, 'connection_test'), 'test');
        await setDoc(testDocRef, {
          timestamp: new Date(),
          test: 'connection_test',
          source: 'WePaie_Connection_Test'
        });

        const testDoc = await getDoc(testDocRef);
        if (testDoc.exists()) {
          await deleteDoc(testDocRef); // Clean up
          updateTest(1, {
            status: 'success',
            message: 'Firestore connection successful',
            details: 'Read/write operations working with security rules'
          });
        } else {
          throw new Error('Document not found after creation');
        }
      } catch (error: any) {
        if (error.code === 'permission-denied') {
          updateTest(1, {
            status: 'success',
            message: 'Firestore connection successful',
            details: 'Connected with security rules active (permission-denied expected)'
          });
        } else {
          updateTest(1, {
            status: 'error',
            message: 'Firestore connection failed',
            details: error.message
          });
          return;
        }
      }

      // Test 3: Security Rules (comprehensive test)
      updateTest(2, { status: 'pending', message: 'Testing security rules...' });

      try {
        // Test 1: Try to read a non-existent company (should be blocked)
        const protectedRef = doc(collection(db, 'companies'), 'unauthorized-test-company');
        try {
          await getDoc(protectedRef);
          // If this succeeds without authentication, security rules are not working
          updateTest(2, {
            status: 'error',
            message: 'Security rules may not be active',
            details: 'Unauthorized read access was allowed'
          });
          return;
        } catch (securityError: any) {
          if (securityError.code !== 'permission-denied') {
            throw securityError;
          }
        }

        // Test 2: Try to create an unauthorized document (should be blocked)
        const unauthorizedRef = doc(collection(db, 'companies'), 'unauthorized-company');
        try {
          await setDoc(unauthorizedRef, {
            name: 'Unauthorized Company',
            email: '<EMAIL>'
          });
          // If this succeeds, security rules are not working
          updateTest(2, {
            status: 'error',
            message: 'Security rules may not be active',
            details: 'Unauthorized write access was allowed'
          });
          return;
        } catch (securityError: any) {
          if (securityError.code !== 'permission-denied') {
            throw securityError;
          }
        }

        // If we get here, both unauthorized operations were properly blocked
        updateTest(2, {
          status: 'success',
          message: 'Security rules are active and working',
          details: 'Unauthorized read and write operations properly blocked'
        });

      } catch (error: any) {
        updateTest(2, {
          status: 'error',
          message: 'Security rules test failed',
          details: error.message
        });
        return;
      }

      // Test 4: Company Service
      updateTest(3, { status: 'pending', message: 'Testing company service...' });

      try {
        // Test company creation with development test data
        const testCompany = await initializeCompany({
          name: 'Test Company (WePaie)',
          email: '<EMAIL>',
          phone: '******-TEST',
          address: 'Test Address, Test City'
        });

        setTestCompanyId(testCompany.id);

        // Test company retrieval
        const retrievedCompany = await companyService.getById(testCompany.id);

        if (retrievedCompany && retrievedCompany.name === 'Test Company (WePaie)') {
          updateTest(3, {
            status: 'success',
            message: 'Company service working',
            details: `Test company created with ID: ${testCompany.id}`
          });
        } else {
          throw new Error('Company retrieval failed');
        }
      } catch (error: any) {
        updateTest(3, {
          status: 'error',
          message: 'Company service failed',
          details: error.message
        });
      }
      
    } catch (error: any) {
      console.error('Test suite error:', error);
    } finally {
      setIsRunning(false);
    }
  };

  const cleanupTestData = async () => {
    if (testCompanyId) {
      try {
        // Note: In a real app, you'd want a proper delete method
        // For now, we'll just note that test data exists
        console.log('Test company created:', testCompanyId);
      } catch (error) {
        console.error('Cleanup error:', error);
      }
    }
  };

  useEffect(() => {
    return () => {
      cleanupTestData();
    };
  }, [testCompanyId]);

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'pending':
        return <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />;
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" />;
    }
  };

  const getStatusBadge = (status: TestResult['status']) => {
    switch (status) {
      case 'pending':
        return <Badge variant="secondary">Pending</Badge>;
      case 'success':
        return <Badge variant="default" className="bg-green-500">Success</Badge>;
      case 'error':
        return <Badge variant="destructive">Error</Badge>;
    }
  };

  const allTestsPassed = tests.every(test => test.status === 'success');
  const hasErrors = tests.some(test => test.status === 'error');

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Database className="h-5 w-5" />
          Firebase Connection Test
        </CardTitle>
        <CardDescription>
          Test the connection to your production Firebase project and verify all services are working correctly.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-3">
          {tests.map((test, index) => (
            <div key={index} className="flex items-start gap-3 p-3 border rounded-lg">
              <div className="mt-0.5">
                {getStatusIcon(test.status)}
              </div>
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between gap-2">
                  <h4 className="font-medium">{test.name}</h4>
                  {getStatusBadge(test.status)}
                </div>
                <p className="text-sm text-muted-foreground mt-1">{test.message}</p>
                {test.details && (
                  <p className="text-xs text-muted-foreground mt-1 font-mono bg-muted p-1 rounded">
                    {test.details}
                  </p>
                )}
              </div>
            </div>
          ))}
        </div>

        <div className="flex gap-2">
          <Button 
            onClick={runTests} 
            disabled={isRunning}
            className="flex-1"
          >
            {isRunning ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Running Tests...
              </>
            ) : (
              <>
                <Settings className="mr-2 h-4 w-4" />
                Run Connection Tests
              </>
            )}
          </Button>
        </div>

        {allTestsPassed && !isRunning && (
          <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
            <div className="flex items-center gap-2 text-green-800">
              <CheckCircle className="h-5 w-5" />
              <h4 className="font-medium">All Tests Passed!</h4>
            </div>
            <p className="text-sm text-green-700 mt-1">
              Your WePaie application is successfully connected to the production Firebase project.
              You can now start using the application with real data.
            </p>
            {testCompanyId && (
              <p className="text-xs text-green-600 mt-2 font-mono">
                Test company ID: {testCompanyId}
              </p>
            )}
          </div>
        )}

        {hasErrors && !isRunning && (
          <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-center gap-2 text-red-800">
              <XCircle className="h-5 w-5" />
              <h4 className="font-medium">Some Tests Failed</h4>
            </div>
            <p className="text-sm text-red-700 mt-1">
              Please check the error details above and ensure your Firebase configuration is correct.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
