/**
 * RBAC Error Boundary Component
 * 
 * Handles errors in the RBAC system gracefully and provides
 * fallback UI when authentication or permission errors occur.
 */

'use client';

import React, { Component, ReactNode } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { AlertTriangle, RefreshCw } from 'lucide-react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
}

export class RBACErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    console.error('🔐 RBAC Error Boundary caught error:', error);
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('🔐 RBAC Error Boundary details:', error, errorInfo);
  }

  handleRetry = () => {
    this.setState({ hasError: false, error: undefined });
    window.location.reload();
  };

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <div className="min-h-screen flex items-center justify-center p-4">
          <Card className="w-full max-w-md">
            <CardHeader className="text-center">
              <div className="mx-auto mb-4 h-12 w-12 rounded-full bg-destructive/10 flex items-center justify-center">
                <AlertTriangle className="h-6 w-6 text-destructive" />
              </div>
              <CardTitle>Erreur d'authentification</CardTitle>
              <CardDescription>
                Une erreur s'est produite lors du chargement de vos informations d'authentification.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="text-sm text-muted-foreground">
                <p>Détails de l'erreur :</p>
                <code className="block mt-2 p-2 bg-muted rounded text-xs">
                  {this.state.error?.message || 'Erreur inconnue'}
                </code>
              </div>
              <div className="flex gap-2">
                <Button onClick={this.handleRetry} className="flex-1">
                  <RefreshCw className="mr-2 h-4 w-4" />
                  Réessayer
                </Button>
                <Button 
                  variant="outline" 
                  onClick={() => window.location.href = '/login'}
                  className="flex-1"
                >
                  Se reconnecter
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      );
    }

    return this.props.children;
  }
}

/**
 * Hook version of the error boundary for functional components
 */
export function useRBACErrorHandler() {
  const handleError = (error: Error) => {
    console.error('🔐 RBAC Error:', error);
    
    // For authentication errors, redirect to login
    if (error.message.includes('auth') || error.message.includes('permission')) {
      window.location.href = '/login';
      return;
    }
    
    // For other errors, show a toast or handle gracefully
    throw error;
  };

  return { handleError };
}
