/**
 * Jest Translation Coverage Test
 * 
 * Jest-compatible version of the translation coverage test
 */

const { 
  runTranslationCoverageTest, 
  compareTranslations, 
  loadTranslations,
  getAllKeys,
  hasNestedKey
} = require('./translation-coverage.test');

describe('Translation Coverage', () => {
  let report;

  beforeAll(() => {
    report = compareTranslations();
  });

  test('should have all translation files present', () => {
    const languages = ['en', 'fr', 'ar'];
    
    languages.forEach(lang => {
      const translations = loadTranslations(lang);
      expect(Object.keys(translations).length).toBeGreaterThan(0);
    });
  });

  test('should have consistent translation keys across all languages', () => {
    const languages = ['en', 'fr', 'ar'];
    const allTranslations = {};
    const allKeys = new Set();

    // Load all translations and collect all keys
    languages.forEach(lang => {
      allTranslations[lang] = loadTranslations(lang);
      const keys = getAllKeys(allTranslations[lang]);
      keys.forEach(key => allKeys.add(key));
    });

    // Check that each language has all keys
    languages.forEach(lang => {
      const missingKeys = [];
      allKeys.forEach(key => {
        if (!hasNestedKey(allTranslations[lang], key)) {
          missingKeys.push(key);
        }
      });

      if (missingKeys.length > 0) {
        console.warn(`Missing keys in ${lang}:`, missingKeys);
      }

      expect(missingKeys.length).toBe(0);
    });
  });

  test('should have 100% translation coverage for English', () => {
    expect(report.completeness.en.percentage).toBe(100);
  });

  test('should have 100% translation coverage for French', () => {
    expect(report.completeness.fr.percentage).toBe(100);
  });

  test('should have 100% translation coverage for Arabic', () => {
    expect(report.completeness.ar.percentage).toBe(100);
  });

  test('should have no missing translations in any language', () => {
    const languages = ['en', 'fr', 'ar'];
    
    languages.forEach(lang => {
      const missingCount = report.missingByLanguage[lang].length;
      if (missingCount > 0) {
        console.warn(`${lang} missing translations:`, report.missingByLanguage[lang]);
      }
      expect(missingCount).toBe(0);
    });
  });

  test('should have minimum required translation keys', () => {
    const requiredKeys = [
      'appName',
      'nav.dashboard',
      'nav.employees',
      'nav.timeManagement',
      'nav.admin',
      'nav.settings',
      'loading',
      'error',
      'success',
      'cancel',
      'save',
      'delete',
      'edit',
      'add'
    ];

    const languages = ['en', 'fr', 'ar'];
    
    languages.forEach(lang => {
      const translations = loadTranslations(lang);
      
      requiredKeys.forEach(key => {
        expect(hasNestedKey(translations, key)).toBe(true);
      });
    });
  });

  test('should have consistent structure across languages', () => {
    const languages = ['en', 'fr', 'ar'];
    const structures = {};

    // Get the structure (keys only) for each language
    languages.forEach(lang => {
      const translations = loadTranslations(lang);
      structures[lang] = getAllKeys(translations).sort();
    });

    // Compare structures
    const baseStructure = structures.en;
    languages.slice(1).forEach(lang => {
      expect(structures[lang]).toEqual(baseStructure);
    });
  });

  test('should not have empty translation values', () => {
    const languages = ['en', 'fr', 'ar'];
    
    languages.forEach(lang => {
      const translations = loadTranslations(lang);
      const keys = getAllKeys(translations);
      
      keys.forEach(key => {
        const value = getNestedValue(translations, key);
        expect(value).toBeTruthy();
        expect(typeof value).toBe('string');
        expect(value.trim().length).toBeGreaterThan(0);
      });
    });
  });

  test('should have proper Arabic RTL text direction indicators', () => {
    const arabicTranslations = loadTranslations('ar');
    const keys = getAllKeys(arabicTranslations);
    
    // Check that Arabic translations contain Arabic characters
    keys.forEach(key => {
      const value = getNestedValue(arabicTranslations, key);
      if (value && typeof value === 'string') {
        // Arabic text should contain Arabic characters (Unicode range)
        const hasArabicChars = /[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]/.test(value);
        
        // Allow some exceptions for technical terms, numbers, or mixed content
        const exceptions = ['WePaie', '<EMAIL>', 'http', 'https', '{{', '}}'];
        const isException = exceptions.some(exception => value.includes(exception));
        
        if (!hasArabicChars && !isException) {
          console.warn(`Arabic translation for "${key}" may not contain Arabic text: "${value}"`);
        }
      }
    });
  });
});

/**
 * Helper function to get nested value from object
 */
function getNestedValue(obj, key) {
  const keys = key.split('.');
  let current = obj;
  
  for (const k of keys) {
    if (current === null || current === undefined || typeof current !== 'object') {
      return undefined;
    }
    current = current[k];
  }
  
  return current;
}

// Additional test for running the full coverage report
describe('Translation Coverage Report', () => {
  test('should generate comprehensive coverage report', () => {
    // Capture console output
    const originalLog = console.log;
    const logs = [];
    console.log = (...args) => logs.push(args.join(' '));

    try {
      const success = runTranslationCoverageTest();
      
      // Restore console.log
      console.log = originalLog;
      
      // Check that report was generated
      expect(logs.length).toBeGreaterThan(0);
      expect(logs.some(log => log.includes('TRANSLATION COVERAGE REPORT'))).toBe(true);
      
      // Test should pass if all translations are complete
      expect(success).toBe(true);
    } finally {
      console.log = originalLog;
    }
  });
});
