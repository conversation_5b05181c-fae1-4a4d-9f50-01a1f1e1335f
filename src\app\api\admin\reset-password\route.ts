import { NextRequest, NextResponse } from 'next/server';
import { getAuth } from 'firebase-admin/auth';
import { getFirestore } from 'firebase-admin/firestore';
import { initializeApp, getApps, cert } from 'firebase-admin/app';

// Initialize Firebase Admin SDK
if (!getApps().length) {
  const serviceAccount = {
    type: 'service_account',
    project_id: process.env.FIREBASE_PROJECT_ID,
    private_key_id: process.env.FIREBASE_PRIVATE_KEY_ID,
    private_key: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
    client_email: process.env.FIREBASE_CLIENT_EMAIL,
    client_id: process.env.FIREBASE_CLIENT_ID,
    auth_uri: 'https://accounts.google.com/o/oauth2/auth',
    token_uri: 'https://oauth2.googleapis.com/token',
    auth_provider_x509_cert_url: 'https://www.googleapis.com/oauth2/v1/certs',
    client_x509_cert_url: `https://www.googleapis.com/robot/v1/metadata/x509/${process.env.FIREBASE_CLIENT_EMAIL}`,
  };

  initializeApp({
    credential: cert(serviceAccount as any),
    projectId: process.env.FIREBASE_PROJECT_ID,
  });
}

const auth = getAuth();
const db = getFirestore();

export interface ResetPasswordRequest {
  email: string;
  userId?: string;
}

export async function POST(request: NextRequest) {
  try {
    // Verify the request is from an authenticated admin user
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const idToken = authHeader.split('Bearer ')[1];
    
    // Verify the ID token
    const decodedToken = await auth.verifyIdToken(idToken);

    console.log('🔍 Reset Password API: Decoded token claims:', {
      uid: decodedToken.uid,
      email: decodedToken.email,
      isAdmin: decodedToken.isAdmin,
      isSuperAdmin: decodedToken.isSuperAdmin,
      role: decodedToken.role
    });

    // Check if user has admin permissions
    const hasAdminPermission = decodedToken.isAdmin === true || 
                              decodedToken.isSuperAdmin === true ||
                              decodedToken.role === 'super_admin' ||
                              decodedToken.role === 'company_admin';

    // Double-check with Firestore document if custom claims are not set
    if (!hasAdminPermission) {
      try {
        const userDoc = await db.collection('users').doc(decodedToken.uid).get();
        if (userDoc.exists) {
          const userData = userDoc.data();
          const isFirestoreAdmin = userData?.role === 'super_admin' || 
                                  userData?.role === 'company_admin';
          if (!isFirestoreAdmin) {
            console.log('❌ Reset Password API: User lacks admin privileges');
            return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
          }
        } else {
          console.log('❌ Reset Password API: User document not found');
          return NextResponse.json({ error: 'User not found' }, { status: 403 });
        }
      } catch (error) {
        console.error('❌ Reset Password API: Error checking user permissions:', error);
        return NextResponse.json({ error: 'Permission check failed' }, { status: 500 });
      }
    }

    const body: ResetPasswordRequest = await request.json();
    const { email, userId } = body;

    // Validate required fields
    if (!email) {
      return NextResponse.json({ error: 'Missing required field: email' }, { status: 400 });
    }

    console.log('🔄 Reset Password API: Sending password reset email to:', email);

    // Generate password reset link using Firebase Admin SDK
    try {
      const resetLink = await auth.generatePasswordResetLink(email, {
        url: `${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/login`,
        handleCodeInApp: false,
      });

      console.log('✅ Reset Password API: Generated password reset link for:', email);

      // Optionally, you could send a custom email here instead of using Firebase's default
      // For now, we'll rely on Firebase's built-in email system

      return NextResponse.json({
        success: true,
        message: 'Password reset email sent successfully',
        email: email
      });

    } catch (error: any) {
      console.error('❌ Reset Password API: Error generating reset link:', error);
      
      if (error.code === 'auth/user-not-found') {
        return NextResponse.json({ error: 'Aucun utilisateur trouvé avec cette adresse email' }, { status: 404 });
      }
      
      if (error.code === 'auth/invalid-email') {
        return NextResponse.json({ error: 'Adresse email invalide' }, { status: 400 });
      }

      throw error;
    }

  } catch (error) {
    console.error('❌ Reset Password API: Error:', error);
    
    return NextResponse.json({ 
      error: 'Erreur lors de l\'envoi de l\'email de réinitialisation',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
