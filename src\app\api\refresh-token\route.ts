import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    return NextResponse.json({
      success: true,
      message: 'Token refresh endpoint - use client-side Firebase Auth to refresh token'
    });
  } catch (error) {
    console.error('❌ Token Refresh: Error:', error);
    return NextResponse.json({ 
      error: 'Token refresh failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
