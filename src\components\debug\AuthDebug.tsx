"use client";

import React from 'react';
import { useAuth } from '@/context/AuthContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  User, 
  Building2, 
  Shield, 
  AlertCircle, 
  CheckCircle, 
  XCircle,
  RefreshCw
} from 'lucide-react';

/**
 * Auth Debug Component
 * 
 * Displays current authentication state for debugging purposes.
 * Only shown in development mode.
 */
export function AuthDebug() {
  const auth = useAuth();

  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  const getStatusIcon = (condition: boolean | null | undefined) => {
    if (condition === null || condition === undefined) {
      return <AlertCircle className="h-4 w-4 text-yellow-500" />;
    }
    return condition ? (
      <CheckCircle className="h-4 w-4 text-green-500" />
    ) : (
      <XCircle className="h-4 w-4 text-red-500" />
    );
  };

  const getStatusText = (condition: boolean | null | undefined) => {
    if (condition === null || condition === undefined) {
      return 'Unknown';
    }
    return condition ? 'Yes' : 'No';
  };

  return (
    <Card className="mb-4 border-yellow-200 bg-yellow-50">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-sm">
          <Shield className="h-4 w-4" />
          Auth Debug (Development Only)
          <Button
            variant="outline"
            size="sm"
            onClick={() => window.location.reload()}
            className="ml-auto"
          >
            <RefreshCw className="h-4 w-4" />
          </Button>
        </CardTitle>
        <CardDescription className="text-xs">
          Current authentication state and user context
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Auth State */}
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div className="space-y-2">
            <h4 className="font-medium">Auth State</h4>
            <div className="space-y-1">
              <div className="flex items-center justify-between">
                <span>Initialized:</span>
                <div className="flex items-center gap-1">
                  {getStatusIcon(auth.isInitialized)}
                  <Badge variant={auth.isInitialized ? "default" : "secondary"}>
                    {getStatusText(auth.isInitialized)}
                  </Badge>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <span>Loading:</span>
                <div className="flex items-center gap-1">
                  {getStatusIcon(!auth.isLoading)}
                  <Badge variant={auth.isLoading ? "destructive" : "default"}>
                    {getStatusText(!auth.isLoading)}
                  </Badge>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <span>Firebase User:</span>
                <div className="flex items-center gap-1">
                  {getStatusIcon(!!auth.firebaseUser)}
                  <Badge variant={auth.firebaseUser ? "default" : "secondary"}>
                    {getStatusText(!!auth.firebaseUser)}
                  </Badge>
                </div>
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <h4 className="font-medium">Data State</h4>
            <div className="space-y-1">
              <div className="flex items-center justify-between">
                <span>User Data:</span>
                <div className="flex items-center gap-1">
                  {getStatusIcon(!!auth.user)}
                  <Badge variant={auth.user ? "default" : "secondary"}>
                    {getStatusText(!!auth.user)}
                  </Badge>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <span>Company Data:</span>
                <div className="flex items-center gap-1">
                  {getStatusIcon(!!auth.company)}
                  <Badge variant={auth.company ? "default" : "secondary"}>
                    {getStatusText(!!auth.company)}
                  </Badge>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* User Details */}
        {auth.firebaseUser && (
          <div className="space-y-2">
            <h4 className="font-medium flex items-center gap-2">
              <User className="h-4 w-4" />
              Firebase User
            </h4>
            <div className="bg-white p-2 rounded border text-xs space-y-1">
              <div><strong>UID:</strong> {auth.firebaseUser.uid}</div>
              <div><strong>Email:</strong> {auth.firebaseUser.email}</div>
              <div><strong>Display Name:</strong> {auth.firebaseUser.displayName || 'None'}</div>
              <div><strong>Email Verified:</strong> {auth.firebaseUser.emailVerified ? 'Yes' : 'No'}</div>
            </div>
          </div>
        )}

        {/* WePaie User Details */}
        {auth.user && (
          <div className="space-y-2">
            <h4 className="font-medium flex items-center gap-2">
              <User className="h-4 w-4" />
              WePaie User
            </h4>
            <div className="bg-white p-2 rounded border text-xs space-y-1">
              <div><strong>ID:</strong> {auth.user.id}</div>
              <div><strong>Name:</strong> {auth.user.name}</div>
              <div><strong>Email:</strong> {auth.user.email}</div>
              <div><strong>Role:</strong> {auth.user.role}</div>
              <div><strong>Company ID:</strong> {auth.user.companyId}</div>
              <div><strong>Active:</strong> {auth.user.isActive ? 'Yes' : 'No'}</div>
            </div>
          </div>
        )}

        {/* Company Details */}
        {auth.company && (
          <div className="space-y-2">
            <h4 className="font-medium flex items-center gap-2">
              <Building2 className="h-4 w-4" />
              Company
            </h4>
            <div className="bg-white p-2 rounded border text-xs space-y-1">
              <div><strong>ID:</strong> {auth.company.id}</div>
              <div><strong>Name:</strong> {auth.company.name}</div>
              <div><strong>Email:</strong> {auth.company.email}</div>
              <div><strong>Phone:</strong> {auth.company.phone}</div>
            </div>
          </div>
        )}

        {/* Error State */}
        {auth.isInitialized && !auth.isLoading && auth.firebaseUser && (!auth.user || !auth.company) && (
          <div className="p-2 bg-red-50 border border-red-200 rounded text-xs">
            <div className="flex items-center gap-2 text-red-800 font-medium">
              <AlertCircle className="h-4 w-4" />
              Issue Detected
            </div>
            <div className="mt-1 text-red-700">
              Firebase user is authenticated but WePaie user data or company data is missing.
              This indicates an issue with the demo admin setup process.
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
