"use client";

import React from 'react';
import { useFirestore, useCompanyId, useUserId, usePermissions } from '@/context/FirestoreContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { CheckCircle, XCircle, RefreshCw, Database, User, Building2 } from 'lucide-react';

/**
 * Context Debug Component
 * 
 * Displays the current state of the FirestoreContext for debugging purposes.
 * Shows company ID, user ID, permissions, and context data.
 */
export function ContextDebug() {
  const { 
    company, 
    user, 
    isLoading, 
    isInitialized, 
    refreshCompany, 
    refreshUser 
  } = useFirestore();
  
  const companyId = useCompanyId();
  const userId = useUserId();
  const { isAdmin } = usePermissions();

  const handleRefresh = async () => {
    if (companyId) {
      await refreshCompany();
    }
    if (userId && companyId) {
      await refreshUser();
    }
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Database className="h-5 w-5" />
          Firestore Context Debug
        </CardTitle>
        <CardDescription>
          Current state of the FirestoreContext for debugging purposes.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Context Status */}
        <div className="grid grid-cols-2 gap-4">
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium">Loading:</span>
            {isLoading ? (
              <Badge variant="secondary">
                <RefreshCw className="h-3 w-3 mr-1 animate-spin" />
                Loading
              </Badge>
            ) : (
              <Badge variant="default">
                <CheckCircle className="h-3 w-3 mr-1" />
                Ready
              </Badge>
            )}
          </div>
          
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium">Initialized:</span>
            {isInitialized ? (
              <Badge variant="default">
                <CheckCircle className="h-3 w-3 mr-1" />
                Yes
              </Badge>
            ) : (
              <Badge variant="destructive">
                <XCircle className="h-3 w-3 mr-1" />
                No
              </Badge>
            )}
          </div>
        </div>

        {/* Company Information */}
        <div className="space-y-2">
          <h4 className="font-medium flex items-center gap-2">
            <Building2 className="h-4 w-4" />
            Company Context
          </h4>
          <div className="bg-muted p-3 rounded-lg space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm">Company ID:</span>
              <code className="text-xs bg-background px-2 py-1 rounded">
                {companyId || 'null'}
              </code>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-sm">Company Name:</span>
              <span className="text-sm font-medium">
                {company?.name || 'Not loaded'}
              </span>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-sm">Company Email:</span>
              <span className="text-sm">
                {company?.email || 'Not loaded'}
              </span>
            </div>
          </div>
        </div>

        {/* User Information */}
        <div className="space-y-2">
          <h4 className="font-medium flex items-center gap-2">
            <User className="h-4 w-4" />
            User Context
          </h4>
          <div className="bg-muted p-3 rounded-lg space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm">User ID:</span>
              <code className="text-xs bg-background px-2 py-1 rounded">
                {userId || 'null'}
              </code>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-sm">User Name:</span>
              <span className="text-sm font-medium">
                {user ? `${user.firstName} ${user.lastName}` : 'Not loaded'}
              </span>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-sm">User Email:</span>
              <span className="text-sm">
                {user?.email || 'Not loaded'}
              </span>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-sm">User Role:</span>
              <Badge variant={user?.role === 'admin' ? 'default' : 'secondary'}>
                {user?.role || 'Not loaded'}
              </Badge>
            </div>
            
            <div className="flex items-center justify-between">
              <span className="text-sm">Is Admin:</span>
              {isAdmin() ? (
                <Badge variant="default">
                  <CheckCircle className="h-3 w-3 mr-1" />
                  Yes
                </Badge>
              ) : (
                <Badge variant="secondary">
                  <XCircle className="h-3 w-3 mr-1" />
                  No
                </Badge>
              )}
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="flex gap-2">
          <Button 
            onClick={handleRefresh} 
            variant="outline" 
            size="sm"
            disabled={isLoading}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh Context
          </Button>
        </div>

        {/* Raw Data (for debugging) */}
        <details className="space-y-2">
          <summary className="text-sm font-medium cursor-pointer">
            Raw Context Data (Click to expand)
          </summary>
          <div className="bg-muted p-3 rounded-lg">
            <pre className="text-xs overflow-auto">
              {JSON.stringify(
                {
                  companyId,
                  userId,
                  isLoading,
                  isInitialized,
                  company: company ? {
                    id: company.id,
                    name: company.name,
                    email: company.email,
                    companyId: company.companyId,
                  } : null,
                  user: user ? {
                    id: user.id,
                    name: `${user.firstName} ${user.lastName}`,
                    email: user.email,
                    role: user.role,
                    companyId: user.companyId,
                  } : null,
                },
                null,
                2
              )}
            </pre>
          </div>
        </details>
      </CardContent>
    </Card>
  );
}
