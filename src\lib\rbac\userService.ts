/**
 * User Management Service for RBAC
 * 
 * Handles user creation, updates, role management, and company assignments
 * with proper Firebase Authentication and Firestore integration.
 */

import {
  sendPasswordResetEmail
} from 'firebase/auth';
import { 
  collection, 
  doc, 
  setDoc, 
  updateDoc, 
  deleteDoc, 
  getDocs, 
  getDoc,
  query, 
  where,
  orderBy,
  Timestamp
} from 'firebase/firestore';
import { auth, db } from '@/lib/firebase';
import type { 
  RBACUser, 
  CreateUserRequest, 
  UpdateUserRequest,
  UserRole,
  CustomClaims
} from './types';

/**
 * Create a new user with role and company assignment
 */
export async function createUser(request: CreateUserRequest): Promise<RBACUser> {
  try {
    const currentUser = auth.currentUser;
    if (!currentUser) {
      throw new Error('Vous devez être connecté pour créer un utilisateur');
    }

    console.log('🔐 UserService: Creating user via Admin API');

    // Get the current user's ID token for authentication
    const idToken = await currentUser.getIdToken();

    // Call the server-side API endpoint
    const response = await fetch('/api/admin/create-user', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${idToken}`
      },
      body: JSON.stringify(request)
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to create user');
    }

    const result = await response.json();
    console.log('✅ UserService: User created successfully via Admin API');

    return result.user as RBACUser;

  } catch (error) {
    console.error('❌ UserService: Error creating user:', error);
    throw new Error(`Failed to create user: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Update user information and role
 */
export async function updateUser(userId: string, companyId: string, updates: UpdateUserRequest): Promise<void> {
  try {
    const userRef = doc(db, 'users', userId);
    
    const updateData: any = {
      ...updates,
      updatedAt: Timestamp.now()
    };
    
    await updateDoc(userRef, updateData);
    
    // Update custom claims if role changed
    if (updates.role) {
      await setUserCustomClaims(userId, {
        role: updates.role,
        companyId: updates.companyId || companyId,
        isAdmin: ['super_admin', 'company_admin'].includes(updates.role),
        isSuperAdmin: updates.role === 'super_admin',
        isPayrollManager: ['super_admin', 'company_admin', 'editor'].includes(updates.role),
        isViewer: updates.role === 'viewer'
      });
    }
  } catch (error) {
    console.error('Error updating user:', error);
    throw new Error(`Failed to update user: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Delete user (deactivate)
 */
export async function deleteUser(userId: string, companyId: string): Promise<void> {
  try {
    const userRef = doc(db, 'users', userId);
    
    // Soft delete by deactivating
    await updateDoc(userRef, {
      isActive: false,
      updatedAt: Timestamp.now()
    });
    
    // Remove custom claims
    await setUserCustomClaims(userId, {
      role: 'viewer',
      companyId: undefined,
      isAdmin: false,
      isSuperAdmin: false,
      isPayrollManager: false,
      isViewer: true
    });
  } catch (error) {
    console.error('Error deleting user:', error);
    throw new Error(`Failed to delete user: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Get user by ID
 */
export async function getUserById(userId: string, companyId: string): Promise<RBACUser | null> {
  try {
    const userRef = doc(db, 'users', userId);
    const userSnap = await getDoc(userRef);
    
    if (!userSnap.exists()) {
      return null;
    }
    
    const data = userSnap.data();
    return {
      id: userSnap.id,
      ...data,
      createdAt: data.createdAt?.toDate?.()?.toISOString() || data.createdAt,
      updatedAt: data.updatedAt?.toDate?.()?.toISOString() || data.updatedAt,
      lastLoginAt: data.lastLoginAt?.toDate?.()?.toISOString() || data.lastLoginAt
    } as RBACUser;
  } catch (error) {
    console.error('Error getting user:', error);
    return null;
  }
}

/**
 * Get all users for a company
 */
export async function getCompanyUsers(companyId: string): Promise<RBACUser[]> {
  try {
    const usersRef = collection(db, 'users');
    // Use simple query without orderBy to avoid composite index requirement
    const q = query(
      usersRef,
      where('companyId', '==', companyId)
    );
    const querySnapshot = await getDocs(q);

    const users = querySnapshot.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        ...data,
        createdAt: data.createdAt?.toDate?.()?.toISOString() || data.createdAt,
        updatedAt: data.updatedAt?.toDate?.()?.toISOString() || data.updatedAt,
        lastLoginAt: data.lastLoginAt?.toDate?.()?.toISOString() || data.lastLoginAt
      } as RBACUser;
    });

    // Sort by createdAt in memory to avoid composite index requirement
    return users.sort((a, b) => {
      const dateA = new Date(a.createdAt).getTime();
      const dateB = new Date(b.createdAt).getTime();
      return dateB - dateA; // Descending order (newest first)
    });
  } catch (error) {
    console.error('Error getting company users:', error);
    return [];
  }
}

/**
 * Get users by role
 */
export async function getUsersByRole(companyId: string, role: UserRole): Promise<RBACUser[]> {
  try {
    const usersRef = collection(db, 'users');
    // Use simple query without orderBy to avoid composite index requirement
    const q = query(
      usersRef,
      where('companyId', '==', companyId),
      where('role', '==', role),
      where('isActive', '==', true)
    );
    const querySnapshot = await getDocs(q);

    const users = querySnapshot.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        ...data,
        createdAt: data.createdAt?.toDate?.()?.toISOString() || data.createdAt,
        updatedAt: data.updatedAt?.toDate?.()?.toISOString() || data.updatedAt,
        lastLoginAt: data.lastLoginAt?.toDate?.()?.toISOString() || data.lastLoginAt
      } as RBACUser;
    });

    // Sort by createdAt in memory to avoid composite index requirement
    return users.sort((a, b) => {
      const dateA = new Date(a.createdAt).getTime();
      const dateB = new Date(b.createdAt).getTime();
      return dateB - dateA; // Descending order (newest first)
    });
  } catch (error) {
    console.error('Error getting users by role:', error);
    return [];
  }
}

/**
 * Search users by email or name
 */
export async function searchUsers(companyId: string, searchTerm: string): Promise<RBACUser[]> {
  try {
    const users = await getCompanyUsers(companyId);
    const term = searchTerm.toLowerCase();
    
    return users.filter(user => 
      user.email.toLowerCase().includes(term) ||
      user.firstName.toLowerCase().includes(term) ||
      user.lastName.toLowerCase().includes(term) ||
      `${user.firstName} ${user.lastName}`.toLowerCase().includes(term)
    );
  } catch (error) {
    console.error('Error searching users:', error);
    return [];
  }
}

/**
 * Activate/deactivate user
 */
export async function toggleUserStatus(userId: string, companyId: string, isActive: boolean): Promise<void> {
  try {
    const userRef = doc(db, 'users', userId);
    await updateDoc(userRef, {
      isActive,
      updatedAt: Timestamp.now()
    });
  } catch (error) {
    console.error('Error toggling user status:', error);
    throw new Error(`Failed to update user status: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Send password reset email
 */
export async function sendUserPasswordReset(email: string): Promise<void> {
  try {
    await sendPasswordResetEmail(auth, email);
  } catch (error) {
    console.error('Error sending password reset:', error);
    throw new Error(`Failed to send password reset: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Set custom claims for user (placeholder - would be implemented via Cloud Functions)
 */
async function setUserCustomClaims(userId: string, claims: CustomClaims): Promise<void> {
  // This would typically be implemented as a Cloud Function call
  // For now, we'll log the claims that should be set
  console.log(`Setting custom claims for user ${userId}:`, claims);
  
  // In a real implementation, you would call a Cloud Function:
  // await httpsCallable(functions, 'setCustomClaims')({ userId, claims });
}

/**
 * Get user statistics for a company
 */
export async function getUserStats(companyId: string): Promise<{
  total: number;
  active: number;
  inactive: number;
  byRole: Record<UserRole, number>;
}> {
  try {
    const users = await getCompanyUsers(companyId);
    
    const stats = {
      total: users.length,
      active: users.filter(u => u.isActive).length,
      inactive: users.filter(u => !u.isActive).length,
      byRole: {
        super_admin: 0,
        company_admin: 0,
        editor: 0,
        viewer: 0
      } as Record<UserRole, number>
    };
    
    users.forEach(user => {
      if (user.role in stats.byRole) {
        stats.byRole[user.role]++;
      }
    });
    
    return stats;
  } catch (error) {
    console.error('Error getting user stats:', error);
    return {
      total: 0,
      active: 0,
      inactive: 0,
      byRole: { super_admin: 0, company_admin: 0, editor: 0, viewer: 0 }
    };
  }
}
