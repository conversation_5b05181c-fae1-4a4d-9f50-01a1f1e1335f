"use client";

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useCompany } from '@/context/CompanyContext';
import { useRBAC } from '@/context/RBACContext';
import { LoadingScreen } from '@/components/ui/loading-screen';
import {
  Building2,
  Shield,
  AlertTriangle,
  CheckCircle,
  Users,
  Settings
} from 'lucide-react';

interface CompanySelectionGuardProps {
  children: React.ReactNode;
  requireCompany?: boolean;
  fallbackMessage?: string;
}

/**
 * Company Selection Guard Component
 * 
 * Enforces company selection before allowing access to company-specific data.
 * Provides company selection interface for super admins and validates access for regular users.
 * 
 * Features:
 * - Automatic company selection for regular users
 * - Company selection interface for super admins
 * - Data integrity enforcement
 * - Loading states and error handling
 */
export function CompanySelectionGuard({ 
  children, 
  requireCompany = true,
  fallbackMessage = "Sélection d'entreprise requise pour accéder à cette fonctionnalité."
}: CompanySelectionGuardProps) {
  const { 
    selectedCompany, 
    availableCompanies, 
    isLoading, 
    isInitialized,
    selectCompany,
    requiresCompanySelection 
  } = useCompany();
  
  const { isSuperAdmin, user: rbacUser } = useRBAC();

  // Show loading while company context is initializing
  if (isLoading || !isInitialized) {
    return (
      <LoadingScreen
        message="Chargement des entreprises..."
        submessage="Initialisation du contexte d'entreprise"
      />
    );
  }

  // If company selection is not required, render children directly
  if (!requireCompany) {
    return <>{children}</>;
  }

  // Super admin needs to select a company for company-specific operations
  if (isSuperAdmin() && requiresCompanySelection()) {
    return (
      <div className="w-full space-y-4">
        {/* Full-width header to establish consistent page width */}
        <div className="w-full border-b border-border pb-4">
          <h1 className="text-2xl font-bold">Sélection d'Entreprise</h1>
          <p className="text-muted-foreground">Choisissez l'entreprise pour accéder aux données</p>
        </div>

        <div className="text-center space-y-2">
          <Building2 className="h-12 w-12 mx-auto text-muted-foreground" />
          <h1 className="text-2xl font-bold">Sélection d'Entreprise</h1>
          <p className="text-muted-foreground">
            Sélectionnez une entreprise pour accéder aux données spécifiques à l'entreprise.
          </p>
        </div>

        {/* Super Admin Notice */}
        <Card className="border-blue-200 bg-blue-50">
          <CardContent className="pt-6">
            <div className="flex items-center gap-2 text-blue-800">
              <Shield className="h-5 w-5" />
              <span className="font-medium">Mode Super Administrateur</span>
            </div>
            <p className="text-sm text-blue-700 mt-1">
              En tant que super administrateur, vous pouvez accéder aux données de toutes les entreprises.
              Sélectionnez une entreprise pour continuer.
            </p>
          </CardContent>
        </Card>

        {/* Company Selection */}
        {availableCompanies.length > 0 ? (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Building2 className="h-5 w-5" />
                Entreprises Disponibles
              </CardTitle>
              <CardDescription>
                Sélectionnez l'entreprise dont vous souhaitez gérer les données.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {availableCompanies.map((company) => (
                <div 
                  key={company.id}
                  className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors"
                >
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <h3 className="font-medium">{company.name}</h3>
                      <Badge 
                        variant={company.isActive ? "default" : "secondary"}
                        className={company.isActive ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"}
                      >
                        {company.isActive ? (
                          <>
                            <CheckCircle className="mr-1 h-3 w-3" />
                            Active
                          </>
                        ) : (
                          <>
                            <AlertTriangle className="mr-1 h-3 w-3" />
                            Inactive
                          </>
                        )}
                      </Badge>
                    </div>
                    {company.email && (
                      <p className="text-sm text-muted-foreground">{company.email}</p>
                    )}
                    {company.description && (
                      <p className="text-sm text-muted-foreground mt-1">{company.description}</p>
                    )}
                  </div>
                  <Button 
                    onClick={() => selectCompany(company)}
                    variant="outline"
                    size="sm"
                    disabled={!company.isActive}
                  >
                    <Building2 className="mr-2 h-4 w-4" />
                    Sélectionner
                  </Button>
                </div>
              ))}
            </CardContent>
          </Card>
        ) : (
          <Card>
            <CardContent className="pt-6">
              <div className="text-center">
                <Building2 className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                <h3 className="font-medium mb-2">Aucune Entreprise Disponible</h3>
                <p className="text-sm text-muted-foreground mb-4">
                  Aucune entreprise n'est disponible dans le système.
                </p>
                <Button onClick={() => window.location.href = '/admin'}>
                  <Settings className="mr-2 h-4 w-4" />
                  Aller à l'Administration
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Fallback Message */}
        <Card className="border-amber-200 bg-amber-50">
          <CardContent className="pt-6">
            <div className="flex items-center gap-2 text-amber-800">
              <AlertTriangle className="h-5 w-5" />
              <span className="font-medium">Information</span>
            </div>
            <p className="text-sm text-amber-700 mt-1">
              {fallbackMessage}
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Regular user without company access
  if (!selectedCompany && rbacUser && !rbacUser.companyId) {
    return (
      <div className="w-full space-y-4">
        {/* Full-width header to establish consistent page width */}
        <div className="w-full border-b border-border pb-4">
          <h1 className="text-2xl font-bold">Accès Refusé</h1>
          <p className="text-muted-foreground">Votre compte n'est associé à aucune entreprise</p>
        </div>

        <div className="text-center space-y-2">
          <AlertTriangle className="h-12 w-12 mx-auto text-destructive" />
          <h1 className="text-2xl font-bold">Accès Refusé</h1>
          <p className="text-muted-foreground">
            Vous n'êtes associé à aucune entreprise.
          </p>
        </div>

        <Card className="border-red-200 bg-red-50">
          <CardContent className="pt-6">
            <div className="flex items-center gap-2 text-red-800">
              <AlertTriangle className="h-5 w-5" />
              <span className="font-medium">Compte Non Configuré</span>
            </div>
            <p className="text-sm text-red-700 mt-1">
              Votre compte n'est associé à aucune entreprise. Contactez votre administrateur pour configurer votre accès.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Regular user with company but no company selected (shouldn't happen with auto-selection)
  if (requiresCompanySelection() && !isSuperAdmin()) {
    return (
      <LoadingScreen
        message="Configuration de l'entreprise..."
        submessage="Chargement des données de votre entreprise"
      />
    );
  }

  // All checks passed, render children
  return <>{children}</>;
}

/**
 * Higher-order component for pages that require company selection
 */
export function withCompanySelection<P extends object>(
  Component: React.ComponentType<P>,
  options?: {
    requireCompany?: boolean;
    fallbackMessage?: string;
  }
) {
  return function CompanyGuardedComponent(props: P) {
    return (
      <CompanySelectionGuard 
        requireCompany={options?.requireCompany}
        fallbackMessage={options?.fallbackMessage}
      >
        <Component {...props} />
      </CompanySelectionGuard>
    );
  };
}

/**
 * Company Selection Status Component
 * Shows current company selection status in the UI
 */
export function CompanySelectionStatus() {
  const { selectedCompany, availableCompanies } = useCompany();
  const { isSuperAdmin } = useRBAC();

  if (!selectedCompany) {
    return null;
  }

  return (
    <div className="flex items-center gap-2 text-sm text-muted-foreground">
      <Building2 className="h-4 w-4" />
      <span>{selectedCompany.name}</span>
      {isSuperAdmin() && availableCompanies.length > 1 && (
        <Badge variant="outline" className="text-xs">
          {availableCompanies.length} entreprises disponibles
        </Badge>
      )}
    </div>
  );
}
