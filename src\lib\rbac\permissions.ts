/**
 * Permission Checking System for WePaie RBAC
 * 
 * Provides comprehensive permission checking functionality
 * for role-based access control throughout the application.
 */

import type { 
  Permission, 
  UserRole, 
  RBACUser, 
  PermissionContext, 
  PermissionResult,
  CustomClaims 
} from './types';
import { ROLE_PERMISSIONS, getRoleConfig } from './roles';

/**
 * Check if a user has a specific permission
 */
export function hasPermission(
  user: RBACUser | null, 
  permission: Permission,
  context?: PermissionContext
): boolean {
  if (!user || !user.isActive) {
    return false;
  }

  // Super admin has all permissions
  if (user.role === 'super_admin') {
    return true;
  }

  // Check if role has the permission
  const rolePermissions = ROLE_PERMISSIONS[user.role];
  if (!rolePermissions.includes(permission)) {
    return false;
  }

  // Additional context-based checks
  if (context) {
    return checkPermissionContext(user, permission, context);
  }

  return true;
}

/**
 * Check if a user has any of the specified permissions
 */
export function hasAnyPermission(
  user: RBACUser | null,
  permissions: Permission[],
  context?: PermissionContext
): boolean {
  return permissions.some(permission => hasPermission(user, permission, context));
}

/**
 * Check if a user has all of the specified permissions
 */
export function hasAllPermissions(
  user: RBACUser | null,
  permissions: Permission[],
  context?: PermissionContext
): boolean {
  return permissions.every(permission => hasPermission(user, permission, context));
}

/**
 * Check if a user can access a specific company's data
 */
export function canAccessCompany(user: RBACUser | null, companyId: string): boolean {
  if (!user || !user.isActive) {
    return false;
  }

  // Super admin can access all companies
  if (user.role === 'super_admin') {
    return true;
  }

  // Other users can only access their assigned company
  return user.companyId === companyId;
}

/**
 * Check if a user can manage another user
 */
export function canManageUser(manager: RBACUser | null, targetUser: RBACUser): boolean {
  if (!manager || !manager.isActive) {
    return false;
  }

  // Super admin can manage all users
  if (manager.role === 'super_admin') {
    return true;
  }

  // Company admin can manage users in their company (except other admins)
  if (manager.role === 'company_admin') {
    return manager.companyId === targetUser.companyId && 
           targetUser.role !== 'super_admin' &&
           targetUser.role !== 'company_admin';
  }

  return false;
}

/**
 * Check permission with context (company, user, resource)
 */
function checkPermissionContext(
  user: RBACUser,
  permission: Permission,
  context: PermissionContext
): boolean {
  // Company-scoped permissions
  if (context.targetCompanyId) {
    if (!canAccessCompany(user, context.targetCompanyId)) {
      return false;
    }
  }

  // User management permissions
  if (permission.startsWith('users.') && context.targetUserId) {
    // Users can only manage users in their scope
    if (user.role === 'company_admin') {
      return user.companyId === context.targetCompanyId;
    }
  }

  return true;
}

/**
 * Get detailed permission result with reason
 */
export function checkPermission(
  user: RBACUser | null,
  permission: Permission,
  context?: PermissionContext
): PermissionResult {
  if (!user) {
    return {
      allowed: false,
      reason: 'User not authenticated',
      requiredPermission: permission
    };
  }

  if (!user.isActive) {
    return {
      allowed: false,
      reason: 'User account is inactive',
      requiredPermission: permission
    };
  }

  // Super admin check
  if (user.role === 'super_admin') {
    return { allowed: true };
  }

  // Role permission check
  const rolePermissions = ROLE_PERMISSIONS[user.role];
  if (!rolePermissions.includes(permission)) {
    return {
      allowed: false,
      reason: `Role '${user.role}' does not have permission '${permission}'`,
      requiredPermission: permission,
      requiredRole: getMinimumRoleForPermission(permission)
    };
  }

  // Context checks
  if (context) {
    if (context.targetCompanyId && !canAccessCompany(user, context.targetCompanyId)) {
      return {
        allowed: false,
        reason: 'User cannot access the specified company',
        requiredPermission: permission
      };
    }
  }

  return { allowed: true };
}

/**
 * Get minimum role required for a permission
 */
function getMinimumRoleForPermission(permission: Permission): UserRole | undefined {
  const roles: UserRole[] = ['viewer', 'editor', 'company_admin', 'super_admin'];
  
  for (const role of roles) {
    if (ROLE_PERMISSIONS[role].includes(permission)) {
      return role;
    }
  }
  
  return undefined;
}

/**
 * Check if user can perform action on resource
 */
export function canPerformAction(
  user: RBACUser | null,
  action: 'create' | 'read' | 'update' | 'delete',
  resource: string,
  context?: PermissionContext
): boolean {
  const permission = `${resource}.${action}` as Permission;
  return hasPermission(user, permission, context);
}

/**
 * Filter items based on user permissions
 */
export function filterByPermission<T>(
  user: RBACUser | null,
  items: T[],
  getPermission: (item: T) => Permission,
  getContext?: (item: T) => PermissionContext
): T[] {
  if (!user) return [];
  
  return items.filter(item => {
    const permission = getPermission(item);
    const context = getContext?.(item);
    return hasPermission(user, permission, context);
  });
}

/**
 * Check if user has role or higher
 */
export function hasRoleOrHigher(user: RBACUser | null, minRole: UserRole): boolean {
  if (!user || !user.isActive) {
    return false;
  }

  const hierarchy: UserRole[] = ['viewer', 'editor', 'company_admin', 'super_admin'];
  const userRoleIndex = hierarchy.indexOf(user.role);
  const minRoleIndex = hierarchy.indexOf(minRole);
  
  return userRoleIndex >= minRoleIndex;
}

/**
 * Get user permissions based on Firebase custom claims
 */
export function getPermissionsFromClaims(claims: CustomClaims): Permission[] {
  if (claims.permissions) {
    return claims.permissions;
  }
  
  // Fallback to role-based permissions
  return ROLE_PERMISSIONS[claims.role] || [];
}

/**
 * Check if user is in same company or super admin
 */
export function isSameCompanyOrSuperAdmin(user: RBACUser | null, targetCompanyId: string): boolean {
  if (!user || !user.isActive) {
    return false;
  }

  return user.role === 'super_admin' || user.companyId === targetCompanyId;
}

/**
 * Validate user can create another user with specified role
 */
export function canCreateUserWithRole(creator: RBACUser | null, targetRole: UserRole, targetCompanyId?: string): boolean {
  if (!creator || !creator.isActive) {
    return false;
  }

  // Super admin can create any user
  if (creator.role === 'super_admin') {
    return true;
  }

  // Company admin can create editor and viewer in their company
  if (creator.role === 'company_admin') {
    const allowedRoles: UserRole[] = ['editor', 'viewer'];
    return allowedRoles.includes(targetRole) && 
           targetCompanyId === creator.companyId;
  }

  return false;
}

/**
 * Get accessible companies for user
 */
export function getAccessibleCompanies(user: RBACUser | null, allCompanies: string[]): string[] {
  if (!user || !user.isActive) {
    return [];
  }

  // Super admin can access all companies
  if (user.role === 'super_admin') {
    return allCompanies;
  }

  // Other users can only access their company
  return user.companyId ? [user.companyId] : [];
}
