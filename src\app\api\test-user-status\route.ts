import { NextRequest, NextResponse } from 'next/server';
import { getAuth } from 'firebase-admin/auth';
import { getFirestore } from 'firebase-admin/firestore';
import { initializeApp, getApps, cert } from 'firebase-admin/app';

// Initialize Firebase Admin SDK
if (!getApps().length) {
  const serviceAccount = {
    type: "service_account",
    project_id: process.env.FIREBASE_PROJECT_ID,
    private_key_id: process.env.FIREBASE_PRIVATE_KEY_ID,
    private_key: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
    client_email: process.env.FIREBASE_CLIENT_EMAIL,
    client_id: process.env.FIREBASE_CLIENT_ID,
    auth_uri: "https://accounts.google.com/o/oauth2/auth",
    token_uri: "https://oauth2.googleapis.com/token",
    auth_provider_x509_cert_url: "https://www.googleapis.com/oauth2/v1/certs",
    client_x509_cert_url: `https://www.googleapis.com/robot/v1/metadata/x509/${process.env.FIREBASE_CLIENT_EMAIL}`
  };

  initializeApp({
    credential: cert(serviceAccount as any),
    projectId: process.env.FIREBASE_PROJECT_ID,
  });
}

const auth = getAuth();
const db = getFirestore();

export async function POST(request: NextRequest) {
  try {
    // Verify the request is from an authenticated user
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const idToken = authHeader.split('Bearer ')[1];
    
    // Verify the ID token
    const decodedToken = await auth.verifyIdToken(idToken);
    
    console.log('🔍 Test User Status: Decoded token claims:', {
      uid: decodedToken.uid,
      email: decodedToken.email,
      isAdmin: decodedToken.isAdmin,
      isSuperAdmin: decodedToken.isSuperAdmin,
      role: decodedToken.role
    });
    
    // Check Firestore document
    console.log('🔍 Test User Status: Checking Firestore document for user:', decodedToken.uid);
    const userDoc = await db.collection('users').doc(decodedToken.uid).get();
    console.log('🔍 Test User Status: User document exists:', userDoc.exists);
    
    let firestoreData = null;
    if (userDoc.exists) {
      firestoreData = userDoc.data();
      console.log('🔍 Test User Status: Firestore user data:', firestoreData);
    }

    return NextResponse.json({
      success: true,
      tokenClaims: {
        uid: decodedToken.uid,
        email: decodedToken.email,
        isAdmin: decodedToken.isAdmin,
        isSuperAdmin: decodedToken.isSuperAdmin,
        role: decodedToken.role
      },
      firestoreData,
      hasFirestoreDoc: userDoc.exists
    });

  } catch (error) {
    console.error('❌ Test User Status: Error:', error);
    return NextResponse.json({ 
      error: 'Test failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
