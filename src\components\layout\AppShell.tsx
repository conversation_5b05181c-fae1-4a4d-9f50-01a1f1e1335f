"use client";
import React from 'react';
import { usePathname } from 'next/navigation';
import {
  SidebarProvider,
  Sidebar,
  SidebarHeader,
  Sidebar<PERSON>ontent,
  SidebarFooter,
  SidebarInset,
  SidebarTrigger,
} from '@/components/ui/sidebar';
import { UserNav } from '@/components/layout/UserNav';
import { NavLinks } from '@/components/layout/NavLinks';
import { CompanyHeaderSelector } from '@/components/company/CompanyContextDisplay';
import { Building2 } from 'lucide-react';
import Link from 'next/link';
import { useTranslation } from '@/context/i18nContext';
import { getPageConfig, getTitleFromPath } from '@/lib/page-config';
import { useRBAC } from '@/context/RBACContext';

interface AppShellProps {
  children: React.ReactNode;
  pageTitle?: string; // This can be a translation key
  headerActions?: React.ReactNode; // Actions personnalisées pour le header
}

export default function AppShell({ children, pageTitle, headerActions }: AppShellProps) {
  const { t, language } = useTranslation();
  const { isSuperAdmin } = useRBAC();
  const isRTL = language === 'ar';

  // Utiliser usePathname pour App Router
  const pathname = usePathname();
  const currentPath = pathname || '/';

  // Obtenir la configuration de la page actuelle
  const pageConfig = getPageConfig(currentPath);

  // Déterminer le titre à afficher
  const displayTitle = pageTitle ||
    t(pageConfig.title, getTitleFromPath(currentPath));

  // Déterminer si on doit afficher le sélecteur de compagnie
  const shouldShowCompanySelector = pageConfig.showCompanySelector ||
    (isSuperAdmin() && pageConfig.showCompanySelector);

  return (
    <SidebarProvider defaultOpen>
      <div className="flex min-h-screen">
        <Sidebar
          variant="sidebar"
          collapsible="icon"
          className="border-r"
          data-sidebar="sidebar"
        >
        <SidebarHeader className="p-3"> {/* Reduced padding from p-4 */}
          <Link href="/dashboard" className="flex items-center gap-2 group-data-[collapsible=icon]:justify-center">
            <Building2 className="h-7 w-7 text-primary" />
            <span className="font-headline text-xl font-semibold text-primary group-data-[collapsible=icon]:hidden">
              {t('appName')}
            </span>
          </Link>
        </SidebarHeader>
        <SidebarContent className="p-1.5"> {/* Reduced padding from p-2 */}
          <NavLinks />
        </SidebarContent>
        <SidebarFooter className="p-1.5"> {/* Reduced padding from p-2 */}
          {/* Optional: Footer content like theme switcher or help link */}
        </SidebarFooter>
        </Sidebar>
        <SidebarInset className="flex flex-col bg-background flex-1" data-sidebar="content">
          <header className="sticky top-0 z-40 flex h-14 items-center justify-between bg-background px-4 sm:px-6 lg:px-8 w-full full-width-header border-b border-border/40"> {/* Full-width header with edge-to-edge border */}
            <div className={`flex items-center gap-4 ${isRTL ? 'flex-row-reverse' : ''}`}>
               <SidebarTrigger className="md:hidden" /> {/* Mobile toggle */}
              <h1 className="text-lg font-headline font-semibold">{displayTitle}</h1>
            </div>
            <div className={`flex items-center gap-4 ${isRTL ? 'flex-row-reverse' : ''}`}>
              {/* Actions personnalisées */}
              {headerActions && (
                <div className="flex items-center space-x-2">
                  {headerActions}
                </div>
              )}

              {/* Sélecteur de compagnie conditionnel */}
              {shouldShowCompanySelector && <CompanyHeaderSelector />}

              <UserNav />
            </div>
          </header>
          <main className="flex-1 overflow-y-auto w-full"> {/* Full-width main content area */}
            <div className="w-full px-2 sm:px-4 lg:px-6 py-4"> {/* Standardized container system */}
              <div className="w-full space-y-4"> {/* Consistent page container for all content */}
                {children}
              </div>
            </div>
          </main>
        </SidebarInset>
      </div>
    </SidebarProvider>
  );
}
