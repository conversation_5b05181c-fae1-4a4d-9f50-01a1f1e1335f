"use client";

import AppShell from '@/components/layout/AppShell';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Plus, Download, Filter } from 'lucide-react';
import { PageHeader } from '@/components/layout/PageHeader';
import { usePageConfig } from '@/hooks/usePageConfig';

/**
 * Page d'exemple démontrant l'approche structurée inspirée de votre méthode
 * 
 * Caractéristiques:
 * - Configuration centralisée via usePageConfig
 * - Header de page cohérent avec PageHeader
 * - Actions dans le header
 * - Layout modulaire avec AppShell
 * - Typage TypeScript strict
 * - Séparation claire des responsabilités
 */
export default function ExampleStructuredPage() {
  // Configuration de page centralisée
  const { title, description } = usePageConfig('/example-structured');

  // Actions pour le header
  const headerActions = (
    <>
      <Button variant="outline" size="sm">
        <Download className="h-4 w-4 mr-2" />
        Exporter
      </Button>
      <Button size="sm">
        <Plus className="h-4 w-4 mr-2" />
        Ajouter
      </Button>
    </>
  );

  // Données d'exemple
  const exampleData = [
    { id: 1, name: 'Ahmed Benali', department: 'IT', status: 'Actif' },
    { id: 2, name: 'Fatima El Mansouri', department: 'RH', status: 'Actif' },
    { id: 3, name: 'Youssef Alami', department: 'Finance', status: 'Inactif' },
  ];

  return (
    <ProtectedRoute requireAuth={true}>
      <AppShell headerActions={headerActions}>
        {/* Header de page avec configuration centralisée */}
        <PageHeader
          title="Exemple d'Approche Structurée"
          description="Démonstration des concepts de votre approche modulaire et typée"
        >
          <Button variant="outline" size="sm">
            <Filter className="h-4 w-4 mr-2" />
            Filtres
          </Button>
        </PageHeader>

          {/* Section de statistiques */}
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">150</div>
                <p className="text-xs text-muted-foreground">+20% par rapport au mois dernier</p>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Actifs</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">145</div>
                <p className="text-xs text-muted-foreground">96.7% du total</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Nouveaux</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">12</div>
                <p className="text-xs text-muted-foreground">Ce mois-ci</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Taux de rétention</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">94%</div>
                <p className="text-xs text-muted-foreground">+2% par rapport au trimestre dernier</p>
              </CardContent>
            </Card>
          </div>

          {/* Section principale avec tableau */}
          <Card>
            <CardHeader>
              <CardTitle>Liste d'Exemple</CardTitle>
              <CardDescription>
                Tableau démontrant la structure modulaire et la cohérence visuelle
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left p-2">Nom</th>
                      <th className="text-left p-2">Département</th>
                      <th className="text-left p-2">Statut</th>
                      <th className="text-left p-2">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {exampleData.map((item) => (
                      <tr key={item.id} className="border-b">
                        <td className="p-2 font-medium">{item.name}</td>
                        <td className="p-2 text-muted-foreground">{item.department}</td>
                        <td className="p-2">
                          <span className={`px-2 py-1 rounded-full text-xs ${
                            item.status === 'Actif' 
                              ? 'bg-green-100 text-green-800' 
                              : 'bg-red-100 text-red-800'
                          }`}>
                            {item.status}
                          </span>
                        </td>
                        <td className="p-2">
                          <Button variant="ghost" size="sm">
                            Modifier
                          </Button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>

          {/* Section d'informations sur l'approche */}
          <Card>
            <CardHeader>
              <CardTitle>Avantages de cette Approche</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <div>
                  <h4 className="font-semibold mb-2">🏗️ Architecture Modulaire</h4>
                  <ul className="text-sm text-muted-foreground space-y-1">
                    <li>• Composants réutilisables (PageHeader, AppShell)</li>
                    <li>• Configuration centralisée des pages</li>
                    <li>• Séparation claire des responsabilités</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold mb-2">🔒 TypeScript Strict</h4>
                  <ul className="text-sm text-muted-foreground space-y-1">
                    <li>• Interfaces typées pour tous les composants</li>
                    <li>• Sécurité de type à la compilation</li>
                    <li>• Meilleure expérience développeur</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold mb-2">🎨 Cohérence Visuelle</h4>
                  <ul className="text-sm text-muted-foreground space-y-1">
                    <li>• Layout standardisé sur toutes les pages</li>
                    <li>• Headers cohérents avec PageHeader</li>
                    <li>• Largeur visuelle uniforme</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold mb-2">⚡ Maintenabilité</h4>
                  <ul className="text-sm text-muted-foreground space-y-1">
                    <li>• Configuration centralisée</li>
                    <li>• Code DRY (Don't Repeat Yourself)</li>
                    <li>• Évolutivité facilitée</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
      </AppShell>
    </ProtectedRoute>
  );
}
