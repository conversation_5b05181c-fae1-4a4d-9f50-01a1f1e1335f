"use client";

import React, { useState } from 'react';
import { useAuth } from '@/context/AuthContext';
import { companyService, userService } from '@/lib/firestore';
import { DEMO_COMPANY_ID, DEMO_ADMIN_DATA } from '@/lib/demo-data';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  TestTube, 
  Loader2, 
  CheckCircle, 
  AlertCircle,
  RefreshCw,
  User,
  Building2
} from 'lucide-react';

/**
 * Auth Test Component
 * 
 * Manual testing component for authentication and demo data setup.
 * Only shown in development mode.
 */
export function AuthTest() {
  const auth = useAuth();
  const [isTestingCompany, setIsTestingCompany] = useState(false);
  const [isTestingUser, setIsTestingUser] = useState(false);
  const [testResults, setTestResults] = useState<{
    company?: { success: boolean; message: string; data?: any };
    user?: { success: boolean; message: string; data?: any };
  }>({});

  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  const testCompanyCreation = async () => {
    if (!auth.firebaseUser) {
      setTestResults(prev => ({
        ...prev,
        company: { success: false, message: 'No Firebase user authenticated' }
      }));
      return;
    }

    setIsTestingCompany(true);
    try {
      console.log('🧪 Testing company creation...');
      
      // Try to create demo company
      const company = await companyService.create({
        name: 'WePaie Demo Company',
        email: '<EMAIL>',
        phone: '+****************',
        address: '123 Demo Street, Demo City, DC 12345',
        settings: companyService.createDefaultSettings(),
        subscription: companyService.createDefaultSubscription(),
      }, DEMO_COMPANY_ID);

      setTestResults(prev => ({
        ...prev,
        company: { 
          success: true, 
          message: 'Company created successfully',
          data: { id: company.id, name: company.name }
        }
      }));

    } catch (error: any) {
      console.error('❌ Company creation test failed:', error);
      setTestResults(prev => ({
        ...prev,
        company: { 
          success: false, 
          message: `Failed: ${error.message}`,
          data: { code: error.code, details: error }
        }
      }));
    } finally {
      setIsTestingCompany(false);
    }
  };

  const testUserCreation = async () => {
    if (!auth.firebaseUser) {
      setTestResults(prev => ({
        ...prev,
        user: { success: false, message: 'No Firebase user authenticated' }
      }));
      return;
    }

    setIsTestingUser(true);
    try {
      console.log('🧪 Testing user creation...');
      
      // Try to create demo user
      const user = await userService.createUser({
        ...DEMO_ADMIN_DATA,
        name: auth.firebaseUser.displayName || DEMO_ADMIN_DATA.name,
        email: auth.firebaseUser.email || DEMO_ADMIN_DATA.email,
      }, DEMO_COMPANY_ID, auth.firebaseUser.uid);

      setTestResults(prev => ({
        ...prev,
        user: { 
          success: true, 
          message: 'User created successfully',
          data: { id: user.id, name: user.name, email: user.email, role: user.role }
        }
      }));

    } catch (error: any) {
      console.error('❌ User creation test failed:', error);
      setTestResults(prev => ({
        ...prev,
        user: { 
          success: false, 
          message: `Failed: ${error.message}`,
          data: { code: error.code, details: error }
        }
      }));
    } finally {
      setIsTestingUser(false);
    }
  };

  const refreshAuth = async () => {
    if (auth.firebaseUser) {
      await auth.refreshUserData();
    }
  };

  return (
    <Card className="mb-4 border-blue-200 bg-blue-50">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-sm">
          <TestTube className="h-4 w-4" />
          Auth Test (Development Only)
        </CardTitle>
        <CardDescription className="text-xs">
          Manual testing for authentication and demo data setup
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Current State */}
        <div className="text-xs space-y-1">
          <div><strong>Firebase User:</strong> {auth.firebaseUser?.email || 'None'}</div>
          <div><strong>WePaie User:</strong> {auth.user?.email || 'None'}</div>
          <div><strong>Company:</strong> {auth.company?.name || 'None'}</div>
        </div>

        {/* Test Buttons */}
        <div className="flex gap-2 flex-wrap">
          <Button
            size="sm"
            variant="outline"
            onClick={testCompanyCreation}
            disabled={isTestingCompany || !auth.firebaseUser}
          >
            {isTestingCompany ? (
              <Loader2 className="h-4 w-4 animate-spin mr-1" />
            ) : (
              <Building2 className="h-4 w-4 mr-1" />
            )}
            Test Company Creation
          </Button>

          <Button
            size="sm"
            variant="outline"
            onClick={testUserCreation}
            disabled={isTestingUser || !auth.firebaseUser}
          >
            {isTestingUser ? (
              <Loader2 className="h-4 w-4 animate-spin mr-1" />
            ) : (
              <User className="h-4 w-4 mr-1" />
            )}
            Test User Creation
          </Button>

          <Button
            size="sm"
            variant="outline"
            onClick={refreshAuth}
            disabled={!auth.firebaseUser}
          >
            <RefreshCw className="h-4 w-4 mr-1" />
            Refresh Auth
          </Button>
        </div>

        {/* Test Results */}
        {testResults.company && (
          <Alert variant={testResults.company.success ? "default" : "destructive"}>
            {testResults.company.success ? (
              <CheckCircle className="h-4 w-4" />
            ) : (
              <AlertCircle className="h-4 w-4" />
            )}
            <AlertDescription>
              <div className="space-y-1">
                <div><strong>Company Test:</strong> {testResults.company.message}</div>
                {testResults.company.data && (
                  <div className="text-xs bg-white/50 p-2 rounded">
                    <pre>{JSON.stringify(testResults.company.data, null, 2)}</pre>
                  </div>
                )}
              </div>
            </AlertDescription>
          </Alert>
        )}

        {testResults.user && (
          <Alert variant={testResults.user.success ? "default" : "destructive"}>
            {testResults.user.success ? (
              <CheckCircle className="h-4 w-4" />
            ) : (
              <AlertCircle className="h-4 w-4" />
            )}
            <AlertDescription>
              <div className="space-y-1">
                <div><strong>User Test:</strong> {testResults.user.message}</div>
                {testResults.user.data && (
                  <div className="text-xs bg-white/50 p-2 rounded">
                    <pre>{JSON.stringify(testResults.user.data, null, 2)}</pre>
                  </div>
                )}
              </div>
            </AlertDescription>
          </Alert>
        )}

        {/* Instructions */}
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription className="text-xs">
            <strong>Instructions:</strong> Sign <NAME_EMAIL> first, then use these buttons to test 
            individual components of the demo data setup process. Check browser console for detailed logs.
          </AlertDescription>
        </Alert>
      </CardContent>
    </Card>
  );
}
