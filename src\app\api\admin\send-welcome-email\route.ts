import { NextRequest, NextResponse } from 'next/server';
import { getAuth } from 'firebase-admin/auth';
import { getFirestore } from 'firebase-admin/firestore';
import { initializeApp, getApps, cert } from 'firebase-admin/app';

// Initialize Firebase Admin SDK
if (!getApps().length) {
  const serviceAccount = {
    type: 'service_account',
    project_id: process.env.FIREBASE_PROJECT_ID,
    private_key_id: process.env.FIREBASE_PRIVATE_KEY_ID,
    private_key: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
    client_email: process.env.FIREBASE_CLIENT_EMAIL,
    client_id: process.env.FIREBASE_CLIENT_ID,
    auth_uri: 'https://accounts.google.com/o/oauth2/auth',
    token_uri: 'https://oauth2.googleapis.com/token',
    auth_provider_x509_cert_url: 'https://www.googleapis.com/oauth2/v1/certs',
    client_x509_cert_url: `https://www.googleapis.com/robot/v1/metadata/x509/${process.env.FIREBASE_CLIENT_EMAIL}`,
  };

  initializeApp({
    credential: cert(serviceAccount as any),
    projectId: process.env.FIREBASE_PROJECT_ID,
  });
}

const auth = getAuth();
const db = getFirestore();

export interface SendWelcomeEmailRequest {
  email: string;
  firstName?: string;
  lastName?: string;
}

export async function POST(request: NextRequest) {
  try {
    // Verify the request is from an authenticated admin user
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const idToken = authHeader.split('Bearer ')[1];
    
    // Verify the ID token
    const decodedToken = await auth.verifyIdToken(idToken);

    console.log('🔍 Send Welcome Email API: Decoded token claims:', {
      uid: decodedToken.uid,
      email: decodedToken.email,
      isAdmin: decodedToken.isAdmin,
      isSuperAdmin: decodedToken.isSuperAdmin,
      role: decodedToken.role
    });

    // Check if user has admin permissions
    const hasAdminPermission = decodedToken.isAdmin === true || 
                              decodedToken.isSuperAdmin === true ||
                              decodedToken.role === 'super_admin' ||
                              decodedToken.role === 'company_admin';

    if (!hasAdminPermission) {
      console.log('❌ Send Welcome Email API: User lacks admin privileges');
      return NextResponse.json({ error: 'Insufficient permissions' }, { status: 403 });
    }

    const body: SendWelcomeEmailRequest = await request.json();
    const { email, firstName, lastName } = body;

    // Validate required fields
    if (!email) {
      return NextResponse.json({ error: 'Missing required field: email' }, { status: 400 });
    }

    console.log('📧 Send Welcome Email API: Sending welcome email to:', email);

    try {
      // Generate a password reset link that serves as the welcome email
      // This is the standard approach for Firebase - new users get a password reset email
      // to set their initial password
      const actionCodeSettings = {
        url: `${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:9002'}/login?welcome=true`,
        handleCodeInApp: false,
      };

      const resetLink = await auth.generatePasswordResetLink(email, actionCodeSettings);
      
      console.log('✅ Send Welcome Email API: Generated password reset link for welcome email');

      // In a production environment, you might want to:
      // 1. Use a custom email template service (SendGrid, Mailgun, etc.)
      // 2. Send a custom welcome email with the reset link
      // 3. Include company branding and custom messaging
      
      // For now, Firebase will send the standard password reset email
      // which serves as the welcome email for new users
      
      // Optional: Log this action for audit purposes
      console.log(`📧 Welcome email process initiated for ${email} (${firstName} ${lastName})`);

      return NextResponse.json({
        success: true,
        message: 'Welcome email sent successfully',
        email: email,
        resetLink: resetLink // In production, you might not want to return this
      });

    } catch (error: any) {
      console.error('❌ Send Welcome Email API: Error sending welcome email:', error);
      
      if (error.code === 'auth/user-not-found') {
        return NextResponse.json({ 
          error: 'Utilisateur non trouvé. L\'utilisateur doit être créé avant d\'envoyer l\'email de bienvenue.' 
        }, { status: 404 });
      }
      
      if (error.code === 'auth/invalid-email') {
        return NextResponse.json({ error: 'Adresse email invalide' }, { status: 400 });
      }

      throw error;
    }

  } catch (error) {
    console.error('❌ Send Welcome Email API: Error:', error);
    
    return NextResponse.json({ 
      error: 'Erreur lors de l\'envoi de l\'email de bienvenue',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
