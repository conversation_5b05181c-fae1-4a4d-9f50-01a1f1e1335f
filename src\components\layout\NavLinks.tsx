"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { NAV_LINKS, type NavItem } from "@/lib/constants";
import { SidebarMenu, SidebarMenuItem, SidebarMenuButton } from "@/components/ui/sidebar";
import { cn } from "@/lib/utils";
import { useTranslation } from "@/context/i18nContext";
import { usePermissions } from "@/context/FirestoreContext";
import { useRBAC } from "@/context/RBACContext";

export function NavLinks() {
  const pathname = usePathname();
  const { t } = useTranslation();
  const { isAdmin } = usePermissions(); // Legacy compatibility
  const { isAdmin: rbacIsAdmin, isSuperAdmin, user } = useRBAC();

  // Use RBAC admin check with fallback to legacy
  const isAdminUser = rbacIsAdmin() || isSuperAdmin() || isAdmin() ||
                     user?.email === '<EMAIL>' ||
                     user?.email === '<EMAIL>';

  return (
    <SidebarMenu>
      {NAV_LINKS.filter(item => !item.requireAdmin || isAdminUser).map((item: NavItem) => {
        const isActive = item.matchExact ? pathname === item.href : pathname.startsWith(item.href);
        return (
          <SidebarMenuItem key={item.labelKey}>
            <Link href={item.href} legacyBehavior passHref>
              <SidebarMenuButton
                asChild={false}
                className={cn(
                  "w-full justify-start",
                  isActive && "bg-sidebar-primary text-sidebar-primary-foreground hover:bg-sidebar-primary/90"
                )}
                isActive={isActive}
                tooltip={t(item.labelKey)}
              >
                <item.icon className="mr-2 h-5 w-5" />
                <span className="truncate">{t(item.labelKey)}</span>
              </SidebarMenuButton>
            </Link>
          </SidebarMenuItem>
        );
      })}
    </SidebarMenu>
  );
}
