"use client";

import React from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { X } from 'lucide-react';
import { EmployeeFilterState } from './EmployeeFilters';

interface ActiveFiltersDisplayProps {
  filters: EmployeeFilterState;
  onClearFilter: (key: keyof EmployeeFilterState) => void;
  className?: string;
}

// Special values for "all" options in selects
const ALL_SERVICES = '__all_services__';
const ALL_POSITIONS = '__all_positions__';

export const ActiveFiltersDisplay = React.memo(function ActiveFiltersDisplay({ filters, onClearFilter, className }: ActiveFiltersDisplayProps) {
  // Count active filters
  const activeFilterCount = Object.entries(filters).filter(([key, value]) => {
    if (key === 'service') return value !== ALL_SERVICES;
    if (key === 'position') return value !== ALL_POSITIONS;
    if (key === 'status') return value !== 'all';
    return value !== '';
  }).length;

  // Always render container to maintain layout stability
  if (activeFilterCount === 0) {
    return <div className={`min-h-[2rem] ${className || ''}`}></div>;
  }

  return (
    <div className={`flex flex-wrap items-center gap-2 min-h-[2rem] ${className || ''}`}>
      <span className="text-sm text-muted-foreground">Filtres actifs:</span>
      
      {filters.search && (
        <Badge variant="secondary" className="flex items-center gap-1 text-xs px-2 py-1">
          Recherche: {filters.search.length > 12 ? `${filters.search.substring(0, 12)}...` : filters.search}
          <Button
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              onClearFilter('search');
            }}
            className="h-3 w-3 p-0 hover:bg-transparent"
          >
            <X className="h-2.5 w-2.5" />
          </Button>
        </Badge>
      )}

      {filters.service && filters.service !== ALL_SERVICES && (
        <Badge variant="secondary" className="flex items-center gap-1 text-xs px-2 py-1">
          Service: {filters.service.length > 10 ? `${filters.service.substring(0, 10)}...` : filters.service}
          <Button
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              onClearFilter('service');
            }}
            className="h-3 w-3 p-0 hover:bg-transparent"
          >
            <X className="h-2.5 w-2.5" />
          </Button>
        </Badge>
      )}

      {filters.position && filters.position !== ALL_POSITIONS && (
        <Badge variant="secondary" className="flex items-center gap-1 text-xs px-2 py-1">
          Fonction: {filters.position.length > 10 ? `${filters.position.substring(0, 10)}...` : filters.position}
          <Button
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              onClearFilter('position');
            }}
            className="h-3 w-3 p-0 hover:bg-transparent"
          >
            <X className="h-2.5 w-2.5" />
          </Button>
        </Badge>
      )}

      {filters.status && filters.status !== 'all' && (
        <Badge variant="secondary" className="flex items-center gap-1 text-xs px-2 py-1">
          Statut: {filters.status === "active" ? "Actif" : "Inactif"}
          <Button
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              onClearFilter('status');
            }}
            className="h-3 w-3 p-0 hover:bg-transparent"
          >
            <X className="h-2.5 w-2.5" />
          </Button>
        </Badge>
      )}

      {(filters.startDateFrom || filters.startDateTo) && (
        <Badge variant="secondary" className="flex items-center gap-1 text-xs px-2 py-1">
          Dates: {filters.startDateFrom || '...'} - {filters.startDateTo || '...'}
          <Button
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              onClearFilter('startDateFrom');
              onClearFilter('startDateTo');
            }}
            className="h-3 w-3 p-0 hover:bg-transparent"
          >
            <X className="h-2.5 w-2.5" />
          </Button>
        </Badge>
      )}
    </div>
  );
});
