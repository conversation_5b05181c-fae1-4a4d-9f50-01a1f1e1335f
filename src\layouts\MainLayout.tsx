"use client";

import React from 'react';
import Head from 'next/head';
import { MainLayoutProps } from '@/types/layout';
import Header from '@/components/layout/Header';
import Sidebar from '@/components/layout/Sidebar';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import { CompanySelectionGuard } from '@/components/company/CompanySelectionGuard';
import { cn } from '@/lib/utils';

const MainLayout: React.FC<MainLayoutProps> = ({
  children,
  pageTitle,
  showCompanySelector = false,
  headerActions,
  requireAuth = true,
  requireCompany = false,
}) => {
  const content = (
    <div className="flex min-h-screen bg-background">
      <Head>
        <title>{pageTitle ? `${pageTitle} | WePaie` : 'WePaie'}</title>
        <meta name="description" content="Système de gestion de paie WePaie" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
      </Head>

      {/* Sidebar fixe */}
      <Sidebar />

      {/* Conteneur principal du contenu */}
      <div className="flex-1 ml-64 flex flex-col min-h-screen">
        {/* Header fixe */}
        <Header 
          pageTitle={pageTitle}
          showCompanySelector={showCompanySelector}
          actions={headerActions}
        />

        {/* Contenu principal */}
        <main className="flex-1 w-full">
          <div className="w-full px-2 sm:px-4 lg:px-6 py-4">
            {requireCompany ? (
              <CompanySelectionGuard
                requireCompany={true}
                fallbackMessage="Sélectionnez une entreprise pour accéder à cette page."
              >
                {children}
              </CompanySelectionGuard>
            ) : (
              children
            )}
          </div>
        </main>
      </div>
    </div>
  );

  // Envelopper avec ProtectedRoute si l'authentification est requise
  if (requireAuth) {
    return (
      <ProtectedRoute requireAuth={true}>
        {content}
      </ProtectedRoute>
    );
  }

  return content;
};

export default MainLayout;
