"use client";

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Separator } from '@/components/ui/separator';
import { useTranslation, type Locale } from '@/context/i18nContext';
import { User, Lock, Globe } from 'lucide-react';

/**
 * Profile Settings Client Component
 * 
 * Client component that handles all interactive functionality for profile settings.
 * This component uses the useTranslation hook and other client-side features.
 * 
 * Features:
 * - Personal information management
 * - Password change functionality
 * - Language preference selection
 * - Real-time language switching
 */
export function ProfileSettingsClient() {
  const { t, language, setLanguage } = useTranslation();

  return (
    <div className="space-y-6 max-w-2xl mx-auto">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">{t('profile.yourProfile', 'Your Profile')}</h1>
        <p className="text-muted-foreground">
          {t('profile.description', 'Manage your personal information and preferences.')}
        </p>
      </div>

      {/* Personal Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            {t('profile.personalInfo', 'Personal Information')}
          </CardTitle>
          <CardDescription>
            {t('profile.personalInfoDescription', 'Update your personal details and contact information.')}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <form className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="firstName">{t('firstName', 'First Name')}</Label>
                <Input id="firstName" placeholder="John" defaultValue="Demo" />
              </div>
              <div>
                <Label htmlFor="lastName">{t('lastName', 'Last Name')}</Label>
                <Input id="lastName" placeholder="Doe" defaultValue="Admin" />
              </div>
            </div>
            
            <div>
              <Label htmlFor="email">{t('emailAddress', 'Email Address')}</Label>
              <Input id="email" type="email" placeholder="<EMAIL>" defaultValue="<EMAIL>" />
            </div>
            
            <div>
              <Label htmlFor="phone">{t('phone', 'Phone Number')}</Label>
              <Input id="phone" type="tel" placeholder="+****************" />
            </div>
            
            <Button type="submit">
              <User className="mr-2 h-4 w-4" />
              {t('saveChanges', 'Save Changes')}
            </Button>
          </form>
        </CardContent>
      </Card>

      {/* Password Change */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Lock className="h-5 w-5" />
            {t('changePassword', 'Change Password')}
          </CardTitle>
          <CardDescription>
            {t('changePasswordDescription', 'Update your account password for security.')}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <form className="space-y-4">
            <div>
              <Label htmlFor="currentPassword">{t('currentPassword', 'Current Password')}</Label>
              <Input id="currentPassword" type="password" />
            </div>
            
            <div>
              <Label htmlFor="newPassword">{t('newPassword', 'New Password')}</Label>
              <Input id="newPassword" type="password" />
            </div>
            
            <div>
              <Label htmlFor="confirmNewPassword">{t('confirmNewPassword', 'Confirm New Password')}</Label>
              <Input id="confirmNewPassword" type="password" />
            </div>
            
            <Button type="submit" variant="outline">
              <Lock className="mr-2 h-4 w-4" />
              {t('updatePassword', 'Update Password')}
            </Button>
          </form>
        </CardContent>
      </Card>

      {/* Language Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Globe className="h-5 w-5" />
            {t('languageSettings.title', 'Language Settings')}
          </CardTitle>
          <CardDescription>
            {t('languageSettings.description', 'Choose your preferred language for the application.')}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <Label htmlFor="language-select">{t('language', 'Language')}</Label>
              <Select value={language} onValueChange={(value: Locale) => setLanguage(value)}>
                <SelectTrigger id="language-select" className="w-full md:w-[200px]">
                  <SelectValue placeholder={t('selectLanguage', 'Select a language')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="en">
                    <div className="flex items-center gap-2">
                      <span>🇺🇸</span>
                      <span>{t('english', 'English')}</span>
                    </div>
                  </SelectItem>
                  <SelectItem value="fr">
                    <div className="flex items-center gap-2">
                      <span>🇫🇷</span>
                      <span>{t('french', 'Français')}</span>
                    </div>
                  </SelectItem>
                  <SelectItem value="ar">
                    <div className="flex items-center gap-2">
                      <span>🇸🇦</span>
                      <span>{t('arabic', 'العربية')}</span>
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <p className="text-sm text-muted-foreground">
              {t('languageNote', 'Changes will take effect immediately and apply to all parts of the application.')}
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Preferences */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            {t('preferences', 'Preferences')}
          </CardTitle>
          <CardDescription>
            {t('preferencesDescription', 'Configure your application preferences and notifications.')}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-muted-foreground">
            <User className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>{t('preferencesComingSoon', 'Additional preference settings will be available here.')}</p>
            <p className="text-sm">
              {t('preferencesFeatures', 'Features will include notification settings, dashboard preferences, and more.')}
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
