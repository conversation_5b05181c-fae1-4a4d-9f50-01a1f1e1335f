import { useState, useEffect, useCallback } from 'react';
import { useCompany } from '@/context/CompanyContext';
import { departmentService, serviceService, positionService } from '@/lib/firestore';
import type { Department, Service, Position } from '@/lib/firestore/types';

/**
 * Hook for managing departments
 */
export function useDepartments() {
  const [departments, setDepartments] = useState<Department[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const { selectedCompany } = useCompany();
  const companyId = selectedCompany?.id;

  const fetchDepartments = useCallback(async () => {
    if (!companyId) {
      setDepartments([]);
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);
      setError(null);
      const data = await departmentService.getActiveDepartments(companyId);
      setDepartments(data);
    } catch (err) {
      setError(err as Error);
    } finally {
      setIsLoading(false);
    }
  }, [companyId]);

  useEffect(() => {
    fetchDepartments();
  }, [fetchDepartments]);

  const createDepartment = useCallback(async (name: string, description?: string) => {
    if (!companyId) throw new Error('No company ID');

    const newDepartment = await departmentService.createDepartment({
      name,
      description,
    }, companyId);

    setDepartments(prev => [...prev, newDepartment].sort((a, b) => a.order - b.order));
    return newDepartment;
  }, [companyId]);

  const updateDepartment = useCallback(async (id: string, updates: Partial<Department>) => {
    if (!companyId) throw new Error('No company ID');

    const updatedDepartment = await departmentService.update(id, updates as any, companyId);
    setDepartments(prev => prev.map(dept => dept.id === id ? updatedDepartment : dept));
    return updatedDepartment;
  }, [companyId]);

  const deactivateDepartment = useCallback(async (id: string) => {
    if (!companyId) throw new Error('No company ID');

    await departmentService.deactivateDepartment(id, companyId);
    setDepartments(prev => prev.filter(dept => dept.id !== id));
  }, [companyId]);

  return {
    departments,
    isLoading,
    error,
    refetch: fetchDepartments,
    createDepartment,
    updateDepartment,
    deactivateDepartment,
  };
}

/**
 * Hook for managing services
 */
export function useServices() {
  const [services, setServices] = useState<Service[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const { selectedCompany } = useCompany();
  const companyId = selectedCompany?.id;

  const fetchServices = useCallback(async () => {
    if (!companyId) {
      console.log('🔍 useServices: No company ID, clearing services');
      setServices([]);
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);
      setError(null);
      console.log('🔍 useServices: Fetching services for company:', companyId);
      const data = await serviceService.getActiveServices(companyId);
      console.log('🔍 useServices: Fetched services:', data.length, 'services');
      setServices(data);
    } catch (err) {
      console.error('❌ useServices: Error fetching services:', err);
      setError(err as Error);
    } finally {
      setIsLoading(false);
    }
  }, [companyId]);

  useEffect(() => {
    fetchServices();
  }, [fetchServices]);

  const createService = useCallback(async (name: string, description?: string) => {
    if (!companyId) throw new Error('No company ID');

    console.log('🔍 useServices: Creating service:', { name, description, companyId });
    const newService = await serviceService.createService({
      name,
      description,
    }, companyId);

    console.log('🔍 useServices: Created service:', newService);
    setServices(prev => {
      const updated = [...prev, newService].sort((a, b) => a.order - b.order);
      console.log('🔍 useServices: Updated services list:', updated.length, 'services');
      return updated;
    });
    return newService;
  }, [companyId]);

  const updateService = useCallback(async (id: string, updates: Partial<Service>) => {
    if (!companyId) throw new Error('No company ID');

    const updatedService = await serviceService.update(id, updates as any, companyId);
    setServices(prev => prev.map(service => service.id === id ? updatedService : service));
    return updatedService;
  }, [companyId]);

  const deactivateService = useCallback(async (id: string) => {
    if (!companyId) throw new Error('No company ID');

    await serviceService.deactivateService(id, companyId);
    setServices(prev => prev.filter(service => service.id !== id));
  }, [companyId]);

  return {
    services,
    isLoading,
    error,
    refetch: fetchServices,
    createService,
    updateService,
    deactivateService,
  };
}

/**
 * Hook for managing positions
 */
export function usePositions(serviceId?: string) {
  const [positions, setPositions] = useState<Position[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const { selectedCompany } = useCompany();
  const companyId = selectedCompany?.id;

  const fetchPositions = useCallback(async () => {
    if (!companyId) {
      console.log('🔍 usePositions: No company ID, clearing positions');
      setPositions([]);
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);
      setError(null);
      let data: Position[];

      console.log('🔍 usePositions: Fetching positions for company:', companyId, 'serviceId:', serviceId);
      if (serviceId) {
        data = await positionService.getPositionsByService(companyId, serviceId);
      } else {
        data = await positionService.getActivePositions(companyId);
      }

      console.log('🔍 usePositions: Fetched positions:', data.length, 'positions');
      setPositions(data);
    } catch (err) {
      console.error('❌ usePositions: Error fetching positions:', err);
      setError(err as Error);
    } finally {
      setIsLoading(false);
    }
  }, [companyId, serviceId]);

  useEffect(() => {
    fetchPositions();
  }, [fetchPositions]);

  const createPosition = useCallback(async (
    name: string,
    serviceId?: string,
    description?: string
  ) => {
    if (!companyId) throw new Error('No company ID');

    console.log('🔍 usePositions: Creating position:', { name, serviceId, description, companyId });
    const newPosition = await positionService.createPosition({
      name,
      serviceId,
      description,
    }, companyId);

    console.log('🔍 usePositions: Created position:', newPosition);
    setPositions(prev => {
      const updated = [...prev, newPosition].sort((a, b) => a.order - b.order);
      console.log('🔍 usePositions: Updated positions list:', updated.length, 'positions');
      return updated;
    });
    return newPosition;
  }, [companyId]);

  const updatePosition = useCallback(async (id: string, updates: Partial<Position>) => {
    if (!companyId) throw new Error('No company ID');

    const updatedPosition = await positionService.update(id, updates as any, companyId);
    setPositions(prev => prev.map(position => position.id === id ? updatedPosition : position));
    return updatedPosition;
  }, [companyId]);

  const deactivatePosition = useCallback(async (id: string) => {
    if (!companyId) throw new Error('No company ID');

    await positionService.deactivatePosition(id, companyId);
    setPositions(prev => prev.filter(position => position.id !== id));
  }, [companyId]);

  return {
    positions,
    isLoading,
    error,
    refetch: fetchPositions,
    createPosition,
    updatePosition,
    deactivatePosition,
  };
}

/**
 * Hook for getting all organizational data
 */
export function useOrganizationalData() {
  const { departments, isLoading: departmentsLoading, error: departmentsError } = useDepartments();
  const { services, isLoading: servicesLoading, error: servicesError } = useServices();
  const { positions, isLoading: positionsLoading, error: positionsError } = usePositions();

  const isLoading = departmentsLoading || servicesLoading || positionsLoading;
  const error = departmentsError || servicesError || positionsError;

  return {
    departments,
    services,
    positions,
    isLoading,
    error,
  };
}
