"use client";

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { LogOut, User, Settings, Loader2 } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useAuth } from "@/context/AuthContext";
import { useRBAC } from "@/context/RBACContext";
import { useCompany } from "@/context/CompanyContext";
import { useToast } from "@/hooks/use-toast";
import { useTranslation } from "@/context/i18nContext";

export function UserNav() {
  const { t } = useTranslation();
  const router = useRouter();
  const { firebaseUser, signOut } = useAuth();
  const { user: rbacUser, isLoading: rbacLoading, isInitialized: rbacInitialized, isSuperAdmin } = useRBAC();
  const { selectedCompany } = useCompany();
  const { toast } = useToast();

  // Show login button if not authenticated
  if (!firebaseUser) {
    return (
      <Link href="/login">
        <Button variant="outline">Sign In</Button>
      </Link>
    );
  }

  // Show loading state while user data is loading
  if (!rbacInitialized || rbacLoading || !rbacUser) {
    return (
      <div className="flex items-center space-x-2">
        <div className="h-8 w-8 bg-muted animate-pulse rounded-full" />
        <div className="h-4 w-16 bg-muted animate-pulse rounded" />
      </div>
    );
  }

  const getInitials = (name: string) => {
    const names = name.split(' ');
    let initials = names[0].substring(0, 1).toUpperCase();
    if (names.length > 1) {
      initials += names[names.length - 1].substring(0, 1).toUpperCase();
    }
    return initials;
  };

  const handleSignOut = async () => {
    try {
      console.log('🔐 UserNav: Starting logout process');
      await signOut();
      toast({
        title: "Déconnecté",
        description: "Vous avez été déconnecté avec succès.",
      });
      router.push('/login');
    } catch (error: any) {
      console.error('🔐 UserNav: Logout error:', error);
      toast({
        title: "Erreur de déconnexion",
        description: error.message || "Une erreur s'est produite lors de la déconnexion",
        variant: "destructive",
      });
    }
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="relative h-8 w-8 rounded-full p-0">
          <Avatar className="h-8 w-8">
            <AvatarImage src={rbacUser.profilePicture || ""} alt={`${rbacUser.firstName} ${rbacUser.lastName}`} />
            <AvatarFallback className="bg-primary text-primary-foreground text-sm font-semibold">
              {rbacUser.firstName.charAt(0).toUpperCase()}
            </AvatarFallback>
          </Avatar>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-56" align="end" forceMount>
        <DropdownMenuLabel className="font-normal">
          <div className="flex flex-col space-y-1">
            <p className="text-sm font-medium leading-none">{rbacUser.firstName} {rbacUser.lastName}</p>
            <p className="text-xs leading-none text-muted-foreground">
              {rbacUser.email}
            </p>
            {!isSuperAdmin() && selectedCompany && (
              <p className="text-xs leading-none text-muted-foreground">
                {selectedCompany.name}
              </p>
            )}
            <p className="text-xs leading-none text-muted-foreground">
              {rbacUser.role === 'super_admin' ? 'Super Administrateur' :
               rbacUser.role === 'company_admin' ? 'Administrateur' :
               rbacUser.role === 'editor' ? 'Éditeur' : 'Observateur'}
            </p>
          </div>
        </DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuGroup>
          <Link href="/settings">
            <DropdownMenuItem>
              <Settings className="mr-2 h-4 w-4" />
              <span>{t('settings')}</span>
            </DropdownMenuItem>
          </Link>
        </DropdownMenuGroup>
        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={handleSignOut}>
          <LogOut className="mr-2 h-4 w-4" />
          <span>Se déconnecter</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
