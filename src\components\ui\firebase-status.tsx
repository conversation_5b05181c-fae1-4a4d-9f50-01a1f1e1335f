"use client";

import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Database, TestTube } from 'lucide-react';

export function FirebaseStatus() {
  const isEmulator = process.env.NEXT_PUBLIC_USE_FIREBASE_EMULATOR === 'true';
  const projectId = process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID;

  if (isEmulator) {
    return (
      <Badge variant="secondary" className="gap-1">
        <TestTube className="h-3 w-3" />
        Emulator Mode
      </Badge>
    );
  }

  return (
    <Badge variant="default" className="gap-1">
      <Database className="h-3 w-3" />
      Production ({projectId})
    </Badge>
  );
}
