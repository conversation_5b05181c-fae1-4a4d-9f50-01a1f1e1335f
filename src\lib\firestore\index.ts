// Export all Firestore services and types
export * from './types';
export * from './base';
export * from './companies';
export * from './employees';
export * from './users';
export * from './absences';
export * from './overtime';
export * from './organizational';

// Export service instances for easy access
export { companyService } from './companies';
export { employeeService } from './employees';
export { userService } from './users';
export { absenceService } from './absences';
export { overtimeService } from './overtime';
export { departmentService, serviceService, positionService } from './organizational';

// Utility functions for common operations
import { companyService } from './companies';
import { employeeService } from './employees';
import { userService } from './users';
import { absenceService } from './absences';
import { overtimeService } from './overtime';

/**
 * Initialize a new company with default settings
 */
export async function initializeCompany(companyData: {
  name: string;
  email: string;
  phone?: string;
  address?: string;
}) {
  const company = await companyService.create({
    ...companyData,
    settings: companyService.createDefaultSettings(),
    subscription: companyService.createDefaultSubscription(),
  });

  return company;
}

/**
 * Create admin user for a company
 */
export async function createAdminUser(
  companyId: string,
  userData: {
    firstName: string;
    lastName: string;
    email: string;
    name: string;
  }
) {
  return userService.createUser({
    ...userData,
    role: 'admin',
  }, companyId);
}

/**
 * Get dashboard statistics for a company
 */
export async function getDashboardStats(companyId: string) {
  const [
    employeeStats,
    userStats,
    absenceStats,
    overtimeStats,
  ] = await Promise.all([
    employeeService.getEmployeeStats(companyId),
    userService.getUserStats(companyId),
    absenceService.getAbsenceStats(companyId),
    overtimeService.getOvertimeStats(companyId),
  ]);

  return {
    employees: employeeStats,
    users: userStats,
    absences: absenceStats,
    overtime: overtimeStats,
  };
}

/**
 * Search across all entities
 */
export async function globalSearch(companyId: string, searchTerm: string) {
  const [employees, users] = await Promise.all([
    employeeService.searchEmployees(companyId, searchTerm),
    userService.getAll(companyId).then(users => 
      users.filter(user => 
        user.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.email.toLowerCase().includes(searchTerm.toLowerCase())
      )
    ),
  ]);

  return {
    employees: employeeService.toEmployees(employees),
    users: userService.toUsers(users),
  };
}

/**
 * Validate company access for operations
 */
export async function validateCompanyAccess(companyId: string, userId: string): Promise<boolean> {
  try {
    const user = await userService.getById(userId, companyId);
    return user !== null && user.isActive;
  } catch (error) {
    return false;
  }
}

/**
 * Get recent activity across all entities
 */
export async function getRecentActivity(companyId: string, limit: number = 10) {
  const [employees, absences, overtime] = await Promise.all([
    employeeService.getAll(companyId, { 
      orderBy: { field: 'updatedAt', direction: 'desc' }, 
      limit: Math.ceil(limit / 3) 
    }),
    absenceService.getAll(companyId, { 
      orderBy: { field: 'updatedAt', direction: 'desc' }, 
      limit: Math.ceil(limit / 3) 
    }),
    overtimeService.getAll(companyId, { 
      orderBy: { field: 'updatedAt', direction: 'desc' }, 
      limit: Math.ceil(limit / 3) 
    }),
  ]);

  // Combine and sort by updatedAt
  const activities = [
    ...employees.map(e => ({ type: 'employee', data: e, updatedAt: e.updatedAt })),
    ...absences.map(a => ({ type: 'absence', data: a, updatedAt: a.updatedAt })),
    ...overtime.map(o => ({ type: 'overtime', data: o, updatedAt: o.updatedAt })),
  ].sort((a, b) => b.updatedAt.getTime() - a.updatedAt.getTime()).slice(0, limit);

  return activities;
}
