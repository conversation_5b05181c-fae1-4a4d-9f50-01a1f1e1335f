"use client";

import React from 'react';
import Link from 'next/link';
import { useRouter } from 'next/router';
import { 
  LayoutDashboard, 
  Users, 
  Clock, 
  Settings, 
  Shield 
} from 'lucide-react';
import { NavItem, SidebarProps } from '@/types/layout';
import { useRBAC } from '@/context/RBACContext';
import { useTranslation } from '@/context/i18nContext';
import { cn } from '@/lib/utils';
import { CompanyLogo } from '@/components/company/CompanyLogo';

const Sidebar: React.FC<SidebarProps> = ({ className }) => {
  const router = useRouter();
  const { hasPermission, isSuperAdmin, isAdmin } = useRBAC();
  const { t } = useTranslation();

  // Configuration des éléments de navigation avec permissions
  const navItems: NavItem[] = [
    {
      name: t('navigation.dashboard', 'Tableau de bord'),
      href: '/',
      icon: LayoutDashboard,
    },
    {
      name: t('navigation.employees', 'Employés'),
      href: '/employees',
      icon: Users,
      permission: 'employees:read',
    },
    {
      name: t('navigation.timeManagement', 'Gestion du temps'),
      href: '/time-management',
      icon: Clock,
      permission: 'time:read',
    },
    {
      name: t('navigation.admin', 'Administration'),
      href: '/admin',
      icon: Shield,
      permission: 'admin:read',
    },
    {
      name: t('navigation.settings', 'Paramètres'),
      href: '/settings',
      icon: Settings,
    },
  ];

  // Filtrer les éléments de navigation selon les permissions
  const visibleNavItems = navItems.filter(item => {
    if (!item.permission) return true;
    
    // Super admin peut tout voir
    if (isSuperAdmin()) return true;
    
    // Vérifier les permissions spécifiques
    return hasPermission(item.permission);
  });

  return (
    <aside className={cn(
      "fixed top-0 left-0 z-40 h-screen w-64 bg-background border-r border-border transition-transform",
      className
    )}>
      <div className="flex h-full flex-col">
        {/* Logo de l'entreprise */}
        <div className="flex h-14 items-center border-b border-border px-4">
          <CompanyLogo className="h-8 w-auto" />
        </div>

        {/* Navigation principale */}
        <nav className="flex-1 space-y-1 p-4">
          {visibleNavItems.map((item) => {
            const isActive = router.pathname === item.href;
            const Icon = item.icon;

            return (
              <Link
                key={item.name}
                href={item.href}
                className={cn(
                  "flex items-center gap-3 rounded-lg px-3 py-2 text-sm font-medium transition-colors",
                  isActive
                    ? "bg-primary text-primary-foreground"
                    : "text-muted-foreground hover:bg-accent hover:text-accent-foreground"
                )}
              >
                {Icon && <Icon className="h-4 w-4" />}
                {item.name}
                {item.badge && (
                  <span className="ml-auto rounded-full bg-primary px-2 py-1 text-xs text-primary-foreground">
                    {item.badge}
                  </span>
                )}
              </Link>
            );
          })}
        </nav>

        {/* Pied de page de la sidebar */}
        <div className="border-t border-border p-4">
          <div className="flex items-center justify-center text-xs text-muted-foreground">
            <span>WePaie v1.0</span>
          </div>
        </div>
      </div>
    </aside>
  );
};

export default Sidebar;
