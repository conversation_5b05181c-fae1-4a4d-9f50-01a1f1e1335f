"use client";

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/context/AuthContext';
import { LoadingScreen } from '@/components/ui/loading-screen';
import { DEFAULT_REDIRECT, AUTH_REDIRECT } from '@/lib/constants';

export default function HomePage() {
  const router = useRouter();
  const { firebaseUser, isInitialized } = useAuth();

  useEffect(() => {
    if (!isInitialized) return;

    if (firebaseUser) {
      // User is authenticated, redirect to dashboard
      router.push(DEFAULT_REDIRECT);
    } else {
      // User is not authenticated, redirect to login
      router.push(AUTH_REDIRECT);
    }
  }, [firebaseUser, isInitialized, router]);

  // Show loading while determining authentication state
  if (!isInitialized) {
    return (
      <LoadingScreen
        message="Initialisation..."
        submessage="Vérification de votre statut de connexion"
      />
    );
  }

  // Show loading while redirecting
  return (
    <LoadingScreen
      message="Redirection..."
      submessage={firebaseUser ? "Redirection vers le tableau de bord" : "Redirection vers la connexion"}
    />
  );
}
