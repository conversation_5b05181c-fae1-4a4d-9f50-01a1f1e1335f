/**
 * Company Management Service for RBAC
 * 
 * Handles company creation, updates, and multi-tenant data management
 * with proper Firebase Firestore integration.
 */

import { 
  collection, 
  doc, 
  setDoc, 
  updateDoc, 
  deleteDoc, 
  getDocs, 
  getDoc,
  query, 
  where,
  orderBy,
  Timestamp
} from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { createUser } from './userService';
import type { 
  Company, 
  CreateCompanyRequest, 
  UpdateCompanyRequest,
  RBACUser
} from './types';

/**
 * Create a new company with initial administrator
 */
export async function createCompany(request: CreateCompanyRequest): Promise<{ company: Company; admin: RBACUser }> {
  try {
    // Generate company ID
    const companyRef = doc(collection(db, 'companies'));
    const companyId = companyRef.id;
    
    // Create company document
    const companyData: Company = {
      id: companyId,
      name: request.name,
      email: request.email,
      phone: request.phone,
      address: request.address,
      industry: request.industry,
      isActive: true,
      maxUsers: request.maxUsers || 50,
      subscriptionStatus: 'trial',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    
    await setDoc(companyRef, {
      ...companyData,
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now()
    });
    
    // Create company administrator
    const adminUser = await createUser({
      email: request.adminEmail,
      firstName: request.adminFirstName,
      lastName: request.adminLastName,
      role: 'company_admin',
      companyId: companyId,
      sendWelcomeEmail: true
    });
    
    return { company: companyData, admin: adminUser };
  } catch (error) {
    console.error('Error creating company:', error);
    throw new Error(`Failed to create company: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Update company information
 */
export async function updateCompany(companyId: string, updates: UpdateCompanyRequest): Promise<void> {
  try {
    const companyRef = doc(db, 'companies', companyId);
    
    const updateData: any = {
      ...updates,
      updatedAt: Timestamp.now()
    };
    
    await updateDoc(companyRef, updateData);
  } catch (error) {
    console.error('Error updating company:', error);
    throw new Error(`Failed to update company: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Delete company (deactivate)
 */
export async function deleteCompany(companyId: string): Promise<void> {
  try {
    const companyRef = doc(db, 'companies', companyId);
    
    // Soft delete by deactivating
    await updateDoc(companyRef, {
      isActive: false,
      updatedAt: Timestamp.now()
    });
    
    // TODO: Also deactivate all users in the company
  } catch (error) {
    console.error('Error deleting company:', error);
    throw new Error(`Failed to delete company: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Get company by ID
 */
export async function getCompanyById(companyId: string): Promise<Company | null> {
  try {
    const companyRef = doc(db, 'companies', companyId);
    const companySnap = await getDoc(companyRef);
    
    if (!companySnap.exists()) {
      return null;
    }
    
    const data = companySnap.data();
    return {
      id: companySnap.id,
      ...data,
      createdAt: data.createdAt?.toDate?.()?.toISOString() || data.createdAt,
      updatedAt: data.updatedAt?.toDate?.()?.toISOString() || data.updatedAt
    } as Company;
  } catch (error) {
    console.error('Error getting company:', error);
    return null;
  }
}

/**
 * Get all companies (super admin only)
 */
export async function getAllCompanies(): Promise<Company[]> {
  try {
    const companiesRef = collection(db, 'companies');
    const q = query(companiesRef, orderBy('createdAt', 'desc'));
    const querySnapshot = await getDocs(q);
    
    return querySnapshot.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        ...data,
        createdAt: data.createdAt?.toDate?.()?.toISOString() || data.createdAt,
        updatedAt: data.updatedAt?.toDate?.()?.toISOString() || data.updatedAt
      } as Company;
    });
  } catch (error) {
    console.error('Error getting companies:', error);
    return [];
  }
}

/**
 * Get active companies
 */
export async function getActiveCompanies(): Promise<Company[]> {
  try {
    const companiesRef = collection(db, 'companies');
    const q = query(
      companiesRef, 
      where('isActive', '==', true),
      orderBy('createdAt', 'desc')
    );
    const querySnapshot = await getDocs(q);
    
    return querySnapshot.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        ...data,
        createdAt: data.createdAt?.toDate?.()?.toISOString() || data.createdAt,
        updatedAt: data.updatedAt?.toDate?.()?.toISOString() || data.updatedAt
      } as Company;
    });
  } catch (error) {
    console.error('Error getting active companies:', error);
    return [];
  }
}

/**
 * Search companies by name or email
 */
export async function searchCompanies(searchTerm: string): Promise<Company[]> {
  try {
    const companies = await getAllCompanies();
    const term = searchTerm.toLowerCase();
    
    return companies.filter(company => 
      company.name.toLowerCase().includes(term) ||
      company.email.toLowerCase().includes(term)
    );
  } catch (error) {
    console.error('Error searching companies:', error);
    return [];
  }
}

/**
 * Activate/deactivate company
 */
export async function toggleCompanyStatus(companyId: string, isActive: boolean): Promise<void> {
  try {
    const companyRef = doc(db, 'companies', companyId);
    await updateDoc(companyRef, {
      isActive,
      updatedAt: Timestamp.now()
    });
  } catch (error) {
    console.error('Error toggling company status:', error);
    throw new Error(`Failed to update company status: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Get company statistics
 */
export async function getCompanyStats(): Promise<{
  total: number;
  active: number;
  inactive: number;
  bySubscription: Record<string, number>;
}> {
  try {
    const companies = await getAllCompanies();
    
    const stats = {
      total: companies.length,
      active: companies.filter(c => c.isActive).length,
      inactive: companies.filter(c => !c.isActive).length,
      bySubscription: {} as Record<string, number>
    };
    
    companies.forEach(company => {
      const status = company.subscriptionStatus || 'unknown';
      stats.bySubscription[status] = (stats.bySubscription[status] || 0) + 1;
    });
    
    return stats;
  } catch (error) {
    console.error('Error getting company stats:', error);
    return {
      total: 0,
      active: 0,
      inactive: 0,
      bySubscription: {}
    };
  }
}

/**
 * Get company user count
 */
export async function getCompanyUserCount(companyId: string): Promise<number> {
  try {
    const usersRef = collection(db, `companies/${companyId}/users`);
    const q = query(usersRef, where('isActive', '==', true));
    const querySnapshot = await getDocs(q);
    
    return querySnapshot.size;
  } catch (error) {
    console.error('Error getting company user count:', error);
    return 0;
  }
}

/**
 * Check if company can add more users
 */
export async function canAddUser(companyId: string): Promise<boolean> {
  try {
    const company = await getCompanyById(companyId);
    if (!company || !company.isActive) {
      return false;
    }
    
    const userCount = await getCompanyUserCount(companyId);
    const maxUsers = company.maxUsers || 50;
    
    return userCount < maxUsers;
  } catch (error) {
    console.error('Error checking if company can add user:', error);
    return false;
  }
}

/**
 * Update company subscription status
 */
export async function updateSubscriptionStatus(
  companyId: string, 
  status: 'active' | 'inactive' | 'trial' | 'suspended'
): Promise<void> {
  try {
    const companyRef = doc(db, 'companies', companyId);
    await updateDoc(companyRef, {
      subscriptionStatus: status,
      updatedAt: Timestamp.now()
    });
  } catch (error) {
    console.error('Error updating subscription status:', error);
    throw new Error(`Failed to update subscription status: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Get companies by subscription status
 */
export async function getCompaniesBySubscription(
  status: 'active' | 'inactive' | 'trial' | 'suspended'
): Promise<Company[]> {
  try {
    const companiesRef = collection(db, 'companies');
    const q = query(
      companiesRef, 
      where('subscriptionStatus', '==', status),
      orderBy('createdAt', 'desc')
    );
    const querySnapshot = await getDocs(q);
    
    return querySnapshot.docs.map(doc => {
      const data = doc.data();
      return {
        id: doc.id,
        ...data,
        createdAt: data.createdAt?.toDate?.()?.toISOString() || data.createdAt,
        updatedAt: data.updatedAt?.toDate?.()?.toISOString() || data.updatedAt
      } as Company;
    });
  } catch (error) {
    console.error('Error getting companies by subscription:', error);
    return [];
  }
}

/**
 * Initialize demo company for development
 */
export async function initializeDemoCompany(): Promise<{ company: Company; admin: RBACUser }> {
  try {
    // Check if demo company already exists
    const existingCompany = await getCompanyById('demo-company');
    if (existingCompany) {
      throw new Error('Demo company already exists');
    }
    
    return await createCompany({
      name: 'WePaie Demo Company',
      email: '<EMAIL>',
      phone: '+212 5XX XX XX XX',
      address: 'Casablanca, Morocco',
      industry: 'Technology',
      adminEmail: '<EMAIL>',
      adminFirstName: 'Admin',
      adminLastName: 'WePaie',
      maxUsers: 100
    });
  } catch (error) {
    console.error('Error initializing demo company:', error);
    throw new Error(`Failed to initialize demo company: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}
