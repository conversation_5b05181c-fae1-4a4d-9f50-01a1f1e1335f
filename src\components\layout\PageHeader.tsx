"use client";

import React from 'react';
import { useTranslation } from '@/context/i18nContext';
import { cn } from '@/lib/utils';

interface PageHeaderProps {
  title?: string;
  description?: string;
  children?: React.ReactNode;
  className?: string;
  showBorder?: boolean;
}

/**
 * Composant PageHeader - Inspiré de votre approche structurée
 * Fournit un header de page cohérent avec titre, description et actions
 * Établit la largeur visuelle de référence pour toutes les pages
 */
export const PageHeader: React.FC<PageHeaderProps> = ({
  title,
  description,
  children,
  className,
  showBorder = true,
}) => {
  const { t } = useTranslation();

  return (
    <div className={cn(
      "w-full",
      showBorder && "border-b border-border pb-4",
      className
    )}>
      <div className="flex items-start justify-between">
        <div className="space-y-1">
          {title && (
            <h1 className="text-2xl font-bold tracking-tight">
              {title.includes('.') ? t(title, title) : title}
            </h1>
          )}
          {description && (
            <p className="text-muted-foreground">
              {description.includes('.') ? t(description, description) : description}
            </p>
          )}
        </div>
        
        {children && (
          <div className="flex items-center space-x-2">
            {children}
          </div>
        )}
      </div>
    </div>
  );
};

export default PageHeader;
